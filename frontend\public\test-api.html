<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
    </style>
</head>
<body>
    <h1>API测试页面</h1>
    
    <div class="test-section">
        <h2>测试1：查询类请求（应该被拒绝）</h2>
        <button onclick="testQuery()">测试：查看系统中有哪些模板</button>
        <div id="result1" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试2：文本生成请求（应该继续处理）</h2>
        <button onclick="testGeneration()">测试：我需要生成一份会议纪要报告</button>
        <div id="result2" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试3：数据分析请求（应该被拒绝）</h2>
        <button onclick="testAnalysis()">测试：帮我分析一下数据</button>
        <div id="result3" class="result"></div>
    </div>

    <script>
        async function callAPI(text, resultId) {
            const resultDiv = document.getElementById(resultId);
            resultDiv.textContent = '正在调用API...';
            resultDiv.className = 'result';
            
            try {
                const formData = new FormData();
                formData.append('text_input', text);
                
                const response = await fetch('http://localhost:8000/api/workflow/langgraph/start', {
                    method: 'POST',
                    body: formData,
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const result = await response.json();
                
                console.log('API响应:', result);
                
                // 解析响应
                let message = '';
                if (result.success) {
                    const finalState = result.data?.final_state;
                    if (finalState) {
                        const lastNodeKey = Object.keys(finalState).pop();
                        const lastState = lastNodeKey ? finalState[lastNodeKey] : null;
                        
                        if (lastState && lastState.intention_result?.user_response) {
                            message = lastState.intention_result.user_response;
                        } else {
                            message = '处理完成，但未获取到响应内容。';
                        }
                    } else {
                        message = '未获取到处理状态。';
                    }
                } else {
                    message = '处理请求时出现问题。';
                }
                
                resultDiv.textContent = `成功！智能体响应：\n${message}`;
                resultDiv.className = 'result success';
                
            } catch (error) {
                console.error('API调用失败:', error);
                resultDiv.textContent = `错误：${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        function testQuery() {
            callAPI('查看系统中有哪些模板', 'result1');
        }
        
        function testGeneration() {
            callAPI('我需要生成一份会议纪要报告', 'result2');
        }
        
        function testAnalysis() {
            callAPI('帮我分析一下数据', 'result3');
        }
    </script>
</body>
</html>
