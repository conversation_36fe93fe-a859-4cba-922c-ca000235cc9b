# 超限报告智能生成工具 - 数据模型设计文档

**版本**: V1.0  
**创建日期**: 2025年6月26日  
**设计者**: Augment Agent  
**文档类型**: 数据模型设计文档  

---

## 📋 1. 业务流程分析

### 1.1 核心业务流程

```
用户登录 → 选择报告类型 → 上传碎片化信息 → AI提取关键信息 → 用户确认修改 → 生成报告 → 下载归档
```

### 1.2 关键业务实体识别

1. **用户 (User)** - 系统使用者
2. **模板 (Template)** - 报告模板，包含占位符结构
3. **模板字段 (TemplateField)** - 模板中的具体字段定义
4. **数据源 (DataSource)** - 用户上传的原始文档
5. **抽取数据 (ExtractedData)** - 从数据源中提取的结构化信息
6. **报告 (Report)** - 生成的最终报告
7. **生成任务 (GenerationTask)** - 报告生成的异步任务
8. **审核记录 (ReviewRecord)** - 用户对抽取数据的确认和修改记录

### 1.3 业务规则

- 一个模板可以有多个字段，字段有顺序
- 一个数据源可以抽取出多条数据，对应不同字段
- 一个报告基于一个模板和一个或多个数据源生成
- 抽取的数据需要用户确认后才能用于报告生成
- 支持字段的重复性（如多个参与人员）

---

## 🎯 2. 概念数据模型 (ER图)

### 2.1 核心实体关系

```
User (用户)
├── 1:N → Template (创建模板)
├── 1:N → DataSource (上传数据源)
├── 1:N → Report (生成报告)
└── 1:N → ReviewRecord (审核记录)

Template (模板)
├── 1:N → TemplateField (包含字段)
├── 1:N → ExtractedData (抽取数据关联)
└── 1:N → Report (生成报告)

DataSource (数据源)
├── 1:N → ExtractedData (抽取数据)
└── 1:N → Report (生成报告)

Report (报告)
├── N:1 → Template (基于模板)
├── N:1 → DataSource (基于数据源)
├── 1:1 → GenerationTask (生成任务)
└── 1:N → ReviewRecord (审核记录)
```

### 2.2 实体属性概览

- **User**: id, username, email, role, created_at
- **Template**: id, name, description, file_path, schema_json, created_by
- **TemplateField**: id, template_id, field_name, field_type, is_required, is_repeatable
- **DataSource**: id, name, file_path, file_type, status, created_by
- **ExtractedData**: id, data_source_id, template_id, field_name, field_value, confidence
- **Report**: id, name, template_id, data_source_id, file_path, status, created_by
- **GenerationTask**: id, report_id, status, progress, error_message
- **ReviewRecord**: id, user_id, extracted_data_id, action, old_value, new_value

---

## 📊 3. 逻辑数据模型设计

### 3.1 用户管理模块

#### 3.1.1 users (用户表)
| 字段名 | 类型 | 长度 | 约束 | 说明 |
|--------|------|------|------|------|
| id | UUID | - | PK | 用户唯一标识 |
| username | VARCHAR | 50 | UNIQUE, NOT NULL | 用户名 |
| email | VARCHAR | 100 | UNIQUE | 邮箱地址 |
| password_hash | VARCHAR | 255 | NOT NULL | 密码哈希 |
| full_name | VARCHAR | 100 | - | 真实姓名 |
| department | VARCHAR | 100 | - | 部门 |
| role | VARCHAR | 20 | DEFAULT 'user' | 角色(admin/user) |
| is_active | BOOLEAN | - | DEFAULT true | 是否激活 |
| last_login_at | TIMESTAMP | - | - | 最后登录时间 |
| created_at | TIMESTAMP | - | NOT NULL | 创建时间 |
| updated_at | TIMESTAMP | - | NOT NULL | 更新时间 |

### 3.2 模板管理模块

#### 3.2.1 templates (模板表)
| 字段名 | 类型 | 长度 | 约束 | 说明 |
|--------|------|------|------|------|
| id | UUID | - | PK | 模板唯一标识 |
| name | VARCHAR | 255 | NOT NULL | 模板名称 |
| description | TEXT | - | - | 模板描述 |
| category | VARCHAR | 50 | - | 模板分类 |
| file_path | VARCHAR | 500 | NOT NULL | 模板文件路径 |
| file_size | INTEGER | - | - | 文件大小(字节) |
| mime_type | VARCHAR | 100 | - | MIME类型 |
| schema_json | JSONB | - | - | 字段结构JSON |
| placeholder_count | INTEGER | - | DEFAULT 0 | 占位符数量 |
| version | VARCHAR | 20 | - | 模板版本 |
| is_active | BOOLEAN | - | DEFAULT true | 是否激活 |
| created_by | UUID | - | FK(users.id) | 创建者 |
| created_at | TIMESTAMP | - | NOT NULL | 创建时间 |
| updated_at | TIMESTAMP | - | NOT NULL | 更新时间 |

#### 3.2.2 template_fields (模板字段表)
| 字段名 | 类型 | 长度 | 约束 | 说明 |
|--------|------|------|------|------|
| id | UUID | - | PK | 字段唯一标识 |
| template_id | UUID | - | FK(templates.id) | 所属模板 |
| field_name | VARCHAR | 255 | NOT NULL | 字段名称 |
| field_label | VARCHAR | 255 | - | 字段显示名 |
| field_type | VARCHAR | 50 | NOT NULL | 字段类型 |
| data_type | VARCHAR | 20 | DEFAULT 'text' | 数据类型 |
| is_required | BOOLEAN | - | DEFAULT false | 是否必填 |
| is_repeatable | BOOLEAN | - | DEFAULT false | 是否可重复 |
| default_value | TEXT | - | - | 默认值 |
| validation_rules | JSONB | - | - | 验证规则 |
| description | TEXT | - | - | 字段描述 |
| field_order | INTEGER | - | DEFAULT 0 | 字段顺序 |
| created_at | TIMESTAMP | - | NOT NULL | 创建时间 |
| updated_at | TIMESTAMP | - | NOT NULL | 更新时间 |

### 3.3 数据源管理模块

#### 3.3.1 data_sources (数据源表)
| 字段名 | 类型 | 长度 | 约束 | 说明 |
|--------|------|------|------|------|
| id | UUID | - | PK | 数据源唯一标识 |
| name | VARCHAR | 255 | NOT NULL | 数据源名称 |
| description | TEXT | - | - | 数据源描述 |
| file_path | VARCHAR | 500 | NOT NULL | 文件路径 |
| file_size | INTEGER | - | - | 文件大小(字节) |
| mime_type | VARCHAR | 100 | - | MIME类型 |
| file_hash | VARCHAR | 64 | - | 文件哈希值 |
| source_type | VARCHAR | 50 | DEFAULT 'document' | 数据源类型 |
| status | VARCHAR | 50 | DEFAULT 'uploaded' | 处理状态 |
| extraction_count | INTEGER | - | DEFAULT 0 | 抽取数据条数 |
| metadata | JSONB | - | - | 文件元数据 |
| created_by | UUID | - | FK(users.id) | 创建者 |
| created_at | TIMESTAMP | - | NOT NULL | 创建时间 |
| updated_at | TIMESTAMP | - | NOT NULL | 更新时间 |

#### 3.3.2 extracted_data (抽取数据表)
| 字段名 | 类型 | 长度 | 约束 | 说明 |
|--------|------|------|------|------|
| id | UUID | - | PK | 抽取数据唯一标识 |
| data_source_id | UUID | - | FK(data_sources.id) | 数据源ID |
| template_id | UUID | - | FK(templates.id) | 关联模板ID |
| field_name | VARCHAR | 255 | NOT NULL | 字段名称 |
| field_value | TEXT | - | - | 字段值 |
| original_text | TEXT | - | - | 原始文本 |
| confidence_score | DECIMAL | 3,2 | - | 置信度分数 |
| extraction_method | VARCHAR | 50 | DEFAULT 'ai' | 抽取方法 |
| is_verified | BOOLEAN | - | DEFAULT false | 是否已验证 |
| verification_status | VARCHAR | 20 | DEFAULT 'pending' | 验证状态 |
| position_info | JSONB | - | - | 位置信息 |
| created_at | TIMESTAMP | - | NOT NULL | 创建时间 |
| updated_at | TIMESTAMP | - | NOT NULL | 更新时间 |

### 3.4 报告生成模块

#### 3.4.1 reports (报告表)
| 字段名 | 类型 | 长度 | 约束 | 说明 |
|--------|------|------|------|------|
| id | UUID | - | PK | 报告唯一标识 |
| name | VARCHAR | 255 | NOT NULL | 报告名称 |
| template_id | UUID | - | FK(templates.id) | 使用的模板 |
| data_source_id | UUID | - | FK(data_sources.id) | 主要数据源 |
| file_path | VARCHAR | 500 | - | 生成的报告文件路径 |
| file_size | INTEGER | - | - | 文件大小(字节) |
| status | VARCHAR | 50 | DEFAULT 'draft' | 报告状态 |
| generation_method | VARCHAR | 50 | DEFAULT 'ai' | 生成方法 |
| quality_score | DECIMAL | 3,2 | - | 质量评分 |
| generation_time | INTEGER | - | - | 生成耗时(秒) |
| download_count | INTEGER | - | DEFAULT 0 | 下载次数 |
| metadata | JSONB | - | - | 报告元数据 |
| created_by | UUID | - | FK(users.id) | 创建者 |
| created_at | TIMESTAMP | - | NOT NULL | 创建时间 |
| updated_at | TIMESTAMP | - | NOT NULL | 更新时间 |

#### 3.4.2 generation_tasks (生成任务表)
| 字段名 | 类型 | 长度 | 约束 | 说明 |
|--------|------|------|------|------|
| id | UUID | - | PK | 任务唯一标识 |
| report_id | UUID | - | FK(reports.id) | 关联报告 |
| task_type | VARCHAR | 50 | NOT NULL | 任务类型 |
| status | VARCHAR | 50 | DEFAULT 'pending' | 任务状态 |
| progress | INTEGER | - | DEFAULT 0 | 进度百分比 |
| started_at | TIMESTAMP | - | - | 开始时间 |
| completed_at | TIMESTAMP | - | - | 完成时间 |
| error_message | TEXT | - | - | 错误信息 |
| result_data | JSONB | - | - | 结果数据 |
| created_at | TIMESTAMP | - | NOT NULL | 创建时间 |
| updated_at | TIMESTAMP | - | NOT NULL | 更新时间 |

### 3.5 审核与质量管理模块

#### 3.5.1 review_records (审核记录表)
| 字段名 | 类型 | 长度 | 约束 | 说明 |
|--------|------|------|------|------|
| id | UUID | - | PK | 审核记录唯一标识 |
| user_id | UUID | - | FK(users.id) | 审核用户 |
| target_type | VARCHAR | 50 | NOT NULL | 审核对象类型 |
| target_id | UUID | - | NOT NULL | 审核对象ID |
| action | VARCHAR | 50 | NOT NULL | 审核动作 |
| old_value | TEXT | - | - | 原始值 |
| new_value | TEXT | - | - | 修改后值 |
| comment | TEXT | - | - | 审核意见 |
| created_at | TIMESTAMP | - | NOT NULL | 创建时间 |

#### 3.5.2 quality_checks (质量检查表)
| 字段名 | 类型 | 长度 | 约束 | 说明 |
|--------|------|------|------|------|
| id | UUID | - | PK | 检查记录唯一标识 |
| report_id | UUID | - | FK(reports.id) | 关联报告 |
| check_type | VARCHAR | 50 | NOT NULL | 检查类型 |
| check_result | VARCHAR | 20 | NOT NULL | 检查结果 |
| issues_found | INTEGER | - | DEFAULT 0 | 发现问题数量 |
| issues_detail | JSONB | - | - | 问题详情 |
| suggestions | TEXT | - | - | 改进建议 |
| created_at | TIMESTAMP | - | NOT NULL | 创建时间 |

---

## 🔧 4. 物理数据模型设计

### 4.1 索引设计

#### 4.1.1 主要索引
```sql
-- 用户表索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_created_at ON users(created_at);

-- 模板表索引
CREATE INDEX idx_templates_name ON templates(name);
CREATE INDEX idx_templates_category ON templates(category);
CREATE INDEX idx_templates_created_by ON templates(created_by);
CREATE INDEX idx_templates_created_at ON templates(created_at);

-- 模板字段表索引
CREATE INDEX idx_template_fields_template_id ON template_fields(template_id);
CREATE INDEX idx_template_fields_field_name ON template_fields(field_name);
CREATE INDEX idx_template_fields_order ON template_fields(template_id, field_order);

-- 数据源表索引
CREATE INDEX idx_data_sources_name ON data_sources(name);
CREATE INDEX idx_data_sources_status ON data_sources(status);
CREATE INDEX idx_data_sources_created_by ON data_sources(created_by);
CREATE INDEX idx_data_sources_created_at ON data_sources(created_at);

-- 抽取数据表索引
CREATE INDEX idx_extracted_data_source_id ON extracted_data(data_source_id);
CREATE INDEX idx_extracted_data_template_id ON extracted_data(template_id);
CREATE INDEX idx_extracted_data_field_name ON extracted_data(field_name);
CREATE INDEX idx_extracted_data_verified ON extracted_data(is_verified);

-- 报告表索引
CREATE INDEX idx_reports_template_id ON reports(template_id);
CREATE INDEX idx_reports_data_source_id ON reports(data_source_id);
CREATE INDEX idx_reports_status ON reports(status);
CREATE INDEX idx_reports_created_by ON reports(created_by);
CREATE INDEX idx_reports_created_at ON reports(created_at);

-- 生成任务表索引
CREATE INDEX idx_generation_tasks_report_id ON generation_tasks(report_id);
CREATE INDEX idx_generation_tasks_status ON generation_tasks(status);
CREATE INDEX idx_generation_tasks_created_at ON generation_tasks(created_at);
```

#### 4.1.2 复合索引
```sql
-- 常用查询组合索引
CREATE INDEX idx_templates_active_category ON templates(is_active, category);
CREATE INDEX idx_data_sources_user_status ON data_sources(created_by, status);
CREATE INDEX idx_reports_user_template ON reports(created_by, template_id);
CREATE INDEX idx_extracted_data_source_field ON extracted_data(data_source_id, field_name);
```
