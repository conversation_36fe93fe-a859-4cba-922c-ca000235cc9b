"""
数据源相关Pydantic schemas
"""

from typing import Optional
from uuid import UUID
from decimal import Decimal
from pydantic import BaseModel, Field
from .common import BaseResponse


class DataSourceBase(BaseModel):
    """数据源基础模型"""
    
    name: str = Field(..., description="数据源名称")
    description: Optional[str] = Field(None, description="数据源描述")
    source_type: str = Field("document", description="数据源类型")


class DataSourceCreate(DataSourceBase):
    """创建数据源请求模型"""
    
    pass


class DataSourceResponse(DataSourceBase, BaseResponse):
    """数据源响应模型"""
    
    file_path: str
    file_size: Optional[int] = None
    mime_type: Optional[str] = None
    status: str = "uploaded"


class ExtractedDataBase(BaseModel):
    """抽取数据基础模型"""
    
    field_name: str = Field(..., description="字段名称")
    field_value: Optional[str] = Field(None, description="字段值")
    confidence_score: Optional[Decimal] = Field(None, description="置信度分数")
    extraction_method: str = Field("ai", description="抽取方法")
    is_verified: bool = Field(False, description="是否已验证")


class ExtractedDataResponse(ExtractedDataBase, BaseResponse):
    """抽取数据响应模型"""
    
    data_source_id: UUID
    template_id: Optional[UUID] = None


class DataExtractionRequest(BaseModel):
    """数据抽取请求模型"""
    
    data_source_id: UUID = Field(..., description="数据源ID")
    template_id: Optional[UUID] = Field(None, description="模板ID")
    extraction_method: str = Field("ai", description="抽取方法")


class DataExtractionResponse(BaseModel):
    """数据抽取响应模型"""
    
    data_source_id: UUID
    extracted_count: int = Field(..., description="抽取的数据条数")
    success_count: int = Field(..., description="成功抽取的数据条数")
    failed_count: int = Field(..., description="失败的数据条数")
    extraction_time: float = Field(..., description="抽取耗时(秒)")
    
    class Config:
        from_attributes = True
