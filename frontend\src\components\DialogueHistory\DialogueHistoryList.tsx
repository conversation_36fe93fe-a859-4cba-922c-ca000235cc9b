/**
 * 对话历史列表组件
 * 提供对话的增删改查功能
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  List,
  Button,
  Space,
  Typography,
  Input,
  Modal,
  message,
  Popconfirm,
  Tag,
  Avatar,
  Divider,
  Empty,
  Spin,
  Tooltip
} from 'antd';
import {
  MessageOutlined,
  DeleteOutlined,
  EditOutlined,
  SearchOutlined,
  PlusOutlined,
  HistoryOutlined,
  UserOutlined,
  RobotOutlined,
  ClearOutlined
} from '@ant-design/icons';
import { dialogueApi } from '../../services/dialogueApi';

const { Title, Text } = Typography;
const { Search } = Input;

// 类型定义
interface Conversation {
  id: string;
  title: string;
  user_id?: string;
  created_at: string;
  last_activity: string;
  message_count: number;
  agent_name: string;
}

interface ConversationMessage {
  id: string;
  role: string;
  content: string;
  timestamp: string;
  sequence_number: number;
}

interface DialogueHistoryListProps {
  userId?: string;
  onConversationSelect?: (conversationId: string) => void;
  onNewConversation?: () => void;
}

const DialogueHistoryList: React.FC<DialogueHistoryListProps> = ({
  userId = 'default_user',
  onConversationSelect,
  onNewConversation
}) => {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [messageHistory, setMessageHistory] = useState<ConversationMessage[]>([]);
  const [historyVisible, setHistoryVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingConversation, setEditingConversation] = useState<Conversation | null>(null);
  const [newTitle, setNewTitle] = useState('');

  // 加载对话列表
  const loadConversations = async () => {
    setLoading(true);
    try {
      const response = await dialogueApi.getUserConversations(userId, { limit: 50 });
      if (response.success) {
        setConversations(response.conversations);
      } else {
        message.error('加载对话列表失败');
      }
    } catch (error) {
      console.error('加载对话列表失败:', error);
      message.error('加载对话列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载对话历史
  const loadConversationHistory = async (conversationId: string) => {
    try {
      const response = await dialogueApi.getConversationHistory(conversationId);
      if (response.success) {
        setMessageHistory(response.messages);
        setHistoryVisible(true);
      } else {
        message.error('加载对话历史失败');
      }
    } catch (error) {
      console.error('加载对话历史失败:', error);
      message.error('加载对话历史失败');
    }
  };

  // 删除对话
  const deleteConversation = async (conversationId: string) => {
    try {
      const response = await dialogueApi.deleteConversation(conversationId);
      if (response.success) {
        message.success('对话已删除');
        loadConversations(); // 重新加载列表
      } else {
        message.error('删除对话失败');
      }
    } catch (error) {
      console.error('删除对话失败:', error);
      message.error('删除对话失败');
    }
  };

  // 清空对话历史
  const clearConversationHistory = async (conversationId: string) => {
    try {
      const response = await dialogueApi.clearConversationHistory(conversationId);
      if (response.success) {
        message.success('对话历史已清空');
      } else {
        message.error('清空对话历史失败');
      }
    } catch (error) {
      console.error('清空对话历史失败:', error);
      message.error('清空对话历史失败');
    }
  };

  // 更新对话标题
  const updateConversationTitle = async () => {
    if (!editingConversation || !newTitle.trim()) {
      message.error('请输入有效的标题');
      return;
    }

    try {
      const response = await dialogueApi.updateConversation(editingConversation.id, {
        title: newTitle.trim()
      });
      
      if (response.success) {
        message.success('标题已更新');
        setEditModalVisible(false);
        setEditingConversation(null);
        setNewTitle('');
        loadConversations(); // 重新加载列表
      } else {
        message.error('更新标题失败');
      }
    } catch (error) {
      console.error('更新标题失败:', error);
      message.error('更新标题失败');
    }
  };

  // 过滤对话列表
  const filteredConversations = conversations.filter(conv =>
    conv.title.toLowerCase().includes(searchText.toLowerCase()) ||
    conv.agent_name.toLowerCase().includes(searchText.toLowerCase())
  );

  // 格式化时间
  const formatTime = (timeString: string) => {
    try {
      return new Date(timeString).toLocaleString('zh-CN');
    } catch {
      return timeString;
    }
  };

  // 获取角色图标
  const getRoleIcon = (role: string) => {
    switch (role.toLowerCase()) {
      case 'human':
      case 'user':
        return <UserOutlined />;
      case 'assistant':
      case 'ai':
        return <RobotOutlined />;
      default:
        return <MessageOutlined />;
    }
  };

  useEffect(() => {
    loadConversations();
  }, [userId]);

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 头部操作栏 */}
      <Card size="small" style={{ marginBottom: '16px', flex: '0 0 auto' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={4} style={{ margin: 0 }}>
            💬 对话历史
          </Title>
          <Space>
            <Search
              placeholder="搜索对话..."
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 200 }}
              prefix={<SearchOutlined />}
            />
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={onNewConversation}
            >
              新建对话
            </Button>
          </Space>
        </div>
      </Card>

      {/* 对话列表 */}
      <Card style={{ flex: 1, overflow: 'hidden' }}>
        <div style={{ height: '100%', overflowY: 'auto' }}>
          <Spin spinning={loading}>
            {filteredConversations.length === 0 ? (
              <Empty
                description="暂无对话记录"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            ) : (
              <List
                dataSource={filteredConversations}
                renderItem={(conversation) => (
                  <List.Item
                    key={conversation.id}
                    style={{
                      cursor: 'pointer',
                      backgroundColor: selectedConversation === conversation.id ? '#f0f0f0' : 'transparent',
                      padding: '12px',
                      borderRadius: '8px',
                      marginBottom: '8px'
                    }}
                    onClick={() => {
                      setSelectedConversation(conversation.id);
                      onConversationSelect?.(conversation.id);
                    }}
                    actions={[
                      <Tooltip title="查看历史">
                        <Button
                          type="text"
                          size="small"
                          icon={<HistoryOutlined />}
                          onClick={(e) => {
                            e.stopPropagation();
                            loadConversationHistory(conversation.id);
                          }}
                        />
                      </Tooltip>,
                      <Tooltip title="编辑标题">
                        <Button
                          type="text"
                          size="small"
                          icon={<EditOutlined />}
                          onClick={(e) => {
                            e.stopPropagation();
                            setEditingConversation(conversation);
                            setNewTitle(conversation.title);
                            setEditModalVisible(true);
                          }}
                        />
                      </Tooltip>,
                      <Tooltip title="清空历史">
                        <Popconfirm
                          title="确定要清空此对话的历史记录吗？"
                          onConfirm={(e) => {
                            e?.stopPropagation();
                            clearConversationHistory(conversation.id);
                          }}
                          onCancel={(e) => e?.stopPropagation()}
                        >
                          <Button
                            type="text"
                            size="small"
                            icon={<ClearOutlined />}
                            onClick={(e) => e.stopPropagation()}
                          />
                        </Popconfirm>
                      </Tooltip>,
                      <Tooltip title="删除对话">
                        <Popconfirm
                          title="确定要删除此对话吗？"
                          onConfirm={(e) => {
                            e?.stopPropagation();
                            deleteConversation(conversation.id);
                          }}
                          onCancel={(e) => e?.stopPropagation()}
                        >
                          <Button
                            type="text"
                            size="small"
                            danger
                            icon={<DeleteOutlined />}
                            onClick={(e) => e.stopPropagation()}
                          />
                        </Popconfirm>
                      </Tooltip>
                    ]}
                  >
                    <List.Item.Meta
                      avatar={<Avatar icon={<MessageOutlined />} />}
                      title={
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Text strong>{conversation.title}</Text>
                          <Tag color="blue">{conversation.agent_name}</Tag>
                        </div>
                      }
                      description={
                        <div>
                          <Text type="secondary">
                            {conversation.message_count} 条消息 • 最后活动: {formatTime(conversation.last_activity)}
                          </Text>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            )}
          </Spin>
        </div>
      </Card>

      {/* 编辑标题模态框 */}
      <Modal
        title="编辑对话标题"
        open={editModalVisible}
        onOk={updateConversationTitle}
        onCancel={() => {
          setEditModalVisible(false);
          setEditingConversation(null);
          setNewTitle('');
        }}
        okText="保存"
        cancelText="取消"
      >
        <Input
          value={newTitle}
          onChange={(e) => setNewTitle(e.target.value)}
          placeholder="请输入新的对话标题"
          maxLength={100}
        />
      </Modal>

      {/* 对话历史模态框 */}
      <Modal
        title="对话历史记录"
        open={historyVisible}
        onCancel={() => setHistoryVisible(false)}
        footer={null}
        width={800}
        style={{ top: 20 }}
      >
        <div style={{ maxHeight: '60vh', overflowY: 'auto' }}>
          {messageHistory.length === 0 ? (
            <Empty description="暂无历史记录" />
          ) : (
            <List
              dataSource={messageHistory}
              renderItem={(msg) => (
                <List.Item key={msg.id}>
                  <List.Item.Meta
                    avatar={<Avatar icon={getRoleIcon(msg.role)} />}
                    title={
                      <Space>
                        <Text strong>{msg.role === 'human' ? '用户' : 'AI助手'}</Text>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {formatTime(msg.timestamp)}
                        </Text>
                      </Space>
                    }
                    description={
                      <div style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                        {msg.content}
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          )}
        </div>
      </Modal>
    </div>
  );
};

export default DialogueHistoryList;
