#!/usr/bin/env python3
"""
文件上传功能测试
"""

import requests
from pathlib import Path

def test_file_upload():
    """测试文件上传功能"""
    print("🔍 测试文件上传功能...")
    
    base_url = "http://localhost:8000"
    
    # 检查测试文件
    test_file = Path("test/documents/test_meeting.txt")
    if not test_file.exists():
        print("❌ 测试文件不存在，先创建...")
        test_file.parent.mkdir(exist_ok=True)
        test_content = """会议纪要
        
会议主题：文件上传功能测试
会议时间：2024年7月4日
主持人：测试员

会议内容：
1. 测试文件上传API
2. 验证文档解析功能
3. 确认返回数据格式"""
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        print(f"✅ 创建测试文件: {test_file}")
    
    try:
        # 测试文件上传
        print("📤 上传文件...")
        with open(test_file, 'rb') as f:
            files = {'file': (test_file.name, f, 'text/plain')}
            response = requests.post(f"{base_url}/api/upload/document", files=files)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 文件上传成功")
            print(f"📄 文件ID: {result['file_id']}")
            print(f"📝 文档类型: {result['document_type']}")
            print(f"📊 文本长度: {result['text_length']}")
            print(f"📋 段落数: {result['paragraph_count']}")
            print(f"👀 预览: {result['preview']}")
            
            file_id = result['file_id']
            
            # 测试获取文档内容
            print(f"\n📖 获取文档内容...")
            response = requests.get(f"{base_url}/api/upload/document/{file_id}")
            
            if response.status_code == 200:
                content = response.json()
                print("✅ 获取文档内容成功")
                print(f"📝 完整文本长度: {len(content['raw_text'])}")
                print(f"📊 段落数: {len(content['paragraphs'])}")
            else:
                print(f"❌ 获取文档内容失败: {response.status_code}")
            
            # 测试文档列表
            print(f"\n📋 获取文档列表...")
            response = requests.get(f"{base_url}/api/upload/list")
            
            if response.status_code == 200:
                doc_list = response.json()
                print(f"✅ 获取文档列表成功，共 {doc_list['total']} 个文档")
                for doc in doc_list['documents']:
                    print(f"  - {doc['filename']} ({doc['file_type']})")
            else:
                print(f"❌ 获取文档列表失败: {response.status_code}")
            
            # 测试删除文档
            print(f"\n🗑️ 删除文档...")
            response = requests.delete(f"{base_url}/api/upload/document/{file_id}")
            
            if response.status_code == 200:
                print("✅ 文档删除成功")
            else:
                print(f"❌ 文档删除失败: {response.status_code}")
            
            return True
            
        else:
            print(f"❌ 文件上传失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保FastAPI应用正在运行")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False


if __name__ == "__main__":
    print("🚀 开始文件上传功能测试\n")
    
    success = test_file_upload()
    
    print(f"\n{'='*50}")
    if success:
        print("🎉 文件上传功能测试通过！")
        print("✅ 文件上传API已准备就绪")
    else:
        print("❌ 文件上传功能测试失败")
        print("💡 请检查FastAPI服务是否正在运行")
