/* 全局样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

#root {
  width: 100vw;
  min-height: 100vh;
}

/* 自定义样式 */
.ant-layout {
  background: #f0f2f5;
}

.ant-layout-header {
  position: sticky;
  top: 0;
  z-index: 100;
}

.ant-steps {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-upload-drag {
  border-radius: 8px !important;
}

.ant-table {
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-content {
    padding: 16px 0 !important;
  }

  .ant-steps {
    padding: 16px;
  }

  .ant-card {
    margin: 0 16px;
  }
}

/* 动画效果 */
.ant-card {
  transition: all 0.3s ease;
}

.ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* 自定义进度条样式 */
.ant-progress-line {
  border-radius: 4px;
}

/* 表格样式优化 */
.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 按钮样式 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

.ant-btn-primary {
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
}

.ant-btn-primary:hover {
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
}
