{"workflow_id": "f9b279d4-45e4-43c9-8abf-1999a099f734", "current_stage": "data_extraction", "next_action": "human_interaction", "pending_interactions": ["supplement"], "human_interactions": [{"interaction_type": "confirm", "field_name": "template_confirmation", "original_value": {"type": "template_confirmation", "message": "我识别到您需要生成会议纪要。系统找到了以下匹配的模板：", "templates": [{"template_id": "2.key_definitions", "template_name": "会议纪要模板", "template_path": "C:\\scripts\\agent\\multi-agents\\backend\\templates\\wordtemplates\\4.template_数字科技中心会议纪要模板 copy.docx", "key_definitions_path": "C:\\scripts\\agent\\multi-agents\\backend\\templates\\keysdefinition\\2.key_definitions.md", "field_definitions": [{"field_name": "meeting_title", "description": "会议的名称，一次会议只有一个名称，20字内", "example": "5月月报项目进度评审会", "format": "普通文本", "must": true, "type": "string", "default": "\"<无会议名称>\"", "length": "", "source": ""}, {"field_name": "meeting_time", "description": "会议召开的时间，为北京时间", "example": "2025年6月26日（星期四）上午9:00", "format": "普通文本", "must": true, "type": "string", "default": "当前系统时间", "length": "", "source": ""}, {"field_name": "meeting_location", "description": "会议召开的地点，一般为某处会议室", "example": "广州地铁设计大厦0513会议室（白云区云城北二路129号）", "format": "普通文本", "must": true, "type": "string", "default": "\"<无会议地点>\"", "length": "", "source": ""}, {"field_name": "meeting_theme", "description": "议题，会上讨论了什么内容，一句或一段话，50字内", "example": "项目开展情况检查", "format": "普通文本", "must": true, "type": "string", "default": "\"<无会议议题>\"", "length": "50", "source": "上传内容"}, {"field_name": "meeting_host", "description": "会议主持人是谁？一般为人名", "example": "谢特赐", "format": "人名", "must": true, "type": "string", "default": "\"<无会议主持人>\"", "length": "12", "source": "上传内容"}, {"field_name": "meeting_recorder", "description": "会议记录人是谁？一般为人名", "example": "谢特赐", "format": "人名", "must": true, "type": "string", "default": "\"<无会议记录人>\"", "length": "12", "source": "上传内容"}, {"field_name": "symmary_receiver", "description": "谁应该接受这个会议纪要，如果是人名，可以有多个人，每个人之间用、隔开或者直接注明所有参会人员", "example": "所有参会人员", "format": "人名列表或组织代号", "must": true, "type": "string", "default": "\"<所有参会人员>\"", "length": "12", "source": "上传内容"}, {"field_name": "attend", "description": "参加会议的人员有哪些？和参会部分并列，放到表格中", "example": "桂林、李远峰、赵梓茗", "format": "行单元格", "must": true, "type": "array", "default": "\"\"", "length": "20", "source": "上传内容"}, {"field_name": "detail", "description": "解决这些问题的截止时间是什么时候？", "example": "数字科技中心", "format": "表格行", "must": true, "type": "array", "default": "会议召开时间的一个星期后", "length": "30", "source": "上传内容"}, {"field_name": "other", "description": "会议还有哪些其他安排，一段话描述，每个安排一句话，形成一段话，可以没有", "example": "", "format": "段落", "must": false, "type": "string", "default": "\"\"", "length": "100", "source": "上传内容"}], "placeholder_count": 10, "description": "动态加载的会议纪要模板"}], "default_selection": 0, "options": [{"value": "confirm", "label": "确认使用推荐模板"}, {"value": "select_other", "label": "选择其他模板"}, {"value": "cancel", "label": "取消生成"}]}, "user_input": null, "user_choice": null, "timestamp": "2025-07-25 17:11:06.332620", "is_completed": false}, {"interaction_type": "template_confirmation", "field_name": null, "original_value": null, "user_input": null, "user_choice": "confirm", "timestamp": "2025-07-25T17:11:09.453595", "is_completed": true}, {"interaction_type": "supplement", "field_name": "missing_required_fields", "original_value": null, "user_input": null, "user_choice": null, "timestamp": "2025-07-25 17:11:19.875391", "is_completed": false}], "extraction_results": [{"field_name": "meeting_title", "field_value": "住建厅科研验收项目内部审查会", "confidence": 0.9, "source_location": null, "extraction_method": "llm_intelligent", "is_verified": false, "needs_human_input": false, "field_definition": {"field_name": "meeting_title", "description": "会议的名称，一次会议只有一个名称，20字内", "example": "5月月报项目进度评审会", "format": "普通文本", "must": true, "type": "string", "default": "\"<无会议名称>\"", "length": "", "source": ""}}, {"field_name": "meeting_time", "field_value": "2025年6月26日(周四)上午9:00", "confidence": 0.9, "source_location": null, "extraction_method": "llm_intelligent", "is_verified": false, "needs_human_input": false, "field_definition": {"field_name": "meeting_time", "description": "会议召开的时间，为北京时间", "example": "2025年6月26日（星期四）上午9:00", "format": "普通文本", "must": true, "type": "string", "default": "当前系统时间", "length": "", "source": ""}}, {"field_name": "meeting_location", "field_value": "广州地铁设计大厦0513会议室(白云区云城北二路129号)", "confidence": 0.9, "source_location": null, "extraction_method": "llm_intelligent", "is_verified": false, "needs_human_input": false, "field_definition": {"field_name": "meeting_location", "description": "会议召开的地点，一般为某处会议室", "example": "广州地铁设计大厦0513会议室（白云区云城北二路129号）", "format": "普通文本", "must": true, "type": "string", "default": "\"<无会议地点>\"", "length": "", "source": ""}}, {"field_name": "meeting_theme", "field_value": "住建厅科研验收项目内部审查", "confidence": 0.9, "source_location": null, "extraction_method": "llm_intelligent", "is_verified": false, "needs_human_input": false, "field_definition": {"field_name": "meeting_theme", "description": "议题，会上讨论了什么内容，一句或一段话，50字内", "example": "项目开展情况检查", "format": "普通文本", "must": true, "type": "string", "default": "\"<无会议议题>\"", "length": "50", "source": "上传内容"}}, {"field_name": "meeting_host", "field_value": "袁泉", "confidence": 0.8, "source_location": null, "extraction_method": "llm_intelligent", "is_verified": false, "needs_human_input": false, "field_definition": {"field_name": "meeting_host", "description": "会议主持人是谁？一般为人名", "example": "谢特赐", "format": "人名", "must": true, "type": "string", "default": "\"<无会议主持人>\"", "length": "12", "source": "上传内容"}}, {"field_name": "meeting_recorder", "field_value": "未找到", "confidence": 0.0, "source_location": null, "extraction_method": "llm_intelligent", "is_verified": false, "needs_human_input": true, "field_definition": {"field_name": "meeting_recorder", "description": "会议记录人是谁？一般为人名", "example": "谢特赐", "format": "人名", "must": true, "type": "string", "default": "\"<无会议记录人>\"", "length": "12", "source": "上传内容"}}, {"field_name": "symmary_receiver", "field_value": "科技质量部; 余泥渣土资源化项目组; 光纤技术项目组; 消防智能审查平台项目组", "confidence": 0.9, "source_location": null, "extraction_method": "llm_intelligent", "is_verified": false, "needs_human_input": false, "field_definition": {"field_name": "symmary_receiver", "description": "谁应该接受这个会议纪要，如果是人名，可以有多个人，每个人之间用、隔开或者直接注明所有参会人员", "example": "所有参会人员", "format": "人名列表或组织代号", "must": true, "type": "string", "default": "\"<所有参会人员>\"", "length": "12", "source": "上传内容"}}, {"field_name": "attend", "field_value": "袁泉\n苏拓\n阮艳妹\n张晶潇\n周林仁\n房帅\n覃羽丰\n谢信\n刘静\n桂林\n李远峰\n赵梓茗", "confidence": 0.9, "source_location": null, "extraction_method": "llm_intelligent", "is_verified": false, "needs_human_input": false, "field_definition": {"field_name": "attend", "description": "参加会议的人员有哪些？和参会部分并列，放到表格中", "example": "桂林、李远峰、赵梓茗", "format": "行单元格", "must": true, "type": "array", "default": "\"\"", "length": "20", "source": "上传内容"}}, {"field_name": "detail", "field_value": "topic: 轨道交通余泥渣土资源化与性能提升关键技术与示范; time: 9:00~10:00; reporter: 张晶潇\ntopic: 基于光纤技术的城市轨道交通控制保护区异物入侵检测技术研究与应用; time: 10:00～11:00; reporter: 刘静\ntopic: 基于BIM的轨道交通消防智能审查平台研发; time: 11:00～12:00; reporter: 桂林", "confidence": 0.9, "source_location": null, "extraction_method": "llm_intelligent", "is_verified": false, "needs_human_input": false, "field_definition": {"field_name": "detail", "description": "解决这些问题的截止时间是什么时候？", "example": "数字科技中心", "format": "表格行", "must": true, "type": "array", "default": "会议召开时间的一个星期后", "length": "30", "source": "上传内容"}}, {"field_name": "other", "field_value": "请各参会人员在会议开始前10分钟到达会议室。", "confidence": 0.7999999999999999, "source_location": null, "extraction_method": "llm_intelligent", "is_verified": false, "needs_human_input": false, "field_definition": {"field_name": "other", "description": "会议还有哪些其他安排，一段话描述，每个安排一句话，形成一段话，可以没有", "example": "", "format": "段落", "must": false, "type": "string", "default": "\"\"", "length": "100", "source": "上传内容"}}], "extracted_data": {"meeting_title": "住建厅科研验收项目内部审查会", "meeting_time": "2025年6月26日(周四)上午9:00", "meeting_location": "广州地铁设计大厦0513会议室(白云区云城北二路129号)", "meeting_theme": "住建厅科研验收项目内部审查", "meeting_host": "袁泉", "meeting_recorder": "未找到", "symmary_receiver": "科技质量部; 余泥渣土资源化项目组; 光纤技术项目组; 消防智能审查平台项目组", "attend": "袁泉\n苏拓\n阮艳妹\n张晶潇\n周林仁\n房帅\n覃羽丰\n谢信\n刘静\n桂林\n李远峰\n赵梓茗", "detail": "topic: 轨道交通余泥渣土资源化与性能提升关键技术与示范; time: 9:00~10:00; reporter: 张晶潇\ntopic: 基于光纤技术的城市轨道交通控制保护区异物入侵检测技术研究与应用; time: 10:00～11:00; reporter: 刘静\ntopic: 基于BIM的轨道交通消防智能审查平台研发; time: 11:00～12:00; reporter: 桂林", "other": "请各参会人员在会议开始前10分钟到达会议室。"}, "extraction_stats": {"total_fields": 10, "extracted_fields": 9, "success_rate": 0.9, "avg_confidence": 0.79}, "template_info": {"template_id": "2.key_definitions", "template_name": "会议纪要模板", "template_path": "C:\\scripts\\agent\\multi-agents\\backend\\templates\\wordtemplates\\4.template_数字科技中心会议纪要模板 copy.docx", "key_definitions_path": "C:\\scripts\\agent\\multi-agents\\backend\\templates\\keysdefinition\\2.key_definitions.md", "field_definitions": [{"field_name": "meeting_title", "description": "会议的名称，一次会议只有一个名称，20字内", "example": "5月月报项目进度评审会", "format": "普通文本", "must": true}, {"field_name": "meeting_time", "description": "会议召开的时间，为北京时间", "example": "2025年6月26日（星期四）上午9:00", "format": "普通文本", "must": true}, {"field_name": "meeting_location", "description": "会议召开的地点，一般为某处会议室", "example": "广州地铁设计大厦0513会议室（白云区云城北二路129号）", "format": "普通文本", "must": true}, {"field_name": "meeting_theme", "description": "议题，会上讨论了什么内容，一句或一段话，50字内", "example": "项目开展情况检查", "format": "普通文本", "must": true}, {"field_name": "meeting_host", "description": "会议主持人是谁？一般为人名", "example": "谢特赐", "format": "人名", "must": true}, {"field_name": "meeting_recorder", "description": "会议记录人是谁？一般为人名", "example": "谢特赐", "format": "人名", "must": true}, {"field_name": "symmary_receiver", "description": "谁应该接受这个会议纪要，如果是人名，可以有多个人，每个人之间用、隔开或者直接注明所有参会人员", "example": "所有参会人员", "format": "人名列表或组织代号", "must": true}, {"field_name": "attend", "description": "参加会议的人员有哪些？和参会部分并列，放到表格中", "example": "桂林、李远峰、赵梓茗", "format": "行单元格", "must": true}, {"field_name": "detail", "description": "解决这些问题的截止时间是什么时候？", "example": "数字科技中心", "format": "表格行", "must": true}, {"field_name": "other", "description": "会议还有哪些其他安排，一段话描述，每个安排一句话，形成一段话，可以没有", "example": "", "format": "段落", "must": false}], "placeholder_count": 10, "description": "动态加载的会议纪要模板"}, "errors": [], "warnings": [], "requires_user_action": true, "saved_at": "2025-07-25T17:11:19.876898"}