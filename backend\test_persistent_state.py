#!/usr/bin/env python3
"""
测试持久化状态机制
"""

import requests
import tempfile
import os
import json
import time

def test_persistent_state():
    """测试持久化状态机制"""
    
    # 创建测试文档
    test_content = """
    产品发布会议纪要
    
    会议时间：2025年1月30日 上午9:00-11:00
    会议地点：产品会议室
    主持人：产品总监
    参会人员：产品经理、市场经理、技术负责人、运营经理、设计师
    
    会议议题：
    1. 新产品发布计划讨论
    2. 市场推广策略制定
    3. 技术准备情况评估
    
    讨论内容：
    1. 产品功能已基本完成，预计下周完成最终测试
    2. 市场推广方案需要进一步完善
    3. 技术团队已准备好发布环境
    4. 设计团队完成了所有宣传物料
    
    决议事项：
    1. 产品经理协调最终测试，确保质量
    2. 市场经理完善推广方案，本周五前提交
    3. 技术负责人准备发布流程，下周一前完成
    4. 运营经理制定用户支持计划
    5. 正式发布时间定为2月15日
    
    会议记录人：产品经理
    """
    
    print("🚀 开始测试持久化状态机制...")
    
    # 第1步: 启动工作流
    print("\n📝 第1步: 启动工作流...")
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(test_content)
        temp_file_path = f.name
    
    try:
        with open(temp_file_path, 'rb') as f:
            files = {'files': ('product_launch.txt', f, 'text/plain')}
            data = {'text_input': '请帮我生成会议纪要'}
            
            response = requests.post(
                'http://localhost:8000/api/workflow/langgraph/start',
                files=files,
                data=data
            )
        
        print(f"启动状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            workflow_id = result.get('workflow_id')
            print(f"工作流ID: {workflow_id}")
            print(f"工作流状态: {result.get('status')}")
            
            requires_user_action = result.get('data', {}).get('requires_user_action', False)
            print(f"需要用户操作: {requires_user_action}")
            
            if requires_user_action:
                print("\n✅ 工作流正确暂停，等待用户确认模板")
                
                # 第2步: 确认模板
                print("\n📋 第2步: 确认推荐的模板...")
                time.sleep(2)
                
                confirm_response = requests.post(
                    f'http://localhost:8000/api/workflow/langgraph/resume/{workflow_id}',
                    json={'action': 'confirm'}
                )
                
                print(f"模板确认状态码: {confirm_response.status_code}")
                if confirm_response.status_code == 200:
                    confirm_result = confirm_response.json()
                    print(f"确认状态: {confirm_result.get('status')}")
                    
                    # 第3步: 等待数据提取并检查持久化状态
                    print("\n🔍 第3步: 等待数据提取并检查持久化状态...")
                    
                    # 等待数据提取完成
                    for i in range(10):  # 最多等待10次，每次3秒
                        time.sleep(3)
                        print(f"  检查持久化状态 ({i+1}/10)...")
                        
                        status_response = requests.get(
                            f'http://localhost:8000/api/workflow/langgraph/status/{workflow_id}'
                        )
                        
                        if status_response.status_code == 200:
                            status_data = status_response.json()
                            
                            print(f"    状态来源: {'持久化存储' if status_data.get('saved_at') else 'LangGraph'}")
                            print(f"    当前阶段: {status_data.get('current_stage')}")
                            print(f"    下一步动作: {status_data.get('next_action')}")
                            print(f"    待处理交互: {status_data.get('pending_interactions', [])}")
                            print(f"    提取结果数量: {len(status_data.get('extraction_results', []))}")
                            print(f"    需要用户操作: {status_data.get('requires_user_action', False)}")
                            
                            # 检查是否有持久化状态
                            if status_data.get('saved_at'):
                                print(f"\n🎉 持久化状态机制成功！")
                                print(f"    保存时间: {status_data.get('saved_at')}")
                                
                                # 显示提取结果
                                extraction_results = status_data.get('extraction_results', [])
                                if extraction_results:
                                    print(f"\n📊 提取结果 (前5个):")
                                    for result in extraction_results[:5]:
                                        field_name = result.get('field_name', 'unknown')
                                        field_value = result.get('field_value', 'N/A')
                                        confidence = result.get('confidence', 0)
                                        print(f"      {field_name}: {field_value} (置信度: {confidence:.2f})")
                                
                                # 如果需要用户操作，进行数据确认
                                if status_data.get('requires_user_action'):
                                    print(f"\n✅ 第4步: 确认提取的数据...")
                                    
                                    data_confirm_response = requests.post(
                                        f'http://localhost:8000/api/workflow/langgraph/resume/{workflow_id}',
                                        json={'action': 'confirm'}
                                    )
                                    
                                    if data_confirm_response.status_code == 200:
                                        print("✅ 数据确认成功！")
                                    else:
                                        print(f"❌ 数据确认失败: {data_confirm_response.status_code}")
                                
                                break
                            
                            # 检查是否有提取结果（即使没有持久化状态）
                            elif status_data.get('extraction_results'):
                                print(f"\n⚠️ 有提取结果但没有持久化状态")
                                break
                        else:
                            print(f"    ❌ 状态检查失败: {status_response.status_code}")
                    else:
                        print("\n⚠️ 等待超时，检查是否有提取文件生成")
                        
                        # 检查是否有提取文件生成
                        import glob
                        extract_files = glob.glob(f"backend/templates/keysextracted/{workflow_id}_*.md")
                        if extract_files:
                            print(f"✅ 找到提取文件: {extract_files}")
                            print("数据提取成功，但状态持久化可能有问题")
                        else:
                            print("❌ 没有找到提取文件")
                        
                else:
                    print(f"❌ 模板确认失败: {confirm_response.status_code}")
            else:
                print("❌ 工作流没有正确暂停等待用户输入")
        else:
            print(f"❌ 启动工作流失败: {response.status_code}")
            print(response.text)
            
    finally:
        # 清理临时文件
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
    
    print("\n✅ 持久化状态测试完成")

if __name__ == "__main__":
    test_persistent_state()
