-- 数据库初始化脚本 V0.1
-- 创建数据库和用户已在 docker-compose.yml 中配置
-- 支持基于LangGraph的智能体应用架构

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建基础表结构 (临时，后续会用 Alembic 管理)

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    department VARCHAR(100),
    role VARCHAR(20) NOT NULL DEFAULT 'user',
    is_active BOOLEAN DEFAULT TRUE,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100)
);

-- 模板表 (增强版)
CREATE TABLE IF NOT EXISTS templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(50),
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    placeholder_count INTEGER DEFAULT 0,
    version VARCHAR(20),
    schema_json JSONB, -- 存储占位符结构
    key_definitions_path VARCHAR(500), -- 关键信息定义文件路径
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE
);

-- 关键信息定义表 (新增)
CREATE TABLE IF NOT EXISTS key_definitions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    template_id UUID NOT NULL REFERENCES templates(id) ON DELETE CASCADE,
    field_name VARCHAR(255) NOT NULL,
    parent_field VARCHAR(255), -- 支持层级结构
    field_level INTEGER DEFAULT 1, -- 字段层级
    field_path VARCHAR(500), -- 完整路径，如 "attend.person"

    -- 7个核心属性
    description TEXT, -- 字段描述，用于嵌入prompt
    example TEXT, -- 字段样例
    format VARCHAR(50) NOT NULL DEFAULT '普通文本', -- 字段格式
    default_value TEXT, -- 默认值
    length INTEGER, -- 字段长度限制
    source VARCHAR(50) NOT NULL DEFAULT '上传内容', -- 信息来源
    must BOOLEAN DEFAULT FALSE, -- 是否必须

    field_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(template_id, field_path)
);

-- 数据源表 (增强版)
CREATE TABLE IF NOT EXISTS data_sources (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    file_hash VARCHAR(64), -- 文件哈希值
    source_type VARCHAR(50) NOT NULL DEFAULT 'document',
    status VARCHAR(50) DEFAULT 'uploaded',
    extraction_count INTEGER DEFAULT 0,
    file_metadata JSONB, -- 文件元数据
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE
);

-- 提取数据表 (支持树形结构)
CREATE TABLE IF NOT EXISTS extracted_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    data_source_id UUID NOT NULL REFERENCES data_sources(id) ON DELETE CASCADE,
    template_id UUID REFERENCES templates(id) ON DELETE SET NULL,
    session_id UUID, -- 关联会话
    field_name VARCHAR(255) NOT NULL,
    field_path VARCHAR(500), -- 完整字段路径
    field_value TEXT,
    original_text TEXT, -- 原始文本
    confidence_score DECIMAL(3,2),
    extraction_method VARCHAR(50) DEFAULT 'ai',
    is_verified BOOLEAN DEFAULT FALSE,
    verification_status VARCHAR(20) DEFAULT 'pending',
    position_info JSONB, -- 位置信息
    parent_id UUID REFERENCES extracted_data(id), -- 支持层级结构
    level INTEGER DEFAULT 1, -- 层级深度
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 报告表 (增强版)
CREATE TABLE IF NOT EXISTS reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    template_id UUID NOT NULL REFERENCES templates(id),
    data_source_id UUID REFERENCES data_sources(id),
    session_id UUID, -- 关联会话
    file_path VARCHAR(500),
    file_size INTEGER,
    status VARCHAR(50) DEFAULT 'draft',
    generation_method VARCHAR(50) DEFAULT 'ai',
    quality_score DECIMAL(3,2), -- 质量评分
    generation_time INTEGER, -- 生成耗时(秒)
    download_count INTEGER DEFAULT 0,
    report_metadata JSONB, -- 报告元数据
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE
);

-- 智能体会话表 (新增)
CREATE TABLE IF NOT EXISTS agent_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    session_name VARCHAR(255),
    session_type VARCHAR(50) DEFAULT 'report_generation', -- 会话类型
    status VARCHAR(50) DEFAULT 'active', -- active, completed, failed
    current_agent VARCHAR(100), -- 当前活跃的智能体
    current_stage VARCHAR(100), -- 当前处理阶段
    context_data JSONB, -- 会话上下文数据
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- 对话历史表 (新增)
CREATE TABLE IF NOT EXISTS conversation_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID NOT NULL REFERENCES agent_sessions(id) ON DELETE CASCADE,
    message_type VARCHAR(50) NOT NULL, -- user, agent, system, tool
    sender VARCHAR(100), -- 发送者标识
    content TEXT NOT NULL, -- 消息内容
    message_metadata JSONB, -- 消息元数据
    tool_calls JSONB, -- 工具调用信息
    attachments JSONB, -- 附件信息
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 工具调用记录表 (新增)
CREATE TABLE IF NOT EXISTS tool_calls (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID NOT NULL REFERENCES agent_sessions(id) ON DELETE CASCADE,
    conversation_id UUID REFERENCES conversation_history(id),
    tool_name VARCHAR(100) NOT NULL, -- 工具名称
    tool_input JSONB NOT NULL, -- 工具输入参数
    tool_output JSONB, -- 工具输出结果
    status VARCHAR(50) DEFAULT 'pending', -- pending, success, failed
    error_message TEXT, -- 错误信息
    execution_time INTEGER, -- 执行时间(毫秒)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- 生成任务表 (增强版)
CREATE TABLE IF NOT EXISTS generation_tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES agent_sessions(id),
    report_id UUID REFERENCES reports(id),
    task_type VARCHAR(50) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    progress INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    result_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 质量检查表
CREATE TABLE IF NOT EXISTS quality_checks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    report_id UUID NOT NULL REFERENCES reports(id) ON DELETE CASCADE,
    check_type VARCHAR(50) NOT NULL,
    check_result VARCHAR(20) NOT NULL,
    issues_found INTEGER DEFAULT 0,
    issues_detail JSONB,
    suggestions TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_templates_name ON templates(name);
CREATE INDEX IF NOT EXISTS idx_templates_created_at ON templates(created_at);
CREATE INDEX IF NOT EXISTS idx_key_definitions_template_id ON key_definitions(template_id);
CREATE INDEX IF NOT EXISTS idx_key_definitions_field_path ON key_definitions(field_path);
CREATE INDEX IF NOT EXISTS idx_data_sources_created_at ON data_sources(created_at);
CREATE INDEX IF NOT EXISTS idx_extracted_data_source_id ON extracted_data(data_source_id);
CREATE INDEX IF NOT EXISTS idx_extracted_data_template_id ON extracted_data(template_id);
CREATE INDEX IF NOT EXISTS idx_extracted_data_session_id ON extracted_data(session_id);
CREATE INDEX IF NOT EXISTS idx_extracted_data_parent_id ON extracted_data(parent_id);
CREATE INDEX IF NOT EXISTS idx_reports_template_id ON reports(template_id);
CREATE INDEX IF NOT EXISTS idx_reports_session_id ON reports(session_id);
CREATE INDEX IF NOT EXISTS idx_reports_created_at ON reports(created_at);
CREATE INDEX IF NOT EXISTS idx_agent_sessions_user_id ON agent_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_agent_sessions_status ON agent_sessions(status);
CREATE INDEX IF NOT EXISTS idx_conversation_history_session_id ON conversation_history(session_id);
CREATE INDEX IF NOT EXISTS idx_conversation_history_created_at ON conversation_history(created_at);
CREATE INDEX IF NOT EXISTS idx_tool_calls_session_id ON tool_calls(session_id);
CREATE INDEX IF NOT EXISTS idx_tool_calls_tool_name ON tool_calls(tool_name);
CREATE INDEX IF NOT EXISTS idx_generation_tasks_session_id ON generation_tasks(session_id);
CREATE INDEX IF NOT EXISTS idx_quality_checks_report_id ON quality_checks(report_id);

-- 插入示例数据
INSERT INTO users (username, email, password_hash, full_name, role, created_by) VALUES
('admin', '<EMAIL>', 'hashed_password', '系统管理员', 'admin', 'system'),
('user1', '<EMAIL>', 'hashed_password', '测试用户1', 'user', 'system')
ON CONFLICT (username) DO NOTHING;

INSERT INTO templates (name, description, category, file_path, created_by) VALUES
('数字科技中心会议纪要模板', '数字科技中心会议纪要标准模板', '会议纪要', '/templates/meeting_minutes_template.docx', 'system'),
('超限报告模板', '标准超限设计报告模板', '工程报告', '/templates/sample_template.docx', 'system')
ON CONFLICT DO NOTHING;

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_templates_updated_at BEFORE UPDATE ON templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_key_definitions_updated_at BEFORE UPDATE ON key_definitions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_data_sources_updated_at BEFORE UPDATE ON data_sources
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_extracted_data_updated_at BEFORE UPDATE ON extracted_data
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_reports_updated_at BEFORE UPDATE ON reports
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agent_sessions_updated_at BEFORE UPDATE ON agent_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_generation_tasks_updated_at BEFORE UPDATE ON generation_tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_quality_checks_updated_at BEFORE UPDATE ON quality_checks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
