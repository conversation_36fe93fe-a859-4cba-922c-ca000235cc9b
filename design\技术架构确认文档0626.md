# 技术架构确认文档 (0626版)

**版本**: V1.0  
**创建日期**: 2025年6月26日  
**项目名称**: 超限报告智能生成工具  
**文档类型**: 技术架构确认文档  

---

## 📋 项目现状分析

### 当前项目结构
项目已具备基础框架：
- ✅ **后端基础**: FastAPI框架，基础目录结构已创建
- ✅ **前端基础**: React + TypeScript + Vite，基础组件可运行
- ✅ **依赖管理**: 后端使用uv管理，前端使用npm
- ✅ **设计文档**: 需求、架构、界面设计文档齐全
- ⚠️ **开发环境**: 需要完善Docker配置和数据库连接

### 技术栈差异分析
**设计文档 vs 实际实现**:
- 设计文档建议: FastAPI + Ant Design Pro + PostgreSQL + Redis
- 当前实现: FastAPI + 基础React + 缺少UI框架和数据库配置
- **需要调整**: 前端UI框架选择，数据库和缓存服务配置

---

## 🎯 确认的技术架构

### 后端技术栈 (已确认)
- **框架**: FastAPI 0.110+
- **数据库**: PostgreSQL 15+ (主数据库)
- **缓存**: Redis 7.0+ (缓存和消息队列)
- **ORM**: SQLAlchemy 2.0+
- **迁移**: Alembic
- **任务队列**: Celery 5.3+
- **AI框架**: LangGraph 0.1+
- **大模型**: Qwen-32B (通过API调用)
- **嵌入模型**: bge-large-zh-v1.5 (硅基流动接口)
- **文档处理**: python-docx
- **数据验证**: Pydantic 2.6+
- **测试框架**: pytest 8.0+

### 前端技术栈 (需要升级)
**当前状态**: React 19 + TypeScript 5 + Vite 6
**需要添加**:
- **UI框架**: Ant Design 5+ (替代Ant Design Pro，更轻量)
- **状态管理**: Zustand (替代Redux Toolkit，更简单)
- **数据获取**: TanStack Query (React Query)
- **路由**: React Router 6+
- **样式**: Tailwind CSS 3+ (可选，与Ant Design配合)
- **图表**: ECharts 5+ (数据可视化)
- **测试**: Vitest (替代Jest，与Vite更好集成)

### 基础设施
- **容器化**: Docker + Docker Compose
- **数据库**: PostgreSQL 15+
- **缓存**: Redis 7.0+
- **向量数据库**: FAISS (本地) / Milvus (生产环境)
- **文件存储**: 本地文件系统 (MVP阶段)

---

## 📁 项目目录结构确认

### 后端目录结构 (基于现有结构优化)
```
backend/
├── app.py                     # FastAPI应用入口 (已存在)
├── config.py                  # 配置文件 (需完善)
├── dependencies.py            # 依赖注入 (已存在)
├── extensions.py              # 扩展初始化 (已存在)
├── models/                    # 数据模型 (已存在目录)
│   ├── __init__.py
│   ├── core.py               # 核心模型
│   ├── template.py           # 模板模型
│   ├── data.py               # 数据模型
│   └── report.py             # 报告模型
├── routers/                   # 路由处理 (已存在目录)
│   ├── __init__.py
│   ├── uploading.py          # 文件上传接口
│   ├── extraction.py         # 信息抽取接口
│   └── reportgen.py          # 报告生成接口
├── schemas/                   # 数据结构 (已存在目录)
│   ├── __init__.py
│   └── core.py               # 请求响应结构
├── api/                       # AI服务 (已存在目录)
│   ├── client/               # 模型客户端
│   ├── agent/                # 智能体
│   ├── tool/                 # 工具函数
│   └── prompt/               # 提示词管理
├── utils/                     # 工具函数 (已存在目录)
│   ├── docx.py               # Word文档处理
│   ├── extract.py            # 信息抽取
│   └── core.py               # 核心工具
├── tests/                     # 测试 (已存在目录)
├── requirements/              # 依赖管理 (已存在目录)
└── pyproject.toml            # 项目配置 (已存在)
```

### 前端目录结构 (需要扩展)
```
frontend/
├── src/
│   ├── components/           # 组件
│   │   ├── common/          # 通用组件
│   │   ├── template/        # 模板组件
│   │   ├── data/            # 数据组件
│   │   └── report/          # 报告组件
│   ├── pages/               # 页面组件
│   │   ├── Dashboard/       # 仪表板
│   │   ├── Template/        # 模板管理
│   │   ├── Data/            # 数据管理
│   │   └── Report/          # 报告生成
│   ├── services/            # API服务
│   ├── hooks/               # 自定义Hooks
│   ├── stores/              # 状态管理
│   ├── utils/               # 工具函数
│   ├── types/               # TypeScript类型定义
│   └── assets/              # 静态资源
├── public/                  # 公共资源
└── package.json            # 依赖配置 (已存在)
```

---

## 🔧 开发环境要求

### 必需软件
- **Python**: 3.12+ (已确认)
- **Node.js**: 18+ (建议升级到20+)
- **Docker**: 最新版本
- **Docker Compose**: V2
- **PostgreSQL**: 15+ (通过Docker)
- **Redis**: 7.0+ (通过Docker)

### 开发工具
- **IDE**: VSCode (推荐插件: Python, TypeScript, Docker)
- **API测试**: Postman / Thunder Client
- **数据库管理**: pgAdmin / DBeaver
- **版本控制**: Git

---

## 🚀 核心功能模块确认

### 1. 模板管理模块
- **功能**: 模板上传、存储、解析、版本管理
- **技术**: FastAPI + SQLAlchemy + python-docx
- **占位符格式**: `{{字段名}}` + JSON Schema描述

### 2. 数据管理模块  
- **功能**: 文件上传、内容抽取、数据预览、验证
- **技术**: FastAPI + AI模型 + 文件处理

### 3. 报告生成模块
- **功能**: 模板选择、数据填充、Word生成、下载
- **技术**: python-docx + 模板引擎

### 4. AI服务模块
- **功能**: 信息抽取、内容生成、质量检查
- **技术**: LangGraph + Qwen-32B + 向量数据库

---

## 📊 数据库设计要点

### 核心表结构
1. **templates** - 模板信息表
2. **template_fields** - 模板字段表  
3. **data_sources** - 数据源表
4. **extracted_data** - 抽取数据表
5. **reports** - 报告记录表
6. **report_history** - 报告历史表

### 关系设计
- 模板与字段: 一对多
- 数据源与抽取数据: 一对多  
- 模板与报告: 一对多

---

## ⚠️ 技术风险与应对

### 主要风险
1. **AI模型集成复杂度** - 分阶段实现，先基础后智能
2. **Word文档处理兼容性** - 充分测试，建立样本库
3. **前端UI框架学习成本** - 选择文档完善的Ant Design
4. **数据库性能** - 合理索引设计，分页查询

### 应对策略
- 采用MVP开发模式，核心功能优先
- 建立完善的测试用例
- 预留技术调研和学习时间
- 及时技术评审和风险识别

---

## 📈 开发规范确认

### 代码规范
- **Python**: PEP 8 + Black格式化
- **TypeScript**: ESLint + Prettier
- **提交规范**: Conventional Commits
- **分支策略**: Git Flow

### 质量保障
- **测试覆盖率**: >80%
- **代码审查**: 必须
- **文档更新**: 同步
- **性能监控**: 集成

---

## ✅ 确认清单

- [x] 技术栈选型确认
- [x] 项目结构设计确认  
- [x] 开发环境要求明确
- [x] 核心功能模块划分
- [x] 数据库设计要点
- [x] 风险识别与应对
- [x] 开发规范制定

---

**下一步行动**: 开始任务1.2 - 开发环境搭建

*本文档作为项目技术架构的最终确认，后续开发将严格按照此架构执行*
