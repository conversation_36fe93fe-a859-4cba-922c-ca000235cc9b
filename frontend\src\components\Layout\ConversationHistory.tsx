/**
 * 历史对话栏组件
 */

import React, { useState, useEffect } from 'react';
import { 
  List, 
  Card, 
  Typography, 
  Button, 
  Space, 
  Tag, 
  Tooltip, 
  Empty,
  Input,
  // Divider
} from 'antd';
import {
  MessageOutlined,
  ClockCircleOutlined,
  // SearchOutlined,
  PlusOutlined,
  DeleteOutlined,
  // EditOutlined
} from '@ant-design/icons';
// import { formatDistanceToNow } from 'date-fns';
// import { zhCN } from 'date-fns/locale';

const { Text, Title } = Typography;
const { Search } = Input;

interface Conversation {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: Date;
  status: 'active' | 'completed' | 'error';
  messageCount: number;
}

interface ConversationHistoryProps {
  collapsed: boolean;
}

const ConversationHistory: React.FC<ConversationHistoryProps> = ({ collapsed }) => {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [searchText, setSearchText] = useState('');
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);

  // 模拟对话数据
  useEffect(() => {
    const mockConversations: Conversation[] = [
      {
        id: '1',
        title: '会议纪要生成 - 数字科技中心',
        lastMessage: '报告已生成完成，请查看结果',
        timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30分钟前
        status: 'completed',
        messageCount: 8
      },
      {
        id: '2',
        title: '项目预审会纪要',
        lastMessage: '正在提取关键信息...',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2小时前
        status: 'active',
        messageCount: 5
      },
      {
        id: '3',
        title: '住建厅会议记录处理',
        lastMessage: '模板识别失败，请重新选择',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1天前
        status: 'error',
        messageCount: 3
      }
    ];
    setConversations(mockConversations);
  }, []);

  const filteredConversations = conversations.filter(conv =>
    conv.title.toLowerCase().includes(searchText.toLowerCase()) ||
    conv.lastMessage.toLowerCase().includes(searchText.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'processing';
      case 'completed': return 'success';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '进行中';
      case 'completed': return '已完成';
      case 'error': return '错误';
      default: return '未知';
    }
  };

  const handleNewConversation = () => {
    // 创建新对话的逻辑
    console.log('创建新对话');
  };

  const handleDeleteConversation = (id: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setConversations(prev => prev.filter(conv => conv.id !== id));
  };

  if (collapsed) {
    return null;
  }

  return (
    <div style={{ 
      height: '100vh', 
      display: 'flex', 
      flexDirection: 'column',
      background: '#fafafa'
    }}>
      {/* 标题栏 */}
      <div style={{ 
        padding: '16px', 
        borderBottom: '1px solid #f0f0f0',
        background: '#fff'
      }}>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          marginBottom: '12px'
        }}>
          <Title level={5} style={{ margin: 0 }}>
            <MessageOutlined style={{ marginRight: '8px' }} />
            对话历史
          </Title>
          <Tooltip title="新建对话">
            <Button 
              type="text" 
              icon={<PlusOutlined />} 
              size="small"
              onClick={handleNewConversation}
            />
          </Tooltip>
        </div>
        
        <Search
          placeholder="搜索对话..."
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: '100%' }}
          size="small"
        />
      </div>

      {/* 对话列表 */}
      <div style={{ flex: 1, overflow: 'auto', padding: '8px' }}>
        {filteredConversations.length === 0 ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="暂无对话记录"
            style={{ marginTop: '60px' }}
          />
        ) : (
          <List
            dataSource={filteredConversations}
            renderItem={(conversation) => (
              <List.Item style={{ padding: 0, marginBottom: '8px' }}>
                <Card
                  size="small"
                  hoverable
                  style={{
                    width: '100%',
                    cursor: 'pointer',
                    border: selectedConversation === conversation.id ? '2px solid #1890ff' : '1px solid #f0f0f0'
                  }}
                  onClick={() => setSelectedConversation(conversation.id)}
                  bodyStyle={{ padding: '12px' }}
                >
                  <div style={{ marginBottom: '8px' }}>
                    <div style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      alignItems: 'flex-start',
                      marginBottom: '4px'
                    }}>
                      <Text strong style={{ 
                        fontSize: '13px',
                        lineHeight: '1.4',
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        flex: 1,
                        marginRight: '8px'
                      }}>
                        {conversation.title}
                      </Text>
                      <Tooltip title="删除对话">
                        <Button
                          type="text"
                          icon={<DeleteOutlined />}
                          size="small"
                          style={{ 
                            color: '#999',
                            minWidth: 'auto',
                            width: '20px',
                            height: '20px',
                            padding: 0
                          }}
                          onClick={(e) => handleDeleteConversation(conversation.id, e)}
                        />
                      </Tooltip>
                    </div>
                    
                    <Text 
                      type="secondary" 
                      style={{ 
                        fontSize: '12px',
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        lineHeight: '1.3'
                      }}
                    >
                      {conversation.lastMessage}
                    </Text>
                  </div>
                  
                  <div style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center' 
                  }}>
                    <Tag 
                      color={getStatusColor(conversation.status)} 
                      style={{ fontSize: '11px', margin: 0 }}
                    >
                      {getStatusText(conversation.status)}
                    </Tag>
                    
                    <Space size={4}>
                      <Text type="secondary" style={{ fontSize: '11px' }}>
                        {conversation.messageCount}条
                      </Text>
                      <Text type="secondary" style={{ fontSize: '11px' }}>
                        <ClockCircleOutlined style={{ marginRight: '2px' }} />
                        {new Date(conversation.timestamp).toLocaleString('zh-CN', {
                          month: 'short',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </Text>
                    </Space>
                  </div>
                </Card>
              </List.Item>
            )}
          />
        )}
      </div>
    </div>
  );
};

export default ConversationHistory;
