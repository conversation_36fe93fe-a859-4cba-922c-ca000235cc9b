"""
数据库持久化检查点存储器
基于LangGraph的Checkpointer接口实现数据库持久化存储
"""

import logging
import json
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple, Iterator, Union
from contextlib import contextmanager

from langgraph.checkpoint.base import BaseCheckpointSaver, Checkpoint, CheckpointMetadata
from langgraph.checkpoint.serde.jsonplus import JsonPlusSerializer
from sqlalchemy.orm import Session
from sqlalchemy import desc

from database import get_db
from models.dialogue import Conversation, ConversationCheckpoint
from services.dialogue_service import dialogue_service

# 配置日志
logger = logging.getLogger(__name__)


class DatabaseCheckpointer(BaseCheckpointSaver):
    """
    数据库持久化检查点存储器
    实现LangGraph的BaseCheckpointSaver接口，将检查点数据持久化到数据库
    """

    def __init__(self, serde: Optional[JsonPlusSerializer] = None):
        """
        初始化数据库检查点存储器
        
        Args:
            serde: 序列化器，默认使用JsonPlusSerializer
        """
        super().__init__(serde=serde or JsonPlusSerializer())
        self.db_session = None
        logger.info("数据库检查点存储器初始化完成")

    @contextmanager
    def _get_db_session(self):
        """获取数据库会话的上下文管理器"""
        if self.db_session:
            yield self.db_session
        else:
            db = next(get_db())
            try:
                yield db
            finally:
                db.close()

    def _ensure_conversation_exists(self, thread_id: str, db: Session) -> Conversation:
        """
        确保对话存在，如果不存在则创建
        
        Args:
            thread_id: 线程ID
            db: 数据库会话
            
        Returns:
            Conversation: 对话对象
        """
        conversation = db.query(Conversation).filter(
            Conversation.thread_id == thread_id
        ).first()
        
        if not conversation:
            conversation = Conversation(
                thread_id=thread_id,
                title=f"对话 {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                status="active",
                interaction_type="chat"
            )
            db.add(conversation)
            db.commit()
            db.refresh(conversation)
            logger.info(f"创建新对话: {conversation.id} (thread_id: {thread_id})")
        
        return conversation

    def put(
        self,
        config: Dict[str, Any],
        checkpoint: Checkpoint,
        metadata: CheckpointMetadata,
        new_versions: Dict[str, Union[str, int]]
    ) -> Dict[str, Any]:
        """
        保存检查点到数据库
        
        Args:
            config: 配置信息
            checkpoint: 检查点数据
            metadata: 检查点元数据
            new_versions: 新版本信息
            
        Returns:
            Dict[str, Any]: 保存结果
        """
        try:
            thread_id = config.get("configurable", {}).get("thread_id")
            if not thread_id:
                raise ValueError("thread_id is required in config")

            with self._get_db_session() as db:
                # 确保对话存在
                conversation = self._ensure_conversation_exists(thread_id, db)
                
                # 序列化检查点数据
                checkpoint_data = self.serde.dumps(checkpoint)
                metadata_data = self.serde.dumps(metadata)
                
                # 获取当前最大步骤号
                last_checkpoint = db.query(ConversationCheckpoint).filter(
                    ConversationCheckpoint.conversation_id == conversation.id
                ).order_by(desc(ConversationCheckpoint.step_number)).first()
                
                step_number = 1
                if last_checkpoint:
                    step_number = last_checkpoint.step_number + 1
                
                # 创建检查点记录
                db_checkpoint = ConversationCheckpoint(
                    conversation_id=conversation.id,
                    checkpoint_id=checkpoint.get("id", str(uuid.uuid4())),
                    parent_checkpoint_id=checkpoint.get("parent_id"),
                    checkpoint_data={
                        "checkpoint": checkpoint_data,
                        "metadata": metadata_data,
                        "config": config,
                        "new_versions": new_versions
                    },
                    step_number=step_number,
                    node_name=metadata.get("step", {}).get("node"),
                    status="completed"
                )
                
                db.add(db_checkpoint)
                db.commit()
                db.refresh(db_checkpoint)
                
                logger.debug(f"保存检查点: {db_checkpoint.checkpoint_id} (step: {step_number})")
                
                return {
                    "configurable": {
                        "thread_id": thread_id,
                        "checkpoint_id": db_checkpoint.checkpoint_id
                    }
                }
                
        except Exception as e:
            logger.error(f"保存检查点失败: {e}")
            raise

    def get_tuple(self, config: Dict[str, Any]) -> Optional[Tuple[Checkpoint, CheckpointMetadata]]:
        """
        获取检查点元组
        
        Args:
            config: 配置信息
            
        Returns:
            Optional[Tuple[Checkpoint, CheckpointMetadata]]: 检查点元组或None
        """
        try:
            thread_id = config.get("configurable", {}).get("thread_id")
            checkpoint_id = config.get("configurable", {}).get("checkpoint_id")
            
            if not thread_id:
                return None

            with self._get_db_session() as db:
                conversation = db.query(Conversation).filter(
                    Conversation.thread_id == thread_id
                ).first()
                
                if not conversation:
                    return None
                
                # 构建查询
                query = db.query(ConversationCheckpoint).filter(
                    ConversationCheckpoint.conversation_id == conversation.id
                )
                
                if checkpoint_id:
                    query = query.filter(ConversationCheckpoint.checkpoint_id == checkpoint_id)
                else:
                    # 获取最新的检查点
                    query = query.order_by(desc(ConversationCheckpoint.step_number))
                
                db_checkpoint = query.first()
                
                if not db_checkpoint:
                    return None
                
                # 反序列化数据
                checkpoint_data = db_checkpoint.checkpoint_data
                checkpoint = self.serde.loads(checkpoint_data["checkpoint"])
                metadata = self.serde.loads(checkpoint_data["metadata"])
                
                logger.debug(f"获取检查点: {db_checkpoint.checkpoint_id}")
                return (checkpoint, metadata)
                
        except Exception as e:
            logger.error(f"获取检查点失败: {e}")
            return None

    def list(
        self,
        config: Dict[str, Any],
        *,
        filter: Optional[Dict[str, Any]] = None,
        before: Optional[Dict[str, Any]] = None,
        limit: Optional[int] = None
    ) -> Iterator[Tuple[Dict[str, Any], Checkpoint, CheckpointMetadata]]:
        """
        列出检查点
        
        Args:
            config: 配置信息
            filter: 过滤条件
            before: 之前的检查点
            limit: 限制数量
            
        Yields:
            Tuple[Dict[str, Any], Checkpoint, CheckpointMetadata]: 检查点信息
        """
        try:
            thread_id = config.get("configurable", {}).get("thread_id")
            if not thread_id:
                return

            with self._get_db_session() as db:
                conversation = db.query(Conversation).filter(
                    Conversation.thread_id == thread_id
                ).first()
                
                if not conversation:
                    return
                
                # 构建查询
                query = db.query(ConversationCheckpoint).filter(
                    ConversationCheckpoint.conversation_id == conversation.id
                ).order_by(desc(ConversationCheckpoint.step_number))
                
                if limit:
                    query = query.limit(limit)
                
                for db_checkpoint in query:
                    try:
                        # 反序列化数据
                        checkpoint_data = db_checkpoint.checkpoint_data
                        checkpoint = self.serde.loads(checkpoint_data["checkpoint"])
                        metadata = self.serde.loads(checkpoint_data["metadata"])
                        
                        config_result = {
                            "configurable": {
                                "thread_id": thread_id,
                                "checkpoint_id": db_checkpoint.checkpoint_id
                            }
                        }
                        
                        yield (config_result, checkpoint, metadata)
                        
                    except Exception as e:
                        logger.error(f"反序列化检查点失败: {e}")
                        continue
                        
        except Exception as e:
            logger.error(f"列出检查点失败: {e}")

    def delete_thread(self, thread_id: str) -> None:
        """
        删除线程的所有检查点

        Args:
            thread_id: 线程ID
        """
        try:
            if not thread_id:
                return

            with self._get_db_session() as db:
                conversation = db.query(Conversation).filter(
                    Conversation.thread_id == thread_id
                ).first()

                if not conversation:
                    return

                # 删除所有检查点
                deleted_count = db.query(ConversationCheckpoint).filter(
                    ConversationCheckpoint.conversation_id == conversation.id
                ).delete()

                db.commit()

                logger.info(f"删除线程 {thread_id} 的检查点: {deleted_count} 条记录")

        except Exception as e:
            logger.error(f"删除线程检查点失败: {e}")
            raise

    def put_writes(
        self,
        config: Dict[str, Any],
        writes: List[Tuple[str, Any]],
        task_id: str
    ) -> None:
        """
        存储中间写入数据

        Args:
            config: 配置信息
            writes: 写入数据列表
            task_id: 任务ID
        """
        # 这个方法在我们的实现中暂时不需要特殊处理
        # 因为我们主要关注检查点的持久化
        logger.debug(f"put_writes called with {len(writes)} writes for task {task_id}")

    # 异步方法实现
    async def aget_tuple(self, config: Dict[str, Any]) -> Optional[Tuple[Checkpoint, CheckpointMetadata]]:
        """异步获取检查点元组"""
        return self.get_tuple(config)

    async def aput(
        self,
        config: Dict[str, Any],
        checkpoint: Checkpoint,
        metadata: CheckpointMetadata,
        new_versions: Dict[str, Union[str, int]]
    ) -> Dict[str, Any]:
        """异步保存检查点"""
        return self.put(config, checkpoint, metadata, new_versions)

    async def alist(
        self,
        config: Dict[str, Any],
        *,
        filter: Optional[Dict[str, Any]] = None,
        before: Optional[Dict[str, Any]] = None,
        limit: Optional[int] = None
    ) -> Iterator[Tuple[Dict[str, Any], Checkpoint, CheckpointMetadata]]:
        """异步列出检查点"""
        for item in self.list(config, filter=filter, before=before, limit=limit):
            yield item

    async def aput_writes(
        self,
        config: Dict[str, Any],
        writes: List[Tuple[str, Any]],
        task_id: str
    ) -> None:
        """异步存储中间写入数据"""
        self.put_writes(config, writes, task_id)


# 创建全局数据库检查点存储器实例
database_checkpointer = DatabaseCheckpointer()
