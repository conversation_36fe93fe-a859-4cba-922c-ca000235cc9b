/**
 * 主工作区组件
 * 根据当前选中的标签页显示不同的内容
 */

import React from 'react';
import { Layout, Button, Typography } from 'antd';
import { MenuUnfoldOutlined, MenuFoldOutlined } from '@ant-design/icons';

// 导入各个功能页面组件
import MainPage from '../Pages/MainPage';
import TemplateManagement from '../Pages/TemplateManagement';
import DataManagement from '../Pages/DataManagement';
import ReportManagement from '../Pages/ReportManagement';
import AgentManagement from '../Pages/AgentManagement';
import SystemSettings from '../Pages/SystemSettings';

const { Header, Content } = Layout;
const { Title } = Typography;

interface MainWorkspaceProps {
  currentTab: string;
  onToggleConversation: () => void;
  conversationCollapsed: boolean;
}

const MainWorkspace: React.FC<MainWorkspaceProps> = ({
  currentTab,
  onToggleConversation,
  conversationCollapsed
}) => {
  const getPageTitle = (tab: string) => {
    switch (tab) {
      case 'main': return '报告智能生成工具';
      case 'templates': return '模板管理';
      case 'data': return '数据管理';
      case 'reports': return '报告管理';
      case 'agents': return '智能体管理';
      case 'settings': return '系统设置';
      default: return '报告智能生成工具';
    }
  };

  const renderContent = () => {
    switch (currentTab) {
      case 'main':
        return <MainPage />;
      case 'templates':
        return <TemplateManagement />;
      case 'data':
        return <DataManagement />;
      case 'reports':
        return <ReportManagement />;
      case 'agents':
        return <AgentManagement />;
      case 'settings':
        return <SystemSettings />;
      default:
        return <MainPage />;
    }
  };

  return (
    <Layout style={{ height: '100%' }}>
      {/* 工作区头部 */}
      <Header style={{
        background: '#fff',
        padding: '0 24px',
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        height: '64px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Button
            type="text"
            icon={conversationCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={onToggleConversation}
            style={{ marginRight: '16px' }}
          />
          <Title level={4} style={{ margin: 0 }}>
            {getPageTitle(currentTab)}
          </Title>
        </div>
      </Header>

      {/* 工作区内容 */}
      <Content style={{
        padding: '24px',
        background: '#f5f5f5',
        overflow: 'auto',
        flex: 1
      }}>
        {renderContent()}
      </Content>
    </Layout>
  );
};

export default MainWorkspace;
