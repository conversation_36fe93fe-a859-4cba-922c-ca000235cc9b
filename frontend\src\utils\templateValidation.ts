/**
 * 模板验证前端工具
 * 提供模板验证相关的前端功能
 */

export interface ValidationIssue {
  level: 'error' | 'warning' | 'info';
  type: string;
  message: string;
  location?: string;
  suggestion?: string;
  field_name?: string;
}

export interface PlaceholderInfo {
  placeholder_text: string;
  field_name: string;
  pattern_type: string;
  location: string;
  context: string;
  line_number?: number;
}

export interface FieldDefinition {
  field_name: string;
  description: string;
  example: string;
  format: string;
  default: string;
  length?: number;
  source: string;
  must: boolean;
}

export interface ValidationResult {
  is_valid: boolean;
  issues: ValidationIssue[];
  placeholders: PlaceholderInfo[];
  field_definitions: FieldDefinition[];
  
  total_placeholders: number;
  matched_placeholders: number;
  unmatched_placeholders: number;
  missing_definitions: number;
  unused_definitions: number;
  
  consistency_score: number;
  completeness_score: number;
  overall_score: number;
  
  suggestions: string[];
  summary: any;
}

/**
 * 模板验证API客户端
 */
export class TemplateValidationClient {
  private baseUrl: string;

  constructor(baseUrl: string = 'http://localhost:8000') {
    this.baseUrl = baseUrl;
  }

  /**
   * 验证上传的模板文件和关键字段定义文件
   */
  async validateUploadedFiles(
    templateFile: File,
    keyDefinitionsFile: File
  ): Promise<ValidationResult> {
    const formData = new FormData();
    formData.append('template_file', templateFile);
    formData.append('key_definitions_file', keyDefinitionsFile);

    const response = await fetch(
      `${this.baseUrl}/api/template-validation/validate-upload`,
      {
        method: 'POST',
        body: formData,
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || '模板验证失败');
    }

    return response.json();
  }

  /**
   * 验证指定路径的模板文件
   */
  async validateFiles(
    templatePath: string,
    keyDefinitionsPath: string
  ): Promise<ValidationResult> {
    const response = await fetch(
      `${this.baseUrl}/api/template-validation/validate-files`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          template_path: templatePath,
          key_definitions_path: keyDefinitionsPath,
        }),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || '模板验证失败');
    }

    return response.json();
  }

  /**
   * 从模板文件中提取占位符
   */
  async extractPlaceholders(templateFile: File): Promise<{
    placeholders: PlaceholderInfo[];
    statistics: {
      total_placeholders: number;
      unique_fields: number;
      pattern_distribution: Record<string, number>;
    };
  }> {
    const formData = new FormData();
    formData.append('template_file', templateFile);

    const response = await fetch(
      `${this.baseUrl}/api/template-validation/extract-placeholders`,
      {
        method: 'POST',
        body: formData,
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || '占位符提取失败');
    }

    const result = await response.json();
    return result.data;
  }

  /**
   * 解析关键字段定义文件
   */
  async parseKeyDefinitions(keyDefinitionsFile: File): Promise<{
    field_definitions: FieldDefinition[];
    statistics: {
      total_fields: number;
      required_fields: number;
      optional_fields: number;
      format_distribution: Record<string, number>;
      source_distribution: Record<string, number>;
    };
  }> {
    const formData = new FormData();
    formData.append('key_definitions_file', keyDefinitionsFile);

    const response = await fetch(
      `${this.baseUrl}/api/template-validation/parse-key-definitions`,
      {
        method: 'POST',
        body: formData,
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || '字段定义解析失败');
    }

    const result = await response.json();
    return result.data;
  }

  /**
   * 根据关键字段定义生成模板骨架
   */
  async generateTemplateSkeleton(
    keyDefinitionsFile: File,
    placeholderFormat: string = 'double_brace'
  ): Promise<{
    template_skeleton: string;
    placeholder_format: string;
    total_fields: number;
    required_fields: number;
  }> {
    const formData = new FormData();
    formData.append('key_definitions_file', keyDefinitionsFile);
    formData.append('placeholder_format', placeholderFormat);

    const response = await fetch(
      `${this.baseUrl}/api/template-validation/generate-template-skeleton`,
      {
        method: 'POST',
        body: formData,
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || '模板骨架生成失败');
    }

    const result = await response.json();
    return result.data;
  }

  /**
   * 获取验证规则说明
   */
  async getValidationRules(): Promise<any> {
    const response = await fetch(
      `${this.baseUrl}/api/template-validation/validation-rules`
    );

    if (!response.ok) {
      throw new Error('获取验证规则失败');
    }

    const result = await response.json();
    return result.data;
  }
}

/**
 * 模板验证结果格式化工具
 */
export class ValidationResultFormatter {
  /**
   * 格式化验证评分
   */
  static formatScore(score: number): string {
    const percentage = Math.round(score * 100);
    if (percentage >= 90) return `${percentage}% 优秀`;
    if (percentage >= 80) return `${percentage}% 良好`;
    if (percentage >= 70) return `${percentage}% 一般`;
    if (percentage >= 60) return `${percentage}% 较差`;
    return `${percentage}% 很差`;
  }

  /**
   * 获取评分颜色
   */
  static getScoreColor(score: number): string {
    if (score >= 0.9) return '#52c41a'; // 绿色
    if (score >= 0.8) return '#1890ff'; // 蓝色
    if (score >= 0.7) return '#faad14'; // 橙色
    if (score >= 0.6) return '#fa8c16'; // 深橙色
    return '#f5222d'; // 红色
  }

  /**
   * 获取问题等级图标
   */
  static getIssueIcon(level: string): string {
    switch (level) {
      case 'error': return '🔴';
      case 'warning': return '🟡';
      case 'info': return '🔵';
      default: return '❓';
    }
  }

  /**
   * 获取占位符模式名称
   */
  static getPatternDisplayName(patternType: string): string {
    const patterns = {
      'double_brace': '双花括号 {{}}',
      'single_brace': '单花括号 {}',
      'square_bracket': '方括号 []',
      'angle_bracket': '尖括号 <>',
    };
    return patterns[patternType] || patternType;
  }

  /**
   * 格式化字段格式类型
   */
  static formatFieldFormat(format: string): string {
    const formatNames = {
      '普通文本': '📝 普通文本',
      '表格行': '📊 表格行',
      '段落': '📄 段落',
      '人名': '👤 人名',
      '单独行': '📋 单独行',
      '行单元格': '🔲 行单元格',
      '日期时间': '📅 日期时间',
      '百分比': '📈 百分比',
    };
    return formatNames[format] || format;
  }

  /**
   * 生成验证报告摘要
   */
  static generateSummary(result: ValidationResult): string {
    const lines = [];
    
    lines.push(`📊 **验证结果**: ${result.is_valid ? '✅ 通过' : '❌ 未通过'}`);
    lines.push(`📈 **总体评分**: ${this.formatScore(result.overall_score)}`);
    lines.push(`🔗 **一致性**: ${this.formatScore(result.consistency_score)}`);
    lines.push(`📝 **完整性**: ${this.formatScore(result.completeness_score)}`);
    lines.push('');
    
    lines.push(`📋 **统计信息**:`);
    lines.push(`  • 占位符总数: ${result.total_placeholders}`);
    lines.push(`  • 匹配占位符: ${result.matched_placeholders}`);
    lines.push(`  • 字段定义: ${result.field_definitions.length}`);
    lines.push(`  • 未使用定义: ${result.unused_definitions}`);
    
    if (result.issues.length > 0) {
      const errorCount = result.issues.filter(i => i.level === 'error').length;
      const warningCount = result.issues.filter(i => i.level === 'warning').length;
      
      lines.push('');
      lines.push(`⚠️ **问题统计**:`);
      lines.push(`  • 错误: ${errorCount} 个`);
      lines.push(`  • 警告: ${warningCount} 个`);
    }
    
    return lines.join('\n');
  }
}

/**
 * 模板验证状态管理
 */
export class ValidationStateManager {
  private validationResults: Map<string, ValidationResult> = new Map();
  private lastValidationTime: Map<string, number> = new Map();

  /**
   * 缓存验证结果
   */
  cacheResult(key: string, result: ValidationResult): void {
    this.validationResults.set(key, result);
    this.lastValidationTime.set(key, Date.now());
  }

  /**
   * 获取缓存的验证结果
   */
  getCachedResult(key: string, maxAge: number = 5 * 60 * 1000): ValidationResult | null {
    const result = this.validationResults.get(key);
    const timestamp = this.lastValidationTime.get(key);
    
    if (result && timestamp && (Date.now() - timestamp) < maxAge) {
      return result;
    }
    
    return null;
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.validationResults.clear();
    this.lastValidationTime.clear();
  }

  /**
   * 生成缓存键
   */
  static generateCacheKey(templateFileName: string, keyDefFileName: string): string {
    return `${templateFileName}_${keyDefFileName}`;
  }
}

// 导出默认实例
export const templateValidationClient = new TemplateValidationClient();
export const validationStateManager = new ValidationStateManager();