/**
 * 模板管理页面
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Upload,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Statistic
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UploadOutlined,
  FileTextOutlined,
  EyeOutlined,
  DownloadOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface Template {
  id: string;
  name: string;
  category: string;
  description: string;
  version: string;
  placeholderCount: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  fileSize: number;
}

const TemplateManagement: React.FC = () => {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<Template | null>(null);
  const [form] = Form.useForm();

  // 模拟数据
  useEffect(() => {
    const mockTemplates: Template[] = [
      {
        id: '1',
        name: '数字科技中心会议纪要',
        category: '会议纪要',
        description: '用于生成数字科技中心会议纪要的标准模板',
        version: '1.0',
        placeholderCount: 15,
        isActive: true,
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-01-20'),
        fileSize: 25600
      },
      {
        id: '2',
        name: '项目预审会纪要',
        category: '会议纪要',
        description: '项目预审会议纪要模板',
        version: '1.1',
        placeholderCount: 12,
        isActive: true,
        createdAt: new Date('2024-01-10'),
        updatedAt: new Date('2024-01-18'),
        fileSize: 22400
      },
      {
        id: '3',
        name: '技术评审报告',
        category: '评审报告',
        description: '技术方案评审报告模板',
        version: '2.0',
        placeholderCount: 20,
        isActive: false,
        createdAt: new Date('2024-01-05'),
        updatedAt: new Date('2024-01-15'),
        fileSize: 31200
      }
    ];
    setTemplates(mockTemplates);
  }, []);

  const columns = [
    {
      title: '模板名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Template) => (
        <Space>
          <FileTextOutlined style={{ color: '#1890ff' }} />
          <span style={{ fontWeight: 500 }}>{text}</span>
          {!record.isActive && <Tag color="red">已停用</Tag>}
        </Space>
      ),
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      render: (category: string) => <Tag color="blue">{category}</Tag>,
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
    },
    {
      title: '字段数量',
      dataIndex: 'placeholderCount',
      key: 'placeholderCount',
      render: (count: number) => <Text type="secondary">{count} 个字段</Text>,
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      key: 'fileSize',
      render: (size: number) => <Text type="secondary">{(size / 1024).toFixed(1)} KB</Text>,
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '停用'}
        </Tag>
      ),
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (date: Date) => date.toLocaleDateString('zh-CN'),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: Template) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewTemplate(record)}
          >
            查看
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditTemplate(record)}
          >
            编辑
          </Button>
          <Button
            type="text"
            icon={<DownloadOutlined />}
            onClick={() => handleDownloadTemplate(record)}
          >
            下载
          </Button>
          <Popconfirm
            title="确定要删除这个模板吗？"
            onConfirm={() => handleDeleteTemplate(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleAddTemplate = () => {
    setEditingTemplate(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEditTemplate = (template: Template) => {
    setEditingTemplate(template);
    form.setFieldsValue(template);
    setModalVisible(true);
  };

  const handleViewTemplate = (template: Template) => {
    message.info(`查看模板: ${template.name}`);
  };

  const handleDownloadTemplate = (template: Template) => {
    message.success(`下载模板: ${template.name}`);
  };

  const handleDeleteTemplate = (id: string) => {
    setTemplates(prev => prev.filter(t => t.id !== id));
    message.success('模板删除成功');
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingTemplate) {
        // 编辑模板
        setTemplates(prev => prev.map(t => 
          t.id === editingTemplate.id 
            ? { ...t, ...values, updatedAt: new Date() }
            : t
        ));
        message.success('模板更新成功');
      } else {
        // 新增模板
        const newTemplate: Template = {
          id: Date.now().toString(),
          ...values,
          placeholderCount: 0,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
          fileSize: 0
        };
        setTemplates(prev => [...prev, newTemplate]);
        message.success('模板创建成功');
      }
      
      setModalVisible(false);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleModalCancel = () => {
    setModalVisible(false);
  };

  // 统计数据
  const stats = {
    total: templates.length,
    active: templates.filter(t => t.isActive).length,
    categories: new Set(templates.map(t => t.category)).size,
    totalFields: templates.reduce((sum, t) => sum + t.placeholderCount, 0)
  };

  return (
    <div>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic title="总模板数" value={stats.total} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="启用模板" value={stats.active} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="模板分类" value={stats.categories} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="总字段数" value={stats.totalFields} />
          </Card>
        </Col>
      </Row>

      {/* 模板列表 */}
      <Card
        title={
          <Space>
            <FileTextOutlined />
            <span>模板列表</span>
          </Space>
        }
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddTemplate}
          >
            新增模板
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={templates}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个模板`,
          }}
        />
      </Card>

      {/* 新增/编辑模板弹窗 */}
      <Modal
        title={editingTemplate ? '编辑模板' : '新增模板'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            isActive: true,
            version: '1.0'
          }}
        >
          <Form.Item
            name="name"
            label="模板名称"
            rules={[{ required: true, message: '请输入模板名称' }]}
          >
            <Input placeholder="请输入模板名称" />
          </Form.Item>

          <Form.Item
            name="category"
            label="模板分类"
            rules={[{ required: true, message: '请选择模板分类' }]}
          >
            <Select placeholder="请选择模板分类">
              <Select.Option value="会议纪要">会议纪要</Select.Option>
              <Select.Option value="评审报告">评审报告</Select.Option>
              <Select.Option value="项目报告">项目报告</Select.Option>
              <Select.Option value="其他">其他</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="模板描述"
            rules={[{ required: true, message: '请输入模板描述' }]}
          >
            <TextArea rows={3} placeholder="请输入模板描述" />
          </Form.Item>

          <Form.Item
            name="version"
            label="版本号"
            rules={[{ required: true, message: '请输入版本号' }]}
          >
            <Input placeholder="请输入版本号，如：1.0" />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select>
              <Select.Option value={true}>启用</Select.Option>
              <Select.Option value={false}>停用</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="file"
            label="模板文件"
          >
            <Upload
              accept=".docx"
              beforeUpload={() => false}
              maxCount={1}
            >
              <Button icon={<UploadOutlined />}>选择模板文件</Button>
            </Upload>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TemplateManagement;
