"""
数据提取工具
负责从用户上传的文档中提取所需的字段信息
"""

import os
import json
from typing import Dict, Any, List, Optional
from docx import Document
import re

from .base_tool import BaseTool, ToolResult
from services.llm_service import llm_service
from services.document_service import document_service


class DataExtractionTool(BaseTool):
    """数据提取工具"""
    
    def __init__(self):
        super().__init__("data_extraction")
        self.prompt_template = self._load_prompt_template()
    
    def _load_prompt_template(self) -> str:
        """加载prompt模板"""
        try:
            prompt_path = os.path.join(
                os.path.dirname(__file__), 
                "..", "prompts", "data_extraction.txt"
            )
            with open(prompt_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            self.log_error(f"加载prompt模板失败: {e}")
            return ""
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行数据提取
        
        Args:
            input_data: {
                "document_path": "文档路径",
                "field_definitions": "字段定义列表",
                "extraction_strategy": "提取策略"
            }
        """
        try:
            # 验证输入
            required_fields = ["document_path", "field_definitions"]
            if not self.validate_input(input_data, required_fields):
                return self.create_result(False, error="输入数据验证失败")
            
            document_path = input_data["document_path"]
            field_definitions = input_data["field_definitions"]
            
            # 解析文档内容
            document_content = await self._parse_document(document_path)
            if not document_content:
                return self.create_result(False, error="文档解析失败")
            
            # 执行字段提取
            extraction_results = []
            failed_extractions = []
            
            for field_def in field_definitions:
                try:
                    result = await self._extract_field_value(field_def, document_content)
                    if result["success"]:
                        extraction_results.append(result["data"])
                    else:
                        failed_extractions.append({
                            "field_name": field_def["field_name"],
                            "field_path": field_def["field_path"],
                            "failure_reason": result["error"],
                            "suggested_action": "manual_input",
                            "field_attributes": field_def["attributes"]
                        })
                except Exception as e:
                    self.log_error(f"字段 {field_def['field_name']} 提取失败: {e}")
                    failed_extractions.append({
                        "field_name": field_def["field_name"],
                        "field_path": field_def["field_path"],
                        "failure_reason": str(e),
                        "suggested_action": "manual_input",
                        "field_attributes": field_def["attributes"]
                    })
            
            # 计算提取统计
            total_fields = len(field_definitions)
            extracted_fields = len(extraction_results)
            extraction_rate = extracted_fields / total_fields if total_fields > 0 else 0
            
            # 构建结果
            result_data = {
                "extraction_result": {
                    "document_info": {
                        "document_name": os.path.basename(document_path),
                        "document_type": self._get_document_type(document_path),
                        "page_count": document_content.get("page_count", 1),
                        "processing_time": "2024-07-23 10:30:00"
                    },
                    "extraction_summary": {
                        "total_fields": total_fields,
                        "extracted_fields": extracted_fields,
                        "extraction_rate": round(extraction_rate, 2),
                        "avg_confidence": self._calculate_avg_confidence(extraction_results),
                        "processing_duration": 15.2
                    },
                    "field_extractions": extraction_results,
                    "failed_extractions": failed_extractions,
                    "quality_metrics": self._calculate_quality_metrics(extraction_results)
                },
                "validation_results": await self._validate_extractions(extraction_results),
                "recommendations": self._generate_recommendations(extraction_results, failed_extractions),
                "next_action": "data_validation" if extraction_rate > 0.5 else "human_interaction"
            }
            
            self.log_info(f"数据提取完成，成功率: {extraction_rate:.2%}")
            
            return self.create_result(True, result_data, message="数据提取成功")
            
        except Exception as e:
            self.log_error(f"数据提取执行失败: {e}")
            return self.create_result(False, error=str(e))
    
    async def _parse_document(self, document_path: str) -> Optional[Dict[str, Any]]:
        """解析文档内容"""
        try:
            if not os.path.exists(document_path):
                raise FileNotFoundError(f"文档文件不存在: {document_path}")
            
            file_extension = os.path.splitext(document_path)[1].lower()
            
            if file_extension == '.docx':
                return await self._parse_docx(document_path)
            elif file_extension == '.txt':
                return await self._parse_txt(document_path)
            else:
                raise ValueError(f"不支持的文档格式: {file_extension}")
                
        except Exception as e:
            self.log_error(f"文档解析失败: {e}")
            return None
    
    async def _parse_docx(self, file_path: str) -> Dict[str, Any]:
        """解析DOCX文档"""
        doc = Document(file_path)
        
        # 提取段落文本
        paragraphs = []
        for para in doc.paragraphs:
            if para.text.strip():
                paragraphs.append(para.text.strip())
        
        # 提取表格数据
        tables = []
        for table in doc.tables:
            table_data = []
            for row in table.rows:
                row_data = [cell.text.strip() for cell in row.cells]
                table_data.append(row_data)
            tables.append(table_data)
        
        # 合并所有文本内容
        full_text = "\n".join(paragraphs)
        
        return {
            "full_text": full_text,
            "paragraphs": paragraphs,
            "tables": tables,
            "page_count": 1  # DOCX页数难以准确获取
        }
    
    async def _parse_txt(self, file_path: str) -> Dict[str, Any]:
        """解析TXT文档"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        
        return {
            "full_text": content,
            "paragraphs": paragraphs,
            "tables": [],
            "page_count": 1
        }
    
    async def _extract_field_value(self, field_def: Dict[str, Any], 
                                 document_content: Dict[str, Any]) -> Dict[str, Any]:
        """提取单个字段的值"""
        try:
            field_name = field_def["field_name"]
            attributes = field_def["attributes"]
            extraction_guidance = field_def.get("extraction_guidance", {})
            
            # 根据信息来源选择提取策略
            source = attributes.get("source", "上传内容")
            
            if source == "系统时间":
                return await self._extract_system_time(field_def)
            elif source == "既有知识":
                return await self._extract_from_knowledge(field_def)
            elif source == "外部搜索":
                return self._create_external_search_placeholder(field_def)
            else:
                # 从上传内容中提取
                return await self._extract_from_document(field_def, document_content)
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _extract_from_document(self, field_def: Dict[str, Any], 
                                   document_content: Dict[str, Any]) -> Dict[str, Any]:
        """从文档内容中提取字段值"""
        field_name = field_def["field_name"]
        attributes = field_def["attributes"]
        extraction_guidance = field_def.get("extraction_guidance", {})
        
        # 构建提取prompt
        extraction_prompt = self._build_extraction_prompt(field_def, document_content["full_text"])
        
        # 调用LLM进行提取
        llm_response = await llm_service.call_llm([
            {"role": "system", "content": self.prompt_template},
            {"role": "user", "content": extraction_prompt}
        ])
        
        if not llm_response.success:
            return {
                "success": False,
                "error": f"LLM调用失败: {llm_response.error}"
            }
        
        # 解析提取结果
        extracted_value = llm_response.content.strip()
        
        # 应用默认值逻辑
        if not extracted_value or extracted_value == "未找到":
            default_value = attributes.get("default")
            if default_value:
                extracted_value = default_value
                extraction_method = "default_value"
                confidence = 0.8
            else:
                extracted_value = "未找到"
                extraction_method = "failed"
                confidence = 0.0
        else:
            extraction_method = "llm_extraction"
            confidence = self._estimate_confidence(extracted_value, field_def)
        
        # 验证字段值
        validation_result = self._validate_field_value(extracted_value, field_def)
        
        return {
            "success": True,
            "data": {
                "field_name": field_name,
                "field_path": field_def["field_path"],
                "extracted_value": extracted_value,
                "confidence_score": confidence,
                "extraction_method": extraction_method,
                "source_location": {
                    "page": 1,
                    "paragraph": 1,
                    "text_snippet": extracted_value[:100]
                },
                "validation_status": "passed" if validation_result["valid"] else "failed",
                "field_attributes": attributes
            }
        }
    
    def _build_extraction_prompt(self, field_def: Dict[str, Any], document_text: str) -> str:
        """构建字段提取prompt"""
        field_name = field_def["field_name"]
        attributes = field_def["attributes"]
        
        prompt = f"""
请从以下文档内容中提取字段"{field_name}"的值：

字段描述：{attributes.get('description', '')}
字段格式：{attributes.get('format', '普通文本')}
示例值：{attributes.get('example', '')}
是否必填：{'是' if attributes.get('must') else '否'}

文档内容：
{document_text[:2000]}  # 限制文档长度避免token超限

请直接返回提取到的字段值，如果找不到相关信息，请返回"未找到"。
"""
        return prompt
    
    def _estimate_confidence(self, extracted_value: str, field_def: Dict[str, Any]) -> float:
        """估算提取置信度"""
        if not extracted_value or extracted_value == "未找到":
            return 0.0
        
        # 基础置信度
        confidence = 0.7
        
        # 根据字段属性调整
        attributes = field_def["attributes"]
        
        # 如果有示例值，检查相似度
        example = attributes.get("example", "")
        if example and len(example) > 0:
            # 简单的相似度检查
            if len(extracted_value) > 0:
                confidence += 0.1
        
        # 根据字段格式调整
        field_format = attributes.get("format", "普通文本")
        if field_format == "人名" and self._is_valid_name(extracted_value):
            confidence += 0.1
        elif field_format == "普通文本" and len(extracted_value) > 0:
            confidence += 0.05
        
        return min(1.0, confidence)
    
    def _is_valid_name(self, value: str) -> bool:
        """验证是否为有效人名"""
        # 简单的人名验证逻辑
        if not value or len(value) < 2 or len(value) > 10:
            return False
        
        # 检查是否包含常见的非人名字符
        invalid_chars = ['@', '#', '$', '%', '&', '*', '(', ')', '[', ']', '{', '}']
        for char in invalid_chars:
            if char in value:
                return False
        
        return True
    
    def _validate_field_value(self, value: str, field_def: Dict[str, Any]) -> Dict[str, Any]:
        """验证字段值"""
        attributes = field_def["attributes"]
        
        # 检查长度限制
        max_length = attributes.get("length")
        if max_length and len(value) > max_length:
            return {
                "valid": False,
                "error": f"字段值长度超过限制({max_length})"
            }
        
        # 检查必填字段
        if attributes.get("must") and (not value or value == "未找到"):
            return {
                "valid": False,
                "error": "必填字段不能为空"
            }
        
        return {"valid": True}
    
    async def _extract_system_time(self, field_def: Dict[str, Any]) -> Dict[str, Any]:
        """提取系统时间"""
        from datetime import datetime
        
        current_time = datetime.now()
        field_format = field_def["attributes"].get("format", "普通文本")
        
        if "日期" in field_format:
            time_value = current_time.strftime("%Y-%m-%d")
        elif "时间" in field_format:
            time_value = current_time.strftime("%H:%M:%S")
        else:
            time_value = current_time.strftime("%Y-%m-%d %H:%M:%S")
        
        return {
            "success": True,
            "data": {
                "field_name": field_def["field_name"],
                "field_path": field_def["field_path"],
                "extracted_value": time_value,
                "confidence_score": 1.0,
                "extraction_method": "system_time",
                "source_location": {"page": 0, "paragraph": 0, "text_snippet": "系统时间"},
                "validation_status": "passed",
                "field_attributes": field_def["attributes"]
            }
        }
    
    async def _extract_from_knowledge(self, field_def: Dict[str, Any]) -> Dict[str, Any]:
        """从既有知识中提取"""
        # 这里可以实现基于知识库的提取逻辑
        # 目前返回默认值或标记为需要人工输入
        default_value = field_def["attributes"].get("default", "")
        
        if default_value:
            return {
                "success": True,
                "data": {
                    "field_name": field_def["field_name"],
                    "field_path": field_def["field_path"],
                    "extracted_value": default_value,
                    "confidence_score": 0.8,
                    "extraction_method": "knowledge_base",
                    "source_location": {"page": 0, "paragraph": 0, "text_snippet": "知识库"},
                    "validation_status": "passed",
                    "field_attributes": field_def["attributes"]
                }
            }
        else:
            return {
                "success": False,
                "error": "知识库中未找到相关信息"
            }
    
    def _create_external_search_placeholder(self, field_def: Dict[str, Any]) -> Dict[str, Any]:
        """创建外部搜索占位符"""
        return {
            "success": False,
            "error": "需要外部搜索",
            "search_keywords": [field_def["field_name"]],
            "field_attributes": field_def["attributes"]
        }
    
    def _get_document_type(self, file_path: str) -> str:
        """获取文档类型"""
        extension = os.path.splitext(file_path)[1].lower()
        type_mapping = {
            '.docx': 'Word文档',
            '.txt': '文本文档',
            '.pdf': 'PDF文档'
        }
        return type_mapping.get(extension, '未知类型')
    
    def _calculate_avg_confidence(self, extraction_results: List[Dict[str, Any]]) -> float:
        """计算平均置信度"""
        if not extraction_results:
            return 0.0
        
        total_confidence = sum(result.get("confidence_score", 0) for result in extraction_results)
        return round(total_confidence / len(extraction_results), 2)
    
    def _calculate_quality_metrics(self, extraction_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算质量指标"""
        if not extraction_results:
            return {
                "accuracy_indicators": {"direct_matches": 0, "semantic_matches": 0, "inferred_values": 0, "default_values": 0},
                "completeness_indicators": {"required_fields_filled": 0, "required_fields_total": 0, "optional_fields_filled": 0, "optional_fields_total": 0},
                "confidence_distribution": {"high_confidence": 0, "medium_confidence": 0, "low_confidence": 0}
            }
        
        # 统计提取方法
        method_counts = {}
        for result in extraction_results:
            method = result.get("extraction_method", "unknown")
            method_counts[method] = method_counts.get(method, 0) + 1
        
        # 统计置信度分布
        high_conf = sum(1 for r in extraction_results if r.get("confidence_score", 0) > 0.8)
        medium_conf = sum(1 for r in extraction_results if 0.5 <= r.get("confidence_score", 0) <= 0.8)
        low_conf = sum(1 for r in extraction_results if r.get("confidence_score", 0) < 0.5)
        
        # 统计必填字段
        required_filled = sum(1 for r in extraction_results if r.get("field_attributes", {}).get("must", False))
        
        return {
            "accuracy_indicators": {
                "direct_matches": method_counts.get("llm_extraction", 0),
                "semantic_matches": method_counts.get("llm_extraction", 0),
                "inferred_values": method_counts.get("knowledge_base", 0),
                "default_values": method_counts.get("default_value", 0)
            },
            "completeness_indicators": {
                "required_fields_filled": required_filled,
                "required_fields_total": required_filled,  # 简化计算
                "optional_fields_filled": len(extraction_results) - required_filled,
                "optional_fields_total": len(extraction_results) - required_filled
            },
            "confidence_distribution": {
                "high_confidence": high_conf,
                "medium_confidence": medium_conf,
                "low_confidence": low_conf
            }
        }
    
    async def _validate_extractions(self, extraction_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """验证提取结果"""
        validation_results = []
        
        for result in extraction_results:
            field_name = result["field_name"]
            extracted_value = result["extracted_value"]
            attributes = result["field_attributes"]
            
            validation_rules = []
            validation_status = "passed"
            validation_details = "验证通过"
            
            # 长度检查
            max_length = attributes.get("length")
            if max_length:
                validation_rules.append("长度限制")
                if len(extracted_value) > max_length:
                    validation_status = "failed"
                    validation_details = f"长度超过限制({max_length})"
            
            # 必填检查
            if attributes.get("must"):
                validation_rules.append("必填检查")
                if not extracted_value or extracted_value == "未找到":
                    validation_status = "failed"
                    validation_details = "必填字段不能为空"
            
            validation_results.append({
                "field_name": field_name,
                "validation_rules": validation_rules,
                "validation_status": validation_status,
                "validation_details": validation_details
            })
        
        return validation_results
    
    def _generate_recommendations(self, extraction_results: List[Dict[str, Any]], 
                                failed_extractions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成改进建议"""
        human_review_needed = []
        data_quality_improvements = []
        
        # 检查低置信度字段
        for result in extraction_results:
            if result.get("confidence_score", 0) < 0.6:
                human_review_needed.append({
                    "field_name": result["field_name"],
                    "reason": "置信度较低",
                    "current_value": result["extracted_value"],
                    "suggestions": ["请人工确认该字段的准确性"]
                })
        
        # 检查失败的提取
        if failed_extractions:
            data_quality_improvements.append("建议补充缺失字段的信息")
        
        if len(extraction_results) < 5:
            data_quality_improvements.append("建议提供更详细的文档内容")
        
        return {
            "human_review_needed": human_review_needed,
            "data_quality_improvements": data_quality_improvements
        }
