<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Debug</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
    </style>
</head>
<body>
    <h1>Frontend Debug Test</h1>
    <div id="tests"></div>
    
    <script>
        function addTest(name, success, message) {
            const div = document.createElement('div');
            div.className = `test ${success ? 'success' : 'error'}`;
            div.innerHTML = `<strong>${name}:</strong> ${success ? 'PASS' : 'FAIL'} - ${message}`;
            document.getElementById('tests').appendChild(div);
        }
        
        // Test 1: Basic JavaScript
        addTest('JavaScript Execution', true, 'JavaScript is working');
        
        // Test 2: Frontend accessibility
        fetch('http://localhost:5175/')
            .then(response => {
                addTest('Frontend Server', response.ok, `Status: ${response.status}`);
                return response.text();
            })
            .then(html => {
                addTest('HTML Contains Root', html.includes('id="root"'), 'Root div present');
                addTest('HTML Contains Script', html.includes('script'), 'Script tags present');
            })
            .catch(error => {
                addTest('Frontend Server', false, `Error: ${error.message}`);
            });
        
        // Test 3: Backend accessibility
        fetch('http://localhost:8000/health')
            .then(response => response.json())
            .then(data => {
                addTest('Backend Health', data.status === 'healthy', `Status: ${data.status}`);
            })
            .catch(error => {
                addTest('Backend Health', false, `Error: ${error.message}`);
            });
        
        // Test 4: Console errors
        const originalError = console.error;
        const errors = [];
        console.error = function(...args) {
            errors.push(args.join(' '));
            originalError.apply(console, arguments);
        };
        
        setTimeout(() => {
            addTest('Console Errors', errors.length === 0, errors.length ? `Found ${errors.length} errors: ${errors[0]}` : 'No console errors');
        }, 2000);
    </script>
</body>
</html>