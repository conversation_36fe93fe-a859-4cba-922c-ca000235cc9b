#!/usr/bin/env python3
"""
测试最终修复效果
"""

import requests
import tempfile
import os
import json
import time

def test_final_fix():
    """测试最终修复效果"""
    
    # 创建测试文档
    test_content = """
    季度总结会议纪要
    
    会议时间：2025年1月31日 下午3:00-5:00
    会议地点：总部大会议室
    主持人：总经理
    参会人员：各部门经理、项目负责人、财务总监、人事总监
    
    会议议题：
    1. 第四季度业绩总结
    2. 年度目标完成情况评估
    3. 下一年度规划讨论
    
    讨论内容：
    1. 第四季度营收超额完成，达到预期目标的110%
    2. 客户满意度持续提升，达到95%
    3. 团队规模扩大20%，人才储备充足
    4. 技术创新项目取得重大突破
    
    决议事项：
    1. 财务总监准备详细财务报告，下周一前提交
    2. 人事总监制定新年度招聘计划
    3. 各部门经理提交下年度预算申请
    4. 项目负责人汇总技术创新成果
    5. 下次会议时间：2025年2月7日 下午3:00
    
    会议记录人：秘书处
    """
    
    print("🚀 开始测试最终修复效果...")
    
    # 第1步: 启动工作流
    print("\n📝 第1步: 启动工作流...")
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(test_content)
        temp_file_path = f.name
    
    try:
        with open(temp_file_path, 'rb') as f:
            files = {'files': ('quarterly_summary.txt', f, 'text/plain')}
            data = {'text_input': '请帮我生成会议纪要'}
            
            response = requests.post(
                'http://localhost:8000/api/workflow/langgraph/start',
                files=files,
                data=data
            )
        
        print(f"启动状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            workflow_id = result.get('workflow_id')
            print(f"工作流ID: {workflow_id}")
            print(f"工作流状态: {result.get('status')}")
            
            requires_user_action = result.get('data', {}).get('requires_user_action', False)
            print(f"需要用户操作: {requires_user_action}")
            
            if requires_user_action:
                print("\n✅ 工作流正确暂停，等待用户确认模板")
                
                # 第2步: 确认模板
                print("\n📋 第2步: 确认推荐的模板...")
                time.sleep(2)
                
                confirm_response = requests.post(
                    f'http://localhost:8000/api/workflow/langgraph/resume/{workflow_id}',
                    json={'action': 'confirm'}
                )
                
                print(f"模板确认状态码: {confirm_response.status_code}")
                if confirm_response.status_code == 200:
                    confirm_result = confirm_response.json()
                    print(f"确认状态: {confirm_result.get('status')}")
                    
                    # 第3步: 等待数据提取并检查状态
                    print("\n🔍 第3步: 等待数据提取并检查状态...")
                    
                    # 等待数据提取完成
                    for i in range(12):  # 最多等待12次，每次3秒
                        time.sleep(3)
                        print(f"  检查状态 ({i+1}/12)...")
                        
                        status_response = requests.get(
                            f'http://localhost:8000/api/workflow/langgraph/status/{workflow_id}'
                        )
                        
                        print(f"    状态码: {status_response.status_code}")
                        
                        if status_response.status_code == 200:
                            status_data = status_response.json()
                            
                            print(f"    状态来源: {'持久化存储' if status_data.get('saved_at') else 'LangGraph'}")
                            print(f"    当前阶段: {status_data.get('current_stage')}")
                            print(f"    下一步动作: {status_data.get('next_action')}")
                            print(f"    待处理交互: {status_data.get('pending_interactions', [])}")
                            print(f"    提取结果数量: {len(status_data.get('extraction_results', []))}")
                            print(f"    需要用户操作: {status_data.get('requires_user_action', False)}")
                            
                            # 检查是否有持久化状态
                            if status_data.get('saved_at'):
                                print(f"\n🎉 最终修复成功！持久化状态机制正常工作！")
                                print(f"    保存时间: {status_data.get('saved_at')}")
                                
                                # 显示提取结果
                                extraction_results = status_data.get('extraction_results', [])
                                if extraction_results:
                                    print(f"\n📊 提取结果 (前5个):")
                                    for result in extraction_results[:5]:
                                        field_name = result.get('field_name', 'unknown')
                                        field_value = result.get('field_value', 'N/A')
                                        confidence = result.get('confidence', 0)
                                        print(f"      {field_name}: {field_value} (置信度: {confidence:.2f})")
                                
                                # 如果需要用户操作，进行数据确认
                                if status_data.get('requires_user_action'):
                                    print(f"\n✅ 第4步: 确认提取的数据...")
                                    
                                    data_confirm_response = requests.post(
                                        f'http://localhost:8000/api/workflow/langgraph/resume/{workflow_id}',
                                        json={'action': 'confirm'}
                                    )
                                    
                                    if data_confirm_response.status_code == 200:
                                        print("✅ 数据确认成功！完整工作流程正常！")
                                        
                                        # 最终状态检查
                                        print("\n📄 第5步: 检查最终状态...")
                                        time.sleep(3)
                                        
                                        final_status_response = requests.get(
                                            f'http://localhost:8000/api/workflow/langgraph/status/{workflow_id}'
                                        )
                                        
                                        if final_status_response.status_code == 200:
                                            final_status = final_status_response.json()
                                            print(f"    最终阶段: {final_status.get('current_stage')}")
                                            print(f"    最终待处理交互: {final_status.get('pending_interactions', [])}")
                                            print(f"    工作流完成状态: {'完成' if not final_status.get('requires_user_action') else '等待用户操作'}")
                                    else:
                                        print(f"❌ 数据确认失败: {data_confirm_response.status_code}")
                                
                                break
                            
                            # 检查是否有提取结果（即使没有持久化状态）
                            elif status_data.get('extraction_results'):
                                print(f"\n⚠️ 有提取结果但没有持久化状态")
                                break
                        elif status_response.status_code == 404:
                            print(f"    工作流未找到，可能还在处理中...")
                        else:
                            print(f"    ❌ 状态检查失败: {status_response.status_code}")
                    else:
                        print("\n⚠️ 等待超时，检查是否有提取文件生成")
                        
                        # 检查是否有提取文件生成
                        import glob
                        extract_files = glob.glob(f"templates/keysextracted/{workflow_id}_*.md")
                        if extract_files:
                            print(f"✅ 找到提取文件: {extract_files}")
                            print("数据提取成功，但状态持久化可能有问题")
                        else:
                            print("❌ 没有找到提取文件")
                        
                else:
                    print(f"❌ 模板确认失败: {confirm_response.status_code}")
            else:
                print("❌ 工作流没有正确暂停等待用户输入")
        else:
            print(f"❌ 启动工作流失败: {response.status_code}")
            print(response.text)
            
    finally:
        # 清理临时文件
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
    
    print("\n✅ 最终修复测试完成")

if __name__ == "__main__":
    test_final_fix()
