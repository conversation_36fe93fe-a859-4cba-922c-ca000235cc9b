#!/usr/bin/env python3
"""
测试模板确认工作流
"""

import requests
import json
import tempfile
import os

BASE_URL = "http://localhost:8000"

def create_test_file():
    """创建测试文件"""
    content = """
会议纪要

时间：2024年7月25日 14:00-15:30
地点：会议室A
主持人：张三
参会人员：李四、王五、赵六

会议议题：
1. 项目进度汇报
2. 下阶段工作安排
3. 资源配置讨论

会议内容：
1. 项目进度汇报
   - 当前完成度：80%
   - 主要成果：完成了核心功能开发
   - 遇到的问题：测试环境配置有待优化

2. 下阶段工作安排
   - 完成剩余功能开发
   - 进行全面测试
   - 准备上线部署

3. 资源配置讨论
   - 需要增加2名测试人员
   - 服务器资源需要扩容

决议事项：
1. 下周完成功能开发
2. 申请增加测试人员
3. 联系运维部门扩容服务器

下次会议时间：2024年8月1日 14:00
"""
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(content)
        return f.name

def test_template_confirmation_with_file():
    """测试带文件上传的模板确认和数据提取工作流"""
    print("🧪 测试带文件上传的模板确认和数据提取工作流")
    print("=" * 50)
    
    # 创建测试文件
    test_file_path = create_test_file()
    
    try:
        # 第一步：启动工作流（带文件上传）
        print("1. 启动工作流（带文件上传）...")
        
        with open(test_file_path, 'rb') as f:
            files = {'files': ('meeting_notes.txt', f, 'text/plain')}
            data = {'text_input': '请根据上传的文档生成会议纪要'}
            
            response = requests.post(
                f"{BASE_URL}/api/workflow/langgraph/start",
                files=files,
                data=data,
                timeout=30
            )
        
        print(f"启动响应状态码: {response.status_code}")
        
        if response.status_code != 200:
            print(f"启动失败: {response.text}")
            return
            
        result = response.json()
        workflow_id = result.get('workflow_id')
        print(f"工作流ID: {workflow_id}")
        
        # 检查意图识别结果
        final_state = result.get('data', {}).get('final_state', {})
        intention_result = final_state.get('intention_result')
        
        if intention_result:
            print(f"意图类型: {intention_result.get('intent_type')}")
            print(f"置信度: {intention_result.get('confidence')}")
        
        # 检查是否需要用户确认
        requires_action = result.get('data', {}).get('requires_user_action', False)
        print(f"需要用户操作: {requires_action}")
        
        if requires_action:
            print("\n2. 模拟用户确认推荐模板...")
            
            # 第二步：确认模板
            resume_data = {
                "action": "confirm"
            }
            
            resume_response = requests.post(
                f"{BASE_URL}/api/workflow/langgraph/resume/{workflow_id}",
                json=resume_data,
                timeout=30
            )
            
            print(f"恢复响应状态码: {resume_response.status_code}")
            
            if resume_response.status_code == 200:
                resume_result = resume_response.json()
                print("✅ 模板确认成功")
                print(f"恢复后状态: {resume_result.get('status')}")

                # 检查最终状态
                final_state = resume_result.get('data', {}).get('final_state', {})
                current_stage = final_state.get('current_stage', 'unknown')
                print(f"当前阶段: {current_stage}")

                # 检查是否有提取结果
                extraction_results = final_state.get('extraction_results', [])
                if extraction_results:
                    print(f"\n📊 数据提取结果:")
                    for result in extraction_results[:5]:  # 只显示前5个
                        field_name = result.get('field_name', '')
                        field_value = result.get('field_value', '')
                        confidence = result.get('confidence', 0)
                        print(f"  {field_name}: {field_value[:50]}... (置信度: {confidence:.2f})")

                    if len(extraction_results) > 5:
                        print(f"  ... 还有 {len(extraction_results) - 5} 个字段")

                # 检查是否有提取文件
                extraction_file = final_state.get('extraction_file_path')
                if extraction_file:
                    print(f"\n📁 提取结果已保存到: {extraction_file}")

                # 检查是否需要进一步的用户交互
                requires_action = resume_result.get('data', {}).get('requires_user_action', False)
                if requires_action:
                    print("\n⚠️ 需要进一步的用户交互（数据确认）")
                else:
                    print("\n✅ 数据提取完成，无需进一步交互")

            else:
                print(f"恢复失败: {resume_response.text}")
        else:
            print("⚠️ 工作流没有要求用户确认")
            print("可能的原因：")
            print("- 意图识别为对话类型而非文本生成类型")
            print("- 模板识别没有触发人工交互")
            
            # 显示详细的响应信息
            print(f"\n响应消息: {result.get('message', '')}")
            
    except Exception as e:
        print(f"测试异常: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        if os.path.exists(test_file_path):
            os.unlink(test_file_path)

if __name__ == "__main__":
    test_template_confirmation_with_file()
