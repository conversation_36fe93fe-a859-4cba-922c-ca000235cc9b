"""
通用Pydantic schemas
"""

from datetime import datetime
from typing import Generic, TypeVar, List, Optional
from uuid import UUID
from pydantic import BaseModel, Field

T = TypeVar('T')


class BaseResponse(BaseModel):
    """基础响应模型"""
    
    id: UUID
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    is_active: bool = True
    
    class Config:
        from_attributes = True


class PaginatedResponse(BaseModel, Generic[T]):
    """分页响应模型"""
    
    items: List[T]
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")
    
    class Config:
        from_attributes = True


class MessageResponse(BaseModel):
    """消息响应模型"""
    
    message: str
    success: bool = True
    data: Optional[dict] = None
    
    class Config:
        from_attributes = True


class ErrorResponse(BaseModel):
    """错误响应模型"""
    
    error: str
    detail: Optional[str] = None
    code: Optional[str] = None
    
    class Config:
        from_attributes = True
