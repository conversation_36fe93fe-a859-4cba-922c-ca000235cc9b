"""
模板解析工具
负责分析Word模板文档，识别占位符，解析字段结构
"""

import os
import json
import re
from typing import Dict, Any, List, Optional
from docx import Document
from sqlalchemy.orm import Session

from .base_tool import BaseTool, ToolResult
from services.llm_service import llm_service
from models.template import Template
from models.agent import KeyDefinition
from database import get_db


class TemplateParsingTool(BaseTool):
    """模板解析工具"""
    
    def __init__(self):
        super().__init__("template_parsing")
        self.prompt_template = self._load_prompt_template()
        # 占位符匹配模式
        self.placeholder_patterns = [
            r'\{\{([^}]+)\}\}',  # {{字段名}}
            r'\{([^}]+)\}',      # {字段名}
            r'\[([^\]]+)\]',     # [字段名]
            r'<([^>]+)>',        # <字段名>
        ]
    
    def _load_prompt_template(self) -> str:
        """加载prompt模板"""
        try:
            prompt_path = os.path.join(
                os.path.dirname(__file__), 
                "..", "prompts", "template_parsing.txt"
            )
            with open(prompt_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            self.log_error(f"加载prompt模板失败: {e}")
            return ""
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行模板解析
        
        Args:
            input_data: {
                "template_id": "模板ID",
                "template_path": "模板文件路径"
            }
        """
        try:
            # 验证输入
            required_fields = ["template_id"]
            if not self.validate_input(input_data, required_fields):
                return self.create_result(False, error="输入数据验证失败")
            
            template_id = input_data["template_id"]
            
            # 获取模板信息
            template_info = await self._get_template_info(template_id)
            if not template_info:
                return self.create_result(False, error="模板信息未找到")
            
            # 解析Word文档
            document_analysis = await self._parse_word_document(template_info["file_path"])
            
            # 获取字段定义
            field_definitions = await self._get_field_definitions(template_id)
            
            # 匹配占位符与字段定义
            matched_fields = await self._match_placeholders_with_definitions(
                document_analysis["placeholders"], field_definitions
            )
            
            # 生成提取指导
            extraction_guidance = await self._generate_extraction_guidance(matched_fields)
            
            # 构建解析结果
            parsing_result = {
                "template_info": template_info,
                "document_structure": document_analysis["structure"],
                "placeholder_analysis": {
                    "total_placeholders": len(document_analysis["placeholders"]),
                    "identified_placeholders": document_analysis["placeholders"],
                    "unmatched_placeholders": [],
                    "parsing_issues": document_analysis.get("issues", [])
                },
                "field_definitions": matched_fields,
                "extraction_strategy": extraction_guidance,
                "quality_assessment": await self._assess_parsing_quality(matched_fields, document_analysis)
            }
            
            # 设置下一步动作
            parsing_result["next_action"] = "data_extraction" if matched_fields else "error_handling"
            
            self.log_info(f"模板解析完成，识别到 {len(matched_fields)} 个字段")
            
            return self.create_result(True, {"parsing_result": parsing_result}, message="模板解析成功")
            
        except Exception as e:
            self.log_error(f"模板解析执行失败: {e}")
            return self.create_result(False, error=str(e))
    
    async def _get_template_info(self, template_id: str) -> Optional[Dict[str, Any]]:
        """获取模板信息"""
        try:
            db = next(get_db())
            template = db.query(Template).filter(Template.id == template_id).first()
            
            if not template:
                return None
            
            return {
                "template_id": str(template.id),
                "template_name": template.name,
                "file_path": template.file_path,
                "category": template.category,
                "version": template.version
            }
            
        except Exception as e:
            self.log_error(f"获取模板信息失败: {e}")
            return None
    
    async def _parse_word_document(self, file_path: str) -> Dict[str, Any]:
        """解析Word文档"""
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"模板文件不存在: {file_path}")
            
            doc = Document(file_path)
            
            # 分析文档结构
            structure = {
                "total_pages": 1,  # Word文档页数难以准确获取，设为1
                "sections": [],
                "tables": []
            }
            
            # 提取占位符
            placeholders = []
            paragraph_index = 0
            
            # 解析段落
            for para in doc.paragraphs:
                if para.text.strip():
                    # 查找占位符
                    found_placeholders = self._extract_placeholders_from_text(
                        para.text, paragraph_index, "paragraph"
                    )
                    placeholders.extend(found_placeholders)
                    paragraph_index += 1
            
            # 解析表格
            table_index = 0
            for table in doc.tables:
                table_info = {
                    "table_name": f"表格{table_index + 1}",
                    "location": f"表格位置 {table_index + 1}",
                    "columns": [],
                    "field_count": 0
                }
                
                # 获取表头
                if table.rows:
                    header_row = table.rows[0]
                    table_info["columns"] = [cell.text.strip() for cell in header_row.cells]
                
                # 查找表格中的占位符
                for row_idx, row in enumerate(table.rows):
                    for cell_idx, cell in enumerate(row.cells):
                        if cell.text.strip():
                            found_placeholders = self._extract_placeholders_from_text(
                                cell.text, f"表格{table_index + 1}_行{row_idx + 1}_列{cell_idx + 1}", "table"
                            )
                            placeholders.extend(found_placeholders)
                            table_info["field_count"] += len(found_placeholders)
                
                structure["tables"].append(table_info)
                table_index += 1
            
            return {
                "structure": structure,
                "placeholders": placeholders,
                "issues": []
            }
            
        except Exception as e:
            self.log_error(f"Word文档解析失败: {e}")
            return {
                "structure": {"total_pages": 0, "sections": [], "tables": []},
                "placeholders": [],
                "issues": [str(e)]
            }
    
    def _extract_placeholders_from_text(self, text: str, location: str, context_type: str) -> List[Dict[str, Any]]:
        """从文本中提取占位符"""
        placeholders = []
        
        for pattern in self.placeholder_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                placeholder_text = match.group(0)
                field_name = match.group(1).strip()
                
                placeholder_info = {
                    "placeholder_text": placeholder_text,
                    "field_name": field_name,
                    "location": {
                        "position": location,
                        "context_type": context_type
                    },
                    "placeholder_type": "standard",
                    "context": text.strip()[:100]  # 保留前100个字符作为上下文
                }
                
                placeholders.append(placeholder_info)
        
        return placeholders
    
    async def _get_field_definitions(self, template_id: str) -> List[Dict[str, Any]]:
        """获取字段定义"""
        try:
            db = next(get_db())
            definitions = db.query(KeyDefinition).filter(
                KeyDefinition.template_id == template_id
            ).order_by(KeyDefinition.field_order).all()
            
            field_list = []
            for definition in definitions:
                field_info = {
                    "field_name": definition.field_name,
                    "field_path": definition.field_path,
                    "parent_field": definition.parent_field,
                    "field_level": definition.field_level,
                    "attributes": {
                        "description": definition.description,
                        "example": definition.example,
                        "format": definition.format,
                        "default": definition.default_value,
                        "length": definition.length,
                        "source": definition.source,
                        "must": definition.must
                    },
                    "field_order": definition.field_order
                }
                field_list.append(field_info)
            
            return field_list
            
        except Exception as e:
            self.log_error(f"获取字段定义失败: {e}")
            return []
    
    async def _match_placeholders_with_definitions(self, placeholders: List[Dict[str, Any]], 
                                                 field_definitions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """匹配占位符与字段定义"""
        matched_fields = []
        
        for placeholder in placeholders:
            field_name = placeholder["field_name"]
            
            # 查找匹配的字段定义
            matched_definition = None
            for definition in field_definitions:
                if (definition["field_name"] == field_name or 
                    definition["field_name"].replace("_", "") == field_name.replace("_", "") or
                    field_name in definition["field_name"] or
                    definition["field_name"] in field_name):
                    matched_definition = definition
                    break
            
            if matched_definition:
                # 合并占位符信息和字段定义
                field_info = {
                    "field_name": matched_definition["field_name"],
                    "field_path": matched_definition["field_path"],
                    "placeholder_text": placeholder["placeholder_text"],
                    "attributes": matched_definition["attributes"],
                    "extraction_guidance": {
                        "extraction_prompt": f"从文档中提取{matched_definition['attributes']['description']}",
                        "search_keywords": [field_name, matched_definition["field_name"]],
                        "validation_rules": [],
                        "fallback_strategy": f"如果找不到{field_name}，尝试查找相关信息"
                    },
                    "location": placeholder["location"]
                }
                
                # 根据字段属性生成验证规则
                if matched_definition["attributes"]["length"]:
                    field_info["extraction_guidance"]["validation_rules"].append(
                        f"长度不超过{matched_definition['attributes']['length']}字符"
                    )
                
                if matched_definition["attributes"]["must"]:
                    field_info["extraction_guidance"]["validation_rules"].append("必填字段")
                
                matched_fields.append(field_info)
        
        return matched_fields
    
    async def _generate_extraction_guidance(self, matched_fields: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成提取指导"""
        # 按字段层级和顺序排序
        sorted_fields = sorted(matched_fields, key=lambda x: (
            x.get("attributes", {}).get("field_level", 1),
            x.get("attributes", {}).get("field_order", 0)
        ))
        
        processing_order = []
        field_dependencies = {}
        special_handling = []
        
        for field in sorted_fields:
            field_name = field["field_name"]
            processing_order.append(field_name)
            field_dependencies[field_name] = []
            
            # 识别特殊处理需求
            field_format = field["attributes"].get("format", "普通文本")
            if field_format == "表格行":
                special_handling.append({
                    "field_type": "table",
                    "fields": [field_name],
                    "extraction_method": "table_parsing"
                })
        
        return {
            "processing_order": processing_order,
            "field_dependencies": field_dependencies,
            "special_handling": special_handling
        }
    
    async def _assess_parsing_quality(self, matched_fields: List[Dict[str, Any]], 
                                    document_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """评估解析质量"""
        total_placeholders = len(document_analysis["placeholders"])
        matched_count = len(matched_fields)
        
        parsing_completeness = matched_count / total_placeholders if total_placeholders > 0 else 0
        field_coverage = min(1.0, matched_count / 10)  # 假设期望10个字段
        
        issues = document_analysis.get("issues", [])
        definition_accuracy = 1.0 - (len(issues) * 0.1)  # 每个问题减少0.1分
        
        return {
            "parsing_completeness": round(parsing_completeness, 2),
            "field_coverage": round(field_coverage, 2),
            "definition_accuracy": max(0.0, round(definition_accuracy, 2)),
            "potential_issues": issues,
            "recommendations": [
                "建议检查未匹配的占位符",
                "确认字段定义的完整性"
            ] if parsing_completeness < 0.8 else []
        }
