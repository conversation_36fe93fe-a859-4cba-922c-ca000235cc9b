# 环境配置文件示例
# 复制此文件为 .env 并根据实际情况修改配置

# 应用配置
APP_NAME=超限报告智能生成工具
APP_VERSION=0.1.0
ENVIRONMENT=development
DEBUG=true
SECRET_KEY=your-secret-key-here

# 数据库配置
DATABASE_URL=postgresql://report_user:report_pass@localhost:5432/report_gen
DB_HOST=localhost
DB_PORT=5432
DB_NAME=report_gen
DB_USER=report_user
DB_PASSWORD=report_pass

# Redis 配置
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Celery 配置
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# 文件存储配置
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=50MB
ALLOWED_EXTENSIONS=doc,docx,pdf,txt

# AI 服务配置
# Qwen API 配置
QWEN_API_KEY=your-qwen-api-key
QWEN_API_BASE=https://dashscope.aliyuncs.com/api/v1
QWEN_MODEL=qwen-max

# 硅基流动 API 配置 (嵌入模型)
SILICONFLOW_API_KEY=your-siliconflow-api-key
SILICONFLOW_API_BASE=https://api.siliconflow.cn/v1
EMBEDDING_MODEL=BAAI/bge-large-zh-v1.5

# 向量数据库配置
VECTOR_DB_TYPE=faiss  # faiss 或 milvus
VECTOR_DB_PATH=./vector_db
MILVUS_HOST=localhost
MILVUS_PORT=19530

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log

# CORS 配置
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# JWT 配置
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440

# 邮件配置 (可选)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password

# 监控配置 (可选)
SENTRY_DSN=your-sentry-dsn
