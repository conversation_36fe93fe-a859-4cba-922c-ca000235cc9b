# 界面设计

## 主页面

### 导航栏（界面左侧1/6宽由顶到底）

#### 登录状态(导航栏1/10高，顶对齐)

显示当前登录用户名，用户名右侧有注销字样

#### 标签页(导航栏剩余高度，底对齐)

分别标注主界面、项目管理、模板管理、数据管理、结果管理、，用于切换不同的操作界面

#### 系统设置栏(导航栏1/10高，底对齐)

用户管理、系统设置、UI选择

### 主操作界面（界面右侧5/6宽由顶到底）

#### 结果交互区(主操作界面3/5高，顶对齐)

响应大模型返回结果，结果类型包括文字、表格、图片、docx文件、基于表格的数据，具有scroll bar可上下滑动，且可以在该区域进行相关结果的编辑，编辑对象包括：文档模板识别结果，文档数据识别结果，文档生成结果

#### 状态区(主操作界面1/10高，其余结果交互区下方，对话区上方)

通过下拉列表分别选择项目，模板，数据，右侧显示当前大模型交互状态，状态包括：检索状态、模板分析、信息抽取、报告生成四个状态

#### 功能沟通区(主操作界面1/5高，底对齐)

此处用户输入不同的命令，完成大部分该产品功能的实现，增加上传按钮，可上传需要处理的文件，右侧具有确定按钮，将输入内容提交至系统

用户动作：
4.1 检索状态
前置条件：
通过自然语言，检索系统中目前已有的项目信息，模板信息，数据信息，报告信息，查询后在结果交互区进行展示，所有结果不可编辑
通过自然语言，切换到其他状态，如果满足前置条件，则可以进行切换
4.2 模板分析
前置条件：具有被选择的项目，上传了docx或者doc文档   或者   选择了具体模板
通过自然语言，启动分析功能对上传的文档结构和数据构成进行分析，形成结果展示在结果交互区，编辑完成后，通过自然语言让系统进行保存
如果没有上传文档数据，但选择了具体模板的，则将模板导入结果交互区进行编辑，编辑完成后可选择是否保存，或者另存为
4.3 信息抽取
前置条件：选择了具体模板，上传了docx、doc、jpg、txt等可能存在信息的数据文件
通过自然语言，启动数据抽取功能，对上传了的数据进行相关的处理，按照模板要求，形成生成报告所需的各类数据，抽取完成后，形成结果展示在结果交互区进行编辑，完成后可选择是否保存，或者另存为
4.4 报告生成
前置条件：选择了具体模板，选择了具体数据
通过自然语言，启动报告生成功能，将数据填充到具体的模板中，形成新的报告，模板文件以docx文档存储，以减少工作量，生成报告后，将报告展示在结果交互区，可以自动进行排版，生成用户需要的结果文件，生成报告的过程中，除了常规的文字数据外，还有表格和图片数据，表格，图片可以根据名称自动排版到模板合适位置

## 项目管理 （点击后切换主操作界面）

界面布局描述：
左侧导航栏中，项目管理高亮，右侧主操作界面按照列表显示项目，可对项目进行增删改查，项目具有项目名称、创建人、创建时间、项目描述、关联模板数、关联数据数量、最后生成报告时间信息
用户动作：
1.点击表头对项目进行排序；
2.点击编辑可打开项目详情对话框，对项目进行编辑，包括项目名称、项目描述
3.点击具体项目相关模板数量，可跳转到模板管理，自动选择本项目
4.点击具体项目相关数据数量，可跳转到数据管理，自动选择本项目

## 模板管理（点击后切换主操作界面）

界面布局描述：
左侧导航栏中，模板管理高亮，右侧按照列表显示所有模板，可对模板进行增删改查和下载。模板列表包含模板名称、所属项目、创建人、创建时间、模板描述、占位符数量、被引用次数等信息。
用户动作：

1. 点击表头可对模板进行排序；
2. 点击新增模板按钮，弹出上传Word模板对话框，填写模板名称、描述并上传文件；
3. 点击模板名称可查看模板详情，包括占位符预览、模板结构树、被引用项目列表等；
4. 点击编辑按钮可修改模板名称、描述，或重新上传模板文件；
5. 点击删除按钮可删除模板（如被项目引用需二次确认）；
6. 点击下载按钮可下载模板原文件；
7. 点击占位符数量可弹出占位符详情列表，支持复制占位符名。

## 数据管理（点击后切换主操作界面）

* 界面布局描述：
  左侧导航栏中，数据管理高亮，右侧以列表形式展示所有数据源，支持增删改查和下载。数据列表包含数据名称、所属项目、关联模板、上传人、上传时间、来源数据（Word/PDF/图片/文本）、抽取结果预览等信息。
  用户动作：

1. 点击数据名称可查看数据详情，包括原文件预览、抽取结果、抽取日志等；
2. 点击编辑按钮可修改数据名称、描述，或重新上传数据文件；
3. 点击删除按钮可删除数据（如已被报告引用需二次确认）；
4. 点击抽取结果预览可展开查看结构化数据、表格、图片等内容；
5. 支持批量选择数据进行删除或下载操作。

## 结果报告（点击后切换主操作界面）

* 界面布局描述：
  左侧界面菜单栏中，结果管理（报告管理）高亮，右侧以列表形式展示所有已生成报告，支持增删改查和下载。报告列表包含报告名称、所属项目、关联模板、关联数据、生成时间、生成状态、报告版本、报告预览等信息。
  用户动作：

1. 点击生成报告按钮，返回主界面，选择项目、模板、数据，填写报告名称、描述，启动报告生成流程；
2. 点击报告名称可查看报告详情，包括报告预览、生成日志、填充数据明细等；
3. 点击下载按钮可下载报告Word文件；
4. 点击删除按钮可删除报告（需二次确认）；
5. 点击报告预览可在线浏览报告内容，支持分页、跳转、放大缩小等操作；

## 用户管理（点击后切换主操作界面）

* 界面布局描述：
  左侧界面菜单栏中，用户管理高亮，右侧以列表形式展示所有用户信息，支持用户的增删改查和权限分配。用户列表包含用户名、姓名、角色、所属部门、创建时间、最后登录时间、状态（启用/禁用）等信息。
  用户动作：

1. 点击新增用户按钮，弹出新建用户对话框，填写用户名、姓名、密码、角色、部门等信息；
2. 点击用户名可查看用户详情，包括基本信息、历史操作记录、所属项目等；
3. 点击编辑按钮可修改用户信息、重置密码、调整角色权限；
4. 点击删除按钮可删除用户（需二次确认）；
5. 点击状态切换按钮可启用/禁用用户账号；
6. 支持批量选择用户进行删除或状态切换操作。


# AI修改完善要求1

1.删除主操作界面中，结果交互区、功能沟通区的名称和名称栏

2.主操作界面中，将上传文件放到沟通区上方，上传后可以显示文件名称

3.功能沟通区右侧的确定按钮放到对话框中右下角位置，大小缩减为当前的1/3

4.主操作界面需要和导航栏中间具有可以左右滑动的能力，可以动态调整导航栏和主操作界面的占比

4.项目管理界面中，项目列表上方的右侧"新建项目"可以增加弹窗功能用于填写项目信息

5.项目管理界面中，项目列表下方增加横向分页器，用于管理大量项目

6.项目管理界面中，项目列表按照最后生成报告时间倒序排列，最近生成过报告的项目在最前面

7.模板管理界面中，点击编辑功能需要可以对模板进行编辑

8.模板管理界面中，模板列表下方增加横向分页器，用于管理大量模板

9.模板管理界面中，模板列表按照创建时间倒序排列，最近创建的模板在最前面

10.数据管理界面中，删除所述项目，上传时间改为"生成时间"

11.数据管理界面中，点击具体数据的来源，需要弹出弹窗，可以下载来源数据

12.数据管理界面中，点击上传数据，需要弹出弹窗，可以新增数据，编辑数据名称，选择关联模板，上传数据源(可选)，上传数据

13.数据管理界面中，数据列表下方增加横向分页器，用于管理大量数据

14.数据管理模板管理界面中，数据列表按照创建时间倒序排列，最近创建的数据在最前面

15.结果管理界面中，删除所属项目，删除生成状态

16.结果管理界面中，结果列表下方增加横向分页器，用于管理大量结果报告

17.结果管理界面中，结果列表按照创建时间倒序排列，最近创建的结果报告在最前面

18.主操作界面的结果交互区需要可以展示多种类型的数据，包括模板数据、抽取数据、以docx为基础的结果报告数据
