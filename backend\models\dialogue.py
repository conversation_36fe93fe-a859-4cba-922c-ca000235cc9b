"""
对话数据模型
基于LangGraph对话存储架构设计的持久化对话模型
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
from sqlalchemy import Column, String, DateTime, Text, JSON, Integer, <PERSON>olean, <PERSON><PERSON>ey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base
import uuid

from .core import Base


class MessageRole(str, Enum):
    """消息角色枚举"""
    HUMAN = "human"
    ASSISTANT = "assistant" 
    SYSTEM = "system"
    TOOL = "tool"


class ConversationStatus(str, Enum):
    """对话状态枚举"""
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    ARCHIVED = "archived"


class InteractionType(str, Enum):
    """交互类型枚举"""
    CHAT = "chat"
    DOCUMENT_PROCESSING = "document_processing"
    REPORT_GENERATION = "report_generation"
    TEMPLATE_VALIDATION = "template_validation"


class Conversation(Base):
    """
    对话会话模型
    对应LangGraph的Thread概念
    """
    __tablename__ = "conversations"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    thread_id = Column(String(255), unique=True, nullable=False, index=True)  # LangGraph thread ID
    title = Column(String(500), nullable=True)  # 对话标题
    user_id = Column(String(255), nullable=True, index=True)  # 用户ID
    status = Column(String(50), default=ConversationStatus.ACTIVE.value)
    interaction_type = Column(String(100), default=InteractionType.CHAT.value)
    
    # 元数据
    conversation_metadata = Column(JSON, default=dict)  # 存储额外的对话元数据
    context_data = Column(JSON, default=dict)  # 对话上下文数据
    
    # 统计信息
    message_count = Column(Integer, default=0)
    last_activity = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")
    checkpoints = relationship("ConversationCheckpoint", back_populates="conversation", cascade="all, delete-orphan")


class Message(Base):
    """
    消息模型
    对应LangGraph的Message概念
    """
    __tablename__ = "messages"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    conversation_id = Column(UUID(as_uuid=True), ForeignKey("conversations.id"), nullable=False, index=True)
    
    # 消息内容
    role = Column(String(50), nullable=False)  # human, assistant, system, tool
    content = Column(Text, nullable=False)
    content_type = Column(String(50), default="text")  # text, json, file, etc.
    
    # LangGraph相关字段
    message_id = Column(String(255), nullable=True)  # LangGraph message ID
    parent_id = Column(String(255), nullable=True)  # 父消息ID，用于构建消息树
    sequence_number = Column(Integer, nullable=False)  # 消息序号
    
    # 元数据
    message_metadata = Column(JSON, default=dict)
    tool_calls = Column(JSON, default=list)  # 工具调用信息
    attachments = Column(JSON, default=list)  # 附件信息
    
    # 状态
    is_edited = Column(Boolean, default=False)
    is_deleted = Column(Boolean, default=False)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    conversation = relationship("Conversation", back_populates="messages")


class ConversationCheckpoint(Base):
    """
    对话检查点模型
    对应LangGraph的Checkpoint概念，用于状态持久化
    """
    __tablename__ = "conversation_checkpoints"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    conversation_id = Column(UUID(as_uuid=True), ForeignKey("conversations.id"), nullable=False, index=True)
    
    # LangGraph检查点数据
    checkpoint_id = Column(String(255), nullable=False, index=True)
    parent_checkpoint_id = Column(String(255), nullable=True)
    checkpoint_data = Column(JSON, nullable=False)  # 完整的状态数据
    
    # 检查点元数据
    step_number = Column(Integer, nullable=False)
    node_name = Column(String(255), nullable=True)  # 当前执行的节点
    status = Column(String(50), nullable=False)  # pending, completed, failed
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    conversation = relationship("Conversation", back_populates="checkpoints")


class HumanFeedback(Base):
    """
    人工反馈模型
    用于存储人工干预和反馈信息
    """
    __tablename__ = "human_feedback"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    conversation_id = Column(UUID(as_uuid=True), ForeignKey("conversations.id"), nullable=False, index=True)
    message_id = Column(UUID(as_uuid=True), ForeignKey("messages.id"), nullable=True, index=True)
    
    # 反馈内容
    feedback_type = Column(String(100), nullable=False)  # correction, approval, rejection, etc.
    feedback_content = Column(Text, nullable=False)
    original_content = Column(Text, nullable=True)  # 原始内容（如果是修正）
    
    # 反馈元数据
    feedback_metadata = Column(JSON, default=dict)
    user_id = Column(String(255), nullable=True)
    
    # 处理状态
    is_processed = Column(Boolean, default=False)
    processed_at = Column(DateTime, nullable=True)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class DialogueTemplate(Base):
    """
    对话模板模型
    用于存储预定义的对话模板和提示词
    """
    __tablename__ = "dialogue_templates"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # 模板内容
    template_type = Column(String(100), nullable=False)  # system_prompt, user_prompt, etc.
    template_content = Column(Text, nullable=False)
    variables = Column(JSON, default=list)  # 模板变量定义
    
    # 使用场景
    interaction_types = Column(JSON, default=list)  # 适用的交互类型
    
    # 状态
    is_active = Column(Boolean, default=True)
    version = Column(String(50), default="1.0")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
