/**
 * 对话管理API服务
 * 提供对话创建、消息发送、人工干预等功能
 */

import api from './api';

// 类型定义
export interface Conversation {
  id: string;
  thread_id: string;
  title: string;
  user_id?: string;
  status: string;
  interaction_type: string;
  message_count: number;
  last_activity: string;
  created_at: string;
  metadata: Record<string, any>;
  context_data: Record<string, any>;
}

export interface Message {
  id: string;
  role: string;
  content: string;
  content_type: string;
  sequence_number: number;
  metadata: Record<string, any>;
  created_at: string;
  is_edited: boolean;
}

export interface CreateConversationRequest {
  user_id?: string;
  title?: string;
  interaction_type?: string;
  context_data?: Record<string, any>;
}

export interface SendMessageRequest {
  message: string;
  require_human_approval?: boolean;
}

export interface ProcessDocumentRequest {
  file_path: string;
  extraction_fields: Array<{
    field_name: string;
    description: string;
    example?: string;
    format?: string;
  }>;
}

export interface HumanFeedbackRequest {
  feedback_content: string;
  feedback_type?: string;
  message_id?: string;
}

export interface AgentStatus {
  agent_name: string;
  state: string;
  current_conversation_id?: string;
  timestamp: string;
}

export interface SendMessageResponse {
  success: boolean;
  user_message: Message;
  assistant_message: Message;
  agent_status: AgentStatus;
}

export interface ProcessDocumentResponse {
  success: boolean;
  result: {
    success: boolean;
    extracted_data: Record<string, string>;
    document_path: string;
    processing_time: string;
  };
  agent_status: AgentStatus;
}

class DialogueApiService {
  /**
   * 创建新对话
   */
  async createConversation(request: CreateConversationRequest): Promise<Conversation> {
    const response = await api.post('/api/dialogue/conversations', request);
    return response.data;
  }

  /**
   * 获取对话列表
   */
  async getConversations(params?: {
    user_id?: string;
    status?: string;
    interaction_type?: string;
    limit?: number;
    offset?: number;
  }): Promise<Conversation[]> {
    const response = await api.get('/api/dialogue/conversations', { params });
    return response.data;
  }

  /**
   * 获取特定对话详情
   */
  async getConversation(conversationId: string): Promise<Conversation> {
    const response = await api.get(`/api/dialogue/conversations/${conversationId}`);
    return response.data;
  }

  /**
   * 获取对话消息历史
   */
  async getConversationMessages(
    conversationId: string,
    params?: {
      limit?: number;
      before_sequence?: number;
      after_sequence?: number;
    }
  ): Promise<Message[]> {
    const response = await api.get(
      `/api/dialogue/conversations/${conversationId}/messages`,
      { params }
    );
    return response.data;
  }

  /**
   * 发送消息
   */
  async sendMessage(
    conversationId: string,
    request: SendMessageRequest
  ): Promise<SendMessageResponse> {
    const response = await api.post(
      `/api/dialogue/conversations/${conversationId}/messages`,
      request
    );
    return response.data;
  }

  /**
   * 处理文档
   */
  async processDocument(
    conversationId: string,
    request: ProcessDocumentRequest
  ): Promise<ProcessDocumentResponse> {
    const response = await api.post(
      `/api/dialogue/conversations/${conversationId}/process-document`,
      request
    );
    return response.data;
  }

  /**
   * 提交人工反馈
   */
  async submitHumanFeedback(
    conversationId: string,
    request: HumanFeedbackRequest
  ): Promise<{ success: boolean; message: string; agent_status: AgentStatus }> {
    const response = await api.post(
      `/api/dialogue/conversations/${conversationId}/human-feedback`,
      request
    );
    return response.data;
  }

  /**
   * 请求人工干预
   */
  async requestHumanIntervention(
    conversationId: string,
    message: string,
    interventionType: string = 'approval',
    contextData?: Record<string, any>
  ): Promise<{ success: boolean; message: string; agent_status: AgentStatus }> {
    const response = await api.post(
      `/api/dialogue/conversations/${conversationId}/request-intervention`,
      {
        message,
        intervention_type: interventionType,
        context_data: contextData,
      }
    );
    return response.data;
  }

  /**
   * 更新对话信息
   */
  async updateConversation(
    conversationId: string,
    updates: {
      title?: string;
      status?: string;
      metadata?: Record<string, any>;
      context_data?: Record<string, any>;
    }
  ): Promise<{ success: boolean; message: string; conversation: Conversation }> {
    const response = await api.put(
      `/api/dialogue/conversations/${conversationId}`,
      updates
    );
    return response.data;
  }

  /**
   * 获取智能体状态
   */
  async getAgentStatus(): Promise<AgentStatus> {
    const response = await api.get('/api/dialogue/agent/status');
    return response.data;
  }

  /**
   * 获取对话历史记录
   */
  async getConversationHistory(
    conversationId: string,
    limit?: number
  ): Promise<{ success: boolean; conversation_id: string; message_count: number; messages: any[] }> {
    const response = await api.get(
      `/api/dialogue/conversations/${conversationId}/history`,
      { params: { limit } }
    );
    return response.data;
  }

  /**
   * 删除对话
   */
  async deleteConversation(conversationId: string): Promise<{ success: boolean; message: string }> {
    const response = await api.delete(`/api/dialogue/conversations/${conversationId}`);
    return response.data;
  }

  /**
   * 获取对话的长期记忆
   */
  async getConversationMemory(
    conversationId: string,
    userId?: string
  ): Promise<{ success: boolean; memories: any[] }> {
    const response = await api.get(
      `/api/dialogue/conversations/${conversationId}/memory`,
      { params: { user_id: userId } }
    );
    return response.data;
  }

  /**
   * 清空对话历史记录
   */
  async clearConversationHistory(conversationId: string): Promise<{ success: boolean; message: string }> {
    const response = await api.post(`/api/dialogue/conversations/${conversationId}/clear-history`);
    return response.data;
  }

  /**
   * 获取用户的所有对话列表
   */
  async getUserConversations(
    userId: string,
    params?: {
      limit?: number;
      offset?: number;
    }
  ): Promise<{
    success: boolean;
    user_id: string;
    total_conversations: number;
    conversations: any[];
    pagination: {
      limit: number;
      offset: number;
      has_more: boolean;
    };
  }> {
    const response = await api.get(`/api/dialogue/users/${userId}/conversations`, { params });
    return response.data;
  }
}

export const dialogueApi = new DialogueApiService();
export default dialogueApi;
