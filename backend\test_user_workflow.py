#!/usr/bin/env python3
"""
测试用户完整工作流程
模拟用户上传文档并确认模板的过程
"""

import requests
import tempfile
import os
import json
import time

def test_user_workflow():
    """测试用户完整工作流程"""
    
    # 创建测试文档 - 使用更真实的会议纪要内容
    test_content = """
住建厅科研验收项目内部审查会

会议时间：2025年6月26日(周四)上午9:00
会议地点：广州地铁设计大厦0513会议室(白云区云城北二路129号)
会议主持：袁泉
会议议题：住建厅科研验收项目内部审查

参会人员：
袁泉、苏拓、阮艳妹、张晶潇、周林仁、房帅、覃羽丰、谢信、刘静、桂林、李远峰、赵梓茗

会议内容：
1. 轨道交通余泥渣土资源化与性能提升关键技术与示范 (9:00~10:00, 汇报人：张晶潇)
2. 基于光纤技术的城市轨道交通控制保护区异物入侵检测技术研究与应用 (10:00～11:00, 汇报人：刘静)  
3. 基于BIM的轨道交通消防智能审查平台研发 (11:00～12:00, 汇报人：桂林)

其他安排：
请各参会人员在会议开始前10分钟到达会议室。

纪要接收：科技质量部; 余泥渣土资源化项目组; 光纤技术项目组; 消防智能审查平台项目组
    """
    
    print("🚀 开始用户工作流测试...")
    
    # 第1步: 启动工作流
    print("\n📝 第1步: 启动工作流...")
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(test_content)
        temp_file_path = f.name
    
    try:
        with open(temp_file_path, 'rb') as f:
            files = {'files': ('meeting_record.txt', f, 'text/plain')}
            data = {'text_input': '请帮我生成会议纪要'}
            
            response = requests.post(
                'http://localhost:8000/api/workflow/langgraph/start',
                files=files,
                data=data
            )
        
        print(f"启动状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            workflow_id = result.get('workflow_id')
            print(f"工作流ID: {workflow_id}")
            
            requires_user_action = result.get('data', {}).get('requires_user_action', False)
            print(f"需要用户操作: {requires_user_action}")
            
            if requires_user_action:
                print("\n✅ 工作流正确暂停，等待用户确认模板")
                
                # 第2步: 确认模板
                print("\n📋 第2步: 确认推荐的模板...")
                time.sleep(2)  # 等待2秒
                
                confirm_response = requests.post(
                    f'http://localhost:8000/api/workflow/langgraph/resume/{workflow_id}',
                    json={'action': 'confirm'}
                )
                
                print(f"模板确认状态码: {confirm_response.status_code}")
                if confirm_response.status_code == 200:
                    confirm_result = confirm_response.json()
                    print(f"确认状态: {confirm_result.get('status')}")
                    
                    # 第3步: 等待数据提取完成并检查状态
                    print("\n🔍 第3步: 等待数据提取完成...")
                    
                    # 多次检查状态，等待数据提取完成
                    for i in range(10):  # 最多等待10次，每次2秒
                        time.sleep(2)
                        print(f"  检查状态 ({i+1}/10)...")
                        
                        status_response = requests.get(
                            f'http://localhost:8000/api/workflow/langgraph/status/{workflow_id}'
                        )
                        
                        if status_response.status_code == 200:
                            status_data = status_response.json()
                            current_stage = status_data.get('current_stage')
                            pending_interactions = status_data.get('pending_interactions', [])
                            extracted_data = status_data.get('extracted_data', {})
                            extraction_results = status_data.get('extraction_results', [])
                            
                            print(f"    当前阶段: {current_stage}")
                            print(f"    待处理交互: {pending_interactions}")
                            print(f"    提取数据数量: {len(extracted_data)}")
                            print(f"    提取结果数量: {len(extraction_results)}")
                            
                            # 如果有待处理的交互，说明需要用户确认数据
                            if pending_interactions:
                                print(f"\n🎉 数据提取完成！需要用户确认数据")
                                print(f"待处理交互类型: {pending_interactions}")
                                
                                # 显示提取的数据
                                if extraction_results:
                                    print("\n📊 提取的数据:")
                                    for result in extraction_results[:5]:  # 只显示前5个
                                        field_name = result.get('field_name', 'unknown')
                                        field_value = result.get('field_value', 'N/A')
                                        confidence = result.get('confidence', 0)
                                        print(f"  {field_name}: {field_value} (置信度: {confidence:.2f})")
                                
                                # 第4步: 确认数据
                                print("\n✅ 第4步: 确认提取的数据...")
                                data_confirm_response = requests.post(
                                    f'http://localhost:8000/api/workflow/langgraph/resume/{workflow_id}',
                                    json={'action': 'confirm'}
                                )
                                
                                if data_confirm_response.status_code == 200:
                                    print("✅ 数据确认成功！工作流应该继续执行...")
                                else:
                                    print(f"❌ 数据确认失败: {data_confirm_response.status_code}")
                                
                                break
                            
                            # 如果没有待处理交互但有提取数据，可能已经完成
                            elif extracted_data or extraction_results:
                                print(f"\n✅ 数据提取完成，无需用户确认")
                                break
                        else:
                            print(f"    ❌ 状态检查失败: {status_response.status_code}")
                    else:
                        print("\n⚠️ 等待超时，数据提取可能仍在进行中")
                        
                else:
                    print(f"❌ 模板确认失败: {confirm_response.status_code}")
                    print(confirm_response.text)
            else:
                print("❌ 工作流没有正确暂停等待用户输入")
        else:
            print(f"❌ 启动工作流失败: {response.status_code}")
            print(response.text)
            
    finally:
        # 清理临时文件
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
    
    print("\n✅ 用户工作流测试完成")

if __name__ == "__main__":
    test_user_workflow()
