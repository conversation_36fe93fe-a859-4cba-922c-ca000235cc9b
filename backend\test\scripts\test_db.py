#!/usr/bin/env python3
"""
数据库连接测试脚本
"""

import os
import sys

# 设置环境变量
os.environ['DATABASE_URL'] = 'postgresql://report_user:report_pass@localhost:5432/report_gen'

def test_basic_connection():
    """测试基础数据库连接"""
    print("🔍 测试基础数据库连接...")
    
    try:
        from config import settings
        print(f"✅ 配置加载成功: {settings.database_url}")
        
        import sqlalchemy
        from sqlalchemy import create_engine, text
        print(f"✅ SQLAlchemy版本: {sqlalchemy.__version__}")
        
        engine = create_engine(settings.database_url, echo=False)
        print("✅ 数据库引擎创建成功")
        
        with engine.connect() as conn:
            result = conn.execute(text('SELECT version()'))
            version = result.fetchone()[0]
            print(f"✅ 数据库连接成功: {version[:50]}...")
            
            # 测试中文支持
            result = conn.execute(text("SELECT '测试中文' as test"))
            chinese_test = result.fetchone()[0]
            print(f"✅ 中文支持正常: {chinese_test}")
            
            # 测试数据库信息
            result = conn.execute(text('SELECT current_database(), current_user'))
            db_info = result.fetchone()
            print(f"✅ 数据库: {db_info[0]}, 用户: {db_info[1]}")
            
        return True
        
    except Exception as e:
        print(f"❌ 基础连接测试失败: {e}")
        return False


def test_models():
    """测试SQLAlchemy模型"""
    print("\n🔍 测试SQLAlchemy模型...")
    
    try:
        from models.core import engine, create_tables, Base
        print("✅ 模型导入成功")
        
        # 测试数据库连接
        with engine.connect() as conn:
            print("✅ 模型数据库连接正常")
        
        # 创建表
        print("📝 创建数据库表...")
        create_tables()
        print("✅ 数据库表创建成功")
        
        # 检查创建的表
        from sqlalchemy import text
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name
            """))
            tables = [row[0] for row in result.fetchall()]
            print(f"✅ 创建的表: {tables}")
            
        return True
        
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_crud_operations():
    """测试基础CRUD操作"""
    print("\n🔍 测试基础CRUD操作...")
    
    try:
        from models.core import SessionLocal
        from models.user import User
        
        # 创建会话
        db = SessionLocal()
        
        # 创建测试用户
        test_user = User(
            username="test_user",
            email="<EMAIL>",
            password_hash="test_hash",
            full_name="测试用户"
        )
        
        db.add(test_user)
        db.commit()
        print("✅ 用户创建成功")
        
        # 查询用户
        user = db.query(User).filter(User.username == "test_user").first()
        if user:
            print(f"✅ 用户查询成功: {user.username} - {user.full_name}")
        
        # 清理测试数据
        db.delete(user)
        db.commit()
        print("✅ 测试数据清理完成")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ CRUD测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始数据库连接测试\n")
    
    # 测试基础连接
    if not test_basic_connection():
        print("\n❌ 基础连接测试失败，停止后续测试")
        sys.exit(1)
    
    # 测试模型
    if not test_models():
        print("\n❌ 模型测试失败，停止后续测试")
        sys.exit(1)
    
    # 测试CRUD操作
    if not test_crud_operations():
        print("\n❌ CRUD测试失败")
        sys.exit(1)
    
    print("\n🎉 所有数据库测试通过！")
    print("✅ 数据库连接问题已解决")


if __name__ == "__main__":
    main()
