"""
文档处理服务
支持docx和txt文档的解析和内容提取
"""

import logging
import os
from pathlib import Path
from typing import Dict, List, Optional, Union
from dataclasses import dataclass
from enum import Enum

from docx import Document
from docx.shared import Inches
from docx.table import Table
from docx.text.paragraph import Paragraph

# 配置日志
logger = logging.getLogger(__name__)


class DocumentType(Enum):
    """文档类型枚举"""
    DOCX = "docx"
    TXT = "txt"
    UNKNOWN = "unknown"


@dataclass
class DocumentContent:
    """文档内容数据类"""
    file_path: str
    file_name: str
    document_type: DocumentType
    raw_text: str
    paragraphs: List[str]
    tables: List[Dict]
    metadata: Dict
    success: bool = True
    error: Optional[str] = None


class DocumentService:
    """文档处理服务类"""
    
    def __init__(self):
        self.supported_extensions = {'.docx', '.txt'}
    
    def get_document_type(self, file_path: Union[str, Path]) -> DocumentType:
        """
        根据文件扩展名判断文档类型
        
        Args:
            file_path: 文件路径
            
        Returns:
            DocumentType: 文档类型
        """
        file_path = Path(file_path)
        extension = file_path.suffix.lower()
        
        if extension == '.docx':
            return DocumentType.DOCX
        elif extension == '.txt':
            return DocumentType.TXT
        else:
            return DocumentType.UNKNOWN
    
    def is_supported(self, file_path: Union[str, Path]) -> bool:
        """
        检查文件是否支持解析
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否支持
        """
        file_path = Path(file_path)
        return file_path.suffix.lower() in self.supported_extensions
    
    def parse_document(self, file_path: Union[str, Path]) -> DocumentContent:
        """
        解析文档内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            DocumentContent: 解析结果
        """
        file_path = Path(file_path)
        
        # 检查文件是否存在
        if not file_path.exists():
            return DocumentContent(
                file_path=str(file_path),
                file_name=file_path.name,
                document_type=DocumentType.UNKNOWN,
                raw_text="",
                paragraphs=[],
                tables=[],
                metadata={},
                success=False,
                error=f"文件不存在: {file_path}"
            )
        
        # 获取文档类型
        doc_type = self.get_document_type(file_path)
        
        if doc_type == DocumentType.DOCX:
            return self._parse_docx(file_path)
        elif doc_type == DocumentType.TXT:
            return self._parse_txt(file_path)
        else:
            return DocumentContent(
                file_path=str(file_path),
                file_name=file_path.name,
                document_type=doc_type,
                raw_text="",
                paragraphs=[],
                tables=[],
                metadata={},
                success=False,
                error=f"不支持的文件类型: {file_path.suffix}"
            )
    
    def _parse_docx(self, file_path: Path) -> DocumentContent:
        """
        解析docx文档
        
        Args:
            file_path: docx文件路径
            
        Returns:
            DocumentContent: 解析结果
        """
        try:
            # 打开docx文档
            doc = Document(file_path)
            
            # 提取段落
            paragraphs = []
            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if text:  # 只保留非空段落
                    paragraphs.append(text)
            
            # 提取表格
            tables = []
            for table in doc.tables:
                table_data = self._extract_table_data(table)
                if table_data:
                    tables.append(table_data)
            
            # 合并所有文本
            raw_text = "\n".join(paragraphs)
            
            # 如果有表格，也添加到文本中
            for table in tables:
                if table.get('text'):
                    raw_text += "\n\n" + table['text']
            
            # 提取元数据
            metadata = self._extract_docx_metadata(doc)
            
            return DocumentContent(
                file_path=str(file_path),
                file_name=file_path.name,
                document_type=DocumentType.DOCX,
                raw_text=raw_text,
                paragraphs=paragraphs,
                tables=tables,
                metadata=metadata,
                success=True
            )
            
        except Exception as e:
            logger.error(f"解析docx文档失败: {file_path}, 错误: {e}")
            return DocumentContent(
                file_path=str(file_path),
                file_name=file_path.name,
                document_type=DocumentType.DOCX,
                raw_text="",
                paragraphs=[],
                tables=[],
                metadata={},
                success=False,
                error=f"解析docx文档失败: {e}"
            )
    
    def _parse_txt(self, file_path: Path) -> DocumentContent:
        """
        解析txt文档
        
        Args:
            file_path: txt文件路径
            
        Returns:
            DocumentContent: 解析结果
        """
        try:
            # 尝试多种编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16']
            content = None
            used_encoding = None
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    used_encoding = encoding
                    break
                except UnicodeDecodeError:
                    continue
            
            if content is None:
                raise ValueError("无法使用支持的编码读取文件")
            
            # 分割段落（按空行分割）
            paragraphs = []
            for paragraph in content.split('\n\n'):
                text = paragraph.strip()
                if text:
                    paragraphs.append(text)
            
            # 如果没有空行分割，按单行分割
            if not paragraphs:
                paragraphs = [line.strip() for line in content.split('\n') if line.strip()]
            
            # 提取元数据
            metadata = {
                'encoding': used_encoding,
                'file_size': file_path.stat().st_size,
                'line_count': len(content.split('\n'))
            }
            
            return DocumentContent(
                file_path=str(file_path),
                file_name=file_path.name,
                document_type=DocumentType.TXT,
                raw_text=content,
                paragraphs=paragraphs,
                tables=[],  # txt文件没有表格
                metadata=metadata,
                success=True
            )
            
        except Exception as e:
            logger.error(f"解析txt文档失败: {file_path}, 错误: {e}")
            return DocumentContent(
                file_path=str(file_path),
                file_name=file_path.name,
                document_type=DocumentType.TXT,
                raw_text="",
                paragraphs=[],
                tables=[],
                metadata={},
                success=False,
                error=f"解析txt文档失败: {e}"
            )
    
    def _extract_table_data(self, table: Table) -> Dict:
        """
        提取表格数据
        
        Args:
            table: docx表格对象
            
        Returns:
            Dict: 表格数据
        """
        try:
            rows = []
            for row in table.rows:
                row_data = []
                for cell in row.cells:
                    cell_text = cell.text.strip()
                    row_data.append(cell_text)
                rows.append(row_data)
            
            # 生成表格的文本表示
            table_text = ""
            for row in rows:
                table_text += " | ".join(row) + "\n"
            
            return {
                'rows': rows,
                'text': table_text.strip(),
                'row_count': len(rows),
                'col_count': len(rows[0]) if rows else 0
            }
            
        except Exception as e:
            logger.error(f"提取表格数据失败: {e}")
            return {}
    
    def _extract_docx_metadata(self, doc: Document) -> Dict:
        """
        提取docx文档元数据
        
        Args:
            doc: docx文档对象
            
        Returns:
            Dict: 元数据
        """
        try:
            core_props = doc.core_properties
            
            metadata = {
                'title': core_props.title or "",
                'author': core_props.author or "",
                'subject': core_props.subject or "",
                'created': core_props.created.isoformat() if core_props.created else "",
                'modified': core_props.modified.isoformat() if core_props.modified else "",
                'paragraph_count': len(doc.paragraphs),
                'table_count': len(doc.tables)
            }
            
            return metadata
            
        except Exception as e:
            logger.error(f"提取docx元数据失败: {e}")
            return {}


# 全局文档服务实例
document_service = DocumentService()
