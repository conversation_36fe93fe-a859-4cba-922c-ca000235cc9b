#!/usr/bin/env python3
"""
调试工作流状态
"""

import requests
import json

def check_workflow_status(workflow_id):
    """检查工作流状态"""
    
    print(f"🔍 检查工作流状态: {workflow_id}")
    
    try:
        response = requests.get(f'http://localhost:8000/api/workflow/langgraph/status/{workflow_id}')
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"完整状态响应:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 分析关键信息
            print(f"\n📊 关键信息分析:")
            print(f"成功: {result.get('success')}")
            print(f"当前阶段: {result.get('current_stage')}")
            print(f"待处理交互: {result.get('pending_interactions', [])}")
            print(f"提取统计: {result.get('extraction_stats', {})}")
            print(f"错误: {result.get('errors', [])}")
            
            # 检查是否有提取数据
            if 'extracted_data' in result:
                print(f"提取数据: {result['extracted_data']}")
            
            # 检查是否需要用户操作
            if result.get('requires_user_action'):
                print(f"⚠️ 需要用户操作")
            else:
                print(f"✅ 不需要用户操作")
                
        else:
            print(f"❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 检查状态失败: {e}")

if __name__ == "__main__":
    # 检查您提到的工作流ID
    workflow_id = "b483685f-2955-4161-a60e-263b20d6f53f"
    check_workflow_status(workflow_id)
