"""
智能体相关的Pydantic模式定义
"""

from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
from datetime import datetime
from uuid import UUID


class AgentSessionBase(BaseModel):
    """智能体会话基础模式"""
    session_name: Optional[str] = Field(None, description="会话名称")
    session_type: str = Field("report_generation", description="会话类型")
    context_data: Optional[Dict[str, Any]] = Field(None, description="上下文数据")


class AgentSessionCreate(AgentSessionBase):
    """创建智能体会话请求"""
    user_id: Optional[UUID] = Field(None, description="用户ID")


class AgentSessionResponse(AgentSessionBase):
    """智能体会话响应"""
    id: UUID = Field(..., description="会话ID")
    status: str = Field(..., description="会话状态")
    current_agent: Optional[str] = Field(None, description="当前智能体")
    current_stage: Optional[str] = Field(None, description="当前阶段")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")

    class Config:
        from_attributes = True


class ConversationBase(BaseModel):
    """对话基础模式"""
    message_type: str = Field(..., description="消息类型")
    sender: Optional[str] = Field(None, description="发送者")
    content: str = Field(..., description="消息内容")
    message_metadata: Optional[Dict[str, Any]] = Field(None, description="消息元数据")
    tool_calls: Optional[Dict[str, Any]] = Field(None, description="工具调用信息")
    attachments: Optional[Dict[str, Any]] = Field(None, description="附件信息")


class ConversationCreate(ConversationBase):
    """创建对话请求"""
    pass


class ConversationResponse(ConversationBase):
    """对话响应"""
    id: UUID = Field(..., description="对话ID")
    session_id: UUID = Field(..., description="会话ID")
    created_at: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True


class WorkflowExecuteRequest(BaseModel):
    """工作流执行请求"""
    user_request: str = Field(..., description="用户请求描述")
    document_paths: List[str] = Field(default_factory=list, description="文档路径列表")
    template_id: Optional[str] = Field(None, description="指定的模板ID")
    session_id: Optional[UUID] = Field(None, description="关联的会话ID")
    additional_context: Optional[Dict[str, Any]] = Field(None, description="额外上下文")
    async_execution: bool = Field(False, description="是否异步执行")


class WorkflowExecuteResponse(BaseModel):
    """工作流执行响应"""
    workflow_id: str = Field(..., description="工作流ID")
    status: str = Field(..., description="执行状态")
    final_state: Optional[Dict[str, Any]] = Field(None, description="最终状态")
    errors: List[str] = Field(default_factory=list, description="错误列表")
    report_generation: Optional[Dict[str, Any]] = Field(None, description="报告生成结果")
    message: str = Field("", description="响应消息")


class HumanInteractionRequest(BaseModel):
    """人机交互请求"""
    action: str = Field(..., description="交互动作")
    field_updates: Optional[Dict[str, str]] = Field(None, description="字段更新")
    selected_option: Optional[str] = Field(None, description="选择的选项")
    additional_input: Optional[Dict[str, Any]] = Field(None, description="额外输入")
    template_selection: Optional[str] = Field(None, description="模板选择")
    batch_updates: Optional[List[Dict[str, Any]]] = Field(None, description="批量更新")


class HumanInteractionResponse(BaseModel):
    """人机交互响应"""
    interaction_id: str = Field(..., description="交互ID")
    status: str = Field(..., description="处理状态")
    message: str = Field("", description="响应消息")
    next_action: str = Field("", description="下一步动作")
    interaction_data: Optional[Dict[str, Any]] = Field(None, description="交互数据")
    requires_user_input: bool = Field(False, description="是否需要用户输入")


class ToolCallBase(BaseModel):
    """工具调用基础模式"""
    tool_name: str = Field(..., description="工具名称")
    tool_input: Dict[str, Any] = Field(..., description="工具输入")
    status: str = Field("pending", description="执行状态")


class ToolCallCreate(ToolCallBase):
    """创建工具调用请求"""
    session_id: UUID = Field(..., description="会话ID")
    conversation_id: Optional[UUID] = Field(None, description="对话ID")


class ToolCallResponse(ToolCallBase):
    """工具调用响应"""
    id: UUID = Field(..., description="工具调用ID")
    session_id: UUID = Field(..., description="会话ID")
    conversation_id: Optional[UUID] = Field(None, description="对话ID")
    tool_output: Optional[Dict[str, Any]] = Field(None, description="工具输出")
    error_message: Optional[str] = Field(None, description="错误信息")
    execution_time: Optional[int] = Field(None, description="执行时间(毫秒)")
    created_at: datetime = Field(..., description="创建时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")

    class Config:
        from_attributes = True


class WorkflowStatusResponse(BaseModel):
    """工作流状态响应"""
    workflow_id: str = Field(..., description="工作流ID")
    status: str = Field(..., description="当前状态")
    current_stage: Optional[str] = Field(None, description="当前阶段")
    progress: float = Field(0.0, description="进度百分比")
    message: str = Field("", description="状态消息")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")


class AgentMetrics(BaseModel):
    """智能体指标"""
    total_sessions: int = Field(0, description="总会话数")
    active_sessions: int = Field(0, description="活跃会话数")
    completed_sessions: int = Field(0, description="完成会话数")
    failed_sessions: int = Field(0, description="失败会话数")
    avg_completion_time: float = Field(0.0, description="平均完成时间(秒)")
    success_rate: float = Field(0.0, description="成功率")


class SessionSummary(BaseModel):
    """会话摘要"""
    session_id: UUID = Field(..., description="会话ID")
    session_name: Optional[str] = Field(None, description="会话名称")
    status: str = Field(..., description="会话状态")
    created_at: datetime = Field(..., description="创建时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    duration: Optional[float] = Field(None, description="持续时间(秒)")
    message_count: int = Field(0, description="消息数量")
    tool_call_count: int = Field(0, description="工具调用数量")


class InteractionOption(BaseModel):
    """交互选项"""
    value: str = Field(..., description="选项值")
    label: str = Field(..., description="选项标签")
    description: Optional[str] = Field(None, description="选项描述")
    primary: bool = Field(False, description="是否为主要选项")
    secondary: bool = Field(False, description="是否为次要选项")
    disabled: bool = Field(False, description="是否禁用")


class InteractionPrompt(BaseModel):
    """交互提示"""
    interaction_type: str = Field(..., description="交互类型")
    title: str = Field(..., description="标题")
    message: str = Field(..., description="提示消息")
    options: List[InteractionOption] = Field(default_factory=list, description="可选项")
    required_fields: List[str] = Field(default_factory=list, description="必填字段")
    timeout: int = Field(300, description="超时时间(秒)")
    quick_actions: List[InteractionOption] = Field(default_factory=list, description="快速操作")
    additional_actions: List[InteractionOption] = Field(default_factory=list, description="附加操作")
    data: Optional[Dict[str, Any]] = Field(None, description="附加数据")
    preview_available: bool = Field(False, description="是否可预览")


class AgentError(BaseModel):
    """智能体错误"""
    error_code: str = Field(..., description="错误代码")
    error_message: str = Field(..., description="错误消息")
    error_details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
    timestamp: datetime = Field(..., description="错误时间")
    agent_name: Optional[str] = Field(None, description="智能体名称")
    stage: Optional[str] = Field(None, description="出错阶段")


class WorkflowConfig(BaseModel):
    """工作流配置"""
    max_iterations: int = Field(10, description="最大迭代次数")
    timeout: int = Field(3600, description="超时时间(秒)")
    enable_human_interaction: bool = Field(True, description="启用人机交互")
    auto_retry: bool = Field(True, description="自动重试")
    retry_count: int = Field(3, description="重试次数")
    quality_threshold: float = Field(0.7, description="质量阈值")


class BatchWorkflowRequest(BaseModel):
    """批量工作流请求"""
    requests: List[WorkflowExecuteRequest] = Field(..., description="工作流请求列表")
    batch_name: Optional[str] = Field(None, description="批次名称")
    parallel_execution: bool = Field(False, description="并行执行")
    max_concurrent: int = Field(5, description="最大并发数")


class BatchWorkflowResponse(BaseModel):
    """批量工作流响应"""
    batch_id: str = Field(..., description="批次ID")
    total_requests: int = Field(..., description="总请求数")
    completed_requests: int = Field(0, description="完成请求数")
    failed_requests: int = Field(0, description="失败请求数")
    status: str = Field("running", description="批次状态")
    results: List[WorkflowExecuteResponse] = Field(default_factory=list, description="执行结果")
    started_at: datetime = Field(..., description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")


class TemplateMatchInfo(BaseModel):
    """模板匹配信息"""
    confidence: str = Field(..., description="匹配置信度文本")
    score: float = Field(..., description="匹配分数")
    total_available: int = Field(..., description="可用模板总数")
    matched_count: int = Field(..., description="匹配模板数量")


class ExtractionSummary(BaseModel):
    """提取摘要信息"""
    total_fields: int = Field(..., description="总字段数")
    extracted_fields: int = Field(..., description="已提取字段数")
    success_rate: float = Field(..., description="成功率")
    quality_level: str = Field(..., description="质量等级")
    quality_color: str = Field(..., description="质量颜色")
    avg_confidence: float = Field(0.0, description="平均置信度")


class ExtractionResult(BaseModel):
    """提取结果"""
    field_name: str = Field(..., description="字段名称")
    field_value: str = Field(..., description="字段值")
    confidence: float = Field(..., description="置信度")
    source_location: Optional[str] = Field(None, description="来源位置")
    extraction_method: str = Field(..., description="提取方法")
    is_verified: bool = Field(False, description="是否已验证")
    needs_human_input: bool = Field(False, description="是否需要人工输入")


class ReviewSection(BaseModel):
    """审查部分"""
    title: str = Field(..., description="标题")
    fields: Optional[List[ExtractionResult]] = Field(None, description="字段列表")
    status: Optional[str] = Field(None, description="状态")
    high_confidence: Optional[int] = Field(None, description="高置信度数量")
    medium_confidence: Optional[int] = Field(None, description="中等置信度数量")
    low_confidence: Optional[int] = Field(None, description="低置信度数量")


class TemplateConfirmationData(BaseModel):
    """模板确认数据"""
    type: str = Field(..., description="交互类型")
    message: str = Field(..., description="消息")
    templates: List[Dict[str, Any]] = Field(..., description="模板列表")
    default_selection: int = Field(..., description="默认选择")
    match_info: TemplateMatchInfo = Field(..., description="匹配信息")
    options: List[InteractionOption] = Field(..., description="选项列表")
    additional_actions: List[InteractionOption] = Field(..., description="附加操作")


class DataExtractionReviewData(BaseModel):
    """数据提取审查数据"""
    type: str = Field(..., description="交互类型")
    message: str = Field(..., description="消息")
    extraction_summary: ExtractionSummary = Field(..., description="提取摘要")
    extraction_results: List[ExtractionResult] = Field(..., description="提取结果")
    missing_fields: List[ExtractionResult] = Field(..., description="缺失字段")
    low_confidence_fields: List[ExtractionResult] = Field(..., description="低置信度字段")
    review_sections: List[ReviewSection] = Field(..., description="审查部分")
    options: List[InteractionOption] = Field(..., description="选项列表")
    quick_actions: List[InteractionOption] = Field(..., description="快速操作")


class DataExtractionSuccessData(BaseModel):
    """数据提取成功数据"""
    type: str = Field(..., description="交互类型")
    message: str = Field(..., description="消息")
    extraction_summary: ExtractionSummary = Field(..., description="提取摘要")
    extraction_results: List[ExtractionResult] = Field(..., description="提取结果")
    options: List[InteractionOption] = Field(..., description="选项列表")
    preview_available: bool = Field(..., description="是否可预览")
