# 轨道交通智能报告助手 - 执行计划

**文档类型:** 执行计划  
**版本:** 1.0  
**创建日期:** 2024年6月1日  
**适用对象:** 单人开发团队 (AI辅助开发)  
**参考文档:** 《轨道交通智能报告助手 - 产品开发实施方案》

## 一、总体开发计划

本执行计划基于《轨道交通智能报告助手 - 产品开发实施方案》中的里程碑规划，针对单人开发、Cursor辅助开发场景进行了细化。计划分为5个主要阶段，每个阶段2-4周不等，总计约15周时间完成MVP版本的开发。

### 总体进度安排

| 阶段 | 名称 | 起止时间 | 工作日 | 主要交付物 |
|------|------|---------|-------|-----------|
| 第一阶段 | 基础框架搭建 | 2024-06-01 至 2024-06-21 | 15天 | 系统基础架构与核心功能 |
| 第二阶段 | 文档处理能力开发 | 2024-06-24 至 2024-07-19 | 20天 | 文档解析与数据抽取功能 |
| 第三阶段 | 交互界面与用户体验 | 2024-07-22 至 2024-08-09 | 15天 | 用户界面与编辑功能 |
| 第四阶段 | 报告生成与导出 | 2024-08-12 至 2024-08-23 | 10天 | 报告生成与导出功能 |
| 第五阶段 | 测试、优化与部署 | 2024-08-26 至 2024-09-13 | 15天 | 系统测试、优化与部署 |

## 二、详细工作计划

### 第一阶段：基础框架搭建 (2024-06-01 至 2024-06-21)

#### 第1周：环境准备与项目初始化

| 日期 | 工作内容 | 具体任务 | 完成标准 | AI辅助要点 |
|------|---------|---------|---------|------------|
| 06-01 | 开发环境准备 | 1. 安装Python 3.12<br>2. 配置VSCode/Cursor<br>3. 安装Docker和Docker Compose<br>4. 准备Git仓库 | 开发环境完全可用，各工具正常运行 | 使用Cursor生成环境配置脚本 |
| 06-02 | 项目脚手架创建 | 1. 初始化Flask AppBuilder项目<br>2. 配置项目结构<br>3. 创建基础目录结构 | 项目可启动，基本结构合理 | 使用Cursor生成项目初始化代码 |
| 06-03 | 数据库设计 | 1. 创建PostgreSQL模式<br>2. 设计核心数据表<br>3. 设计索引策略 | 数据库脚本可执行，结构符合需求 | 使用Cursor生成SQL脚本 |
| 06-04 | 用户认证系统 | 1. 配置Flask-AppBuilder安全模块<br>2. 实现用户模型与视图<br>3. 设置基本角色与权限 | 用户可注册、登录、管理角色 | 使用Cursor生成认证代码 |
| 06-05 | 基础API架构 | 1. 设计API响应格式<br>2. 实现API错误处理<br>3. 创建API测试端点 | API框架可用，响应格式统一 | 使用Cursor生成API基础框架 |

#### 第2-3周：核心功能模块开发

| 日期 | 工作内容 | 具体任务 | 完成标准 | AI辅助要点 |
|------|---------|---------|---------|------------|
| 06-08~06-09 | 项目管理模块 | 1. 创建项目模型类<br>2. 实现项目CRUD接口<br>3. 创建项目列表与详情视图 | 项目可创建、查看、编辑、删除 | 使用Cursor生成模型类和API |
| 06-10~06-11 | 模板管理模块 | 1. 创建模板模型类<br>2. 实现模板上传与解析功能<br>3. 创建模板CRUD接口 | 模板可上传、查看、编辑、删除 | 使用Cursor生成文件处理代码 |
| 06-12~06-13 | 数据源管理模块 | 1. 创建数据源模型类<br>2. 实现文件上传功能<br>3. 创建数据源CRUD接口 | 数据源可上传、查看、编辑、删除 | 使用Cursor生成文件处理代码 |
| 06-14~06-16 | Celery任务系统 | 1. 配置Redis与Celery<br>2. 实现任务队列<br>3. 创建任务监控接口 | 后台任务可提交、监控、取消 | 使用Cursor生成异步任务代码 |
| 06-17~06-18 | 基础UI框架 | 1. 设计页面布局<br>2. 实现导航菜单<br>3. 创建基础模板 | 页面布局合理，导航正常工作 | 使用Cursor生成HTML/CSS模板 |
| 06-19~06-21 | 阶段整合测试 | 1. 集成各个模块<br>2. 测试基本流程<br>3. 修复问题并优化 | 所有基础功能可用，无明显bug | 使用Cursor生成测试代码 |

#### 第一阶段检查项

- [ ] 项目脚手架完成并可正常运行
- [ ] 数据库结构已创建且符合设计
- [ ] 用户认证系统可正常工作
- [ ] 项目管理模块功能完整
- [ ] 模板管理模块功能完整
- [ ] 数据源管理模块功能完整
- [ ] Celery任务系统可正常工作
- [ ] 基础UI框架已搭建完成

### 第二阶段：文档处理能力开发 (2024-06-24 至 2024-07-19)

#### 第4周：LLM API集成

| 日期 | 工作内容 | 具体任务 | 完成标准 | AI辅助要点 |
|------|---------|---------|---------|------------|
| 06-24 | LLM API设计 | 1. 设计LLM API调用接口<br>2. 实现API客户端<br>3. 添加重试与错误处理 | API可正常调用，处理错误情况 | 使用Cursor生成API客户端代码 |
| 06-25~06-26 | 提示工程实现 | 1. 设计基础提示模板<br>2. 实现提示管理系统<br>3. 创建提示测试工具 | 提示可动态配置，测试工具可用 | 使用Cursor生成提示模板 |
| 06-27~06-28 | 文档结构识别 | 1. 实现DSR API调用<br>2. 处理结构化结果<br>3. 创建结构可视化 | 可准确识别文档结构 | 使用Cursor生成结构化处理代码 |

#### 第5-6周：文档解析与数据抽取

| 日期 | 工作内容 | 具体任务 | 完成标准 | AI辅助要点 |
|------|---------|---------|---------|------------|
| 07-01~07-02 | Word文档解析 | 1. 实现python-docx集成<br>2. 提取文本、表格、图片<br>3. 保留格式信息 | Word文档可完整解析 | 使用Cursor生成文档解析代码 |
| 07-03~07-05 | PDF文档解析 | 1. 实现MinerU集成<br>2. 提取文本、表格、图片<br>3. 处理布局信息 | PDF文档可完整解析 | 使用Cursor生成PDF处理代码 |
| 07-08~07-09 | OCR功能集成 | 1. 配置PaddleOCR<br>2. 实现图片文字识别<br>3. 处理识别结果 | 图片中文字可准确识别 | 使用Cursor生成OCR调用代码 |
| 07-10~07-12 | 关键信息抽取 | 1. 实现KIE API调用<br>2. 处理抽取结果<br>3. 创建数据校验规则 | 能准确抽取关键数据点 | 使用Cursor生成数据处理代码 |
| 07-15~07-16 | 表格数据处理 | 1. 实现表格识别与结构化<br>2. 处理表格数据<br>3. 创建表格可视化 | 表格数据可准确识别与处理 | 使用Cursor生成表格处理代码 |
| 07-17~07-19 | 阶段整合测试 | 1. 集成各解析功能<br>2. 测试不同文档类型<br>3. 优化识别准确率 | 各类文档处理功能正常，准确率满足要求 | 使用Cursor生成测试代码 |

#### 第二阶段检查项

- [ ] LLM API客户端可正常工作
- [ ] 提示工程系统已实现
- [ ] 文档结构识别功能正常
- [ ] Word文档解析功能完整
- [ ] PDF文档解析功能完整
- [ ] OCR功能可正常使用
- [ ] 关键信息抽取准确率满足要求
- [ ] 表格数据处理功能完整

### 第三阶段：交互界面与用户体验 (2024-07-22 至 2024-08-09)

#### 第7-8周：界面组件开发

| 日期 | 工作内容 | 具体任务 | 完成标准 | AI辅助要点 |
|------|---------|---------|---------|------------|
| 07-22~07-23 | 结构编辑器 | 1. 设计结构树编辑器<br>2. 实现拖拽功能<br>3. 添加增删改操作 | 结构编辑器可正常使用 | 使用Cursor生成前端组件 |
| 07-24~07-26 | 数据点编辑器 | 1. 设计数据编辑表单<br>2. 实现数据验证<br>3. 添加批量操作 | 数据点可方便编辑与验证 | 使用Cursor生成表单组件 |
| 07-29~07-30 | 表格编辑器 | 1. 设计表格编辑组件<br>2. 实现行列操作<br>3. 添加数据验证 | 表格可方便编辑与修改 | 使用Cursor生成表格组件 |
| 07-31~08-02 | 图片管理器 | 1. 设计图片管理组件<br>2. 实现图片关联功能<br>3. 添加预览功能 | 图片可上传、预览、关联 | 使用Cursor生成图片处理代码 |

#### 第9周：交互流程优化

| 日期 | 工作内容 | 具体任务 | 完成标准 | AI辅助要点 |
|------|---------|---------|---------|------------|
| 08-05~08-06 | 主界面交互 | 1. 实现主界面状态管理<br>2. 优化用户操作流程<br>3. 添加进度反馈 | 主界面交互流畅，操作直观 | 使用Cursor生成状态管理代码 |
| 08-07~08-09 | 阶段整合测试 | 1. 集成各UI组件<br>2. 测试完整交互流程<br>3. 优化用户体验 | 界面美观，交互流畅 | 使用Cursor生成UI测试代码 |

#### 第三阶段检查项

- [ ] 结构编辑器功能完整
- [ ] 数据点编辑器功能完整
- [ ] 表格编辑器功能完整
- [ ] 图片管理器功能完整
- [ ] 主界面状态管理正常
- [ ] 用户交互流程流畅
- [ ] 界面美观，操作直观

### 第四阶段：报告生成与导出 (2024-08-12 至 2024-08-23)

#### 第10-11周：报告生成功能

| 日期 | 工作内容 | 具体任务 | 完成标准 | AI辅助要点 |
|------|---------|---------|---------|------------|
| 08-12~08-13 | 报告数据组织 | 1. 设计报告数据结构<br>2. 实现数据验证<br>3. 创建完整度检查 | 报告数据组织合理，验证有效 | 使用Cursor生成数据验证代码 |
| 08-14~08-16 | Word模板填充 | 1. 实现模板占位符处理<br>2. 处理文本内容填充<br>3. 实现格式保持 | 内容可准确填充到模板中 | 使用Cursor生成模板处理代码 |
| 08-19~08-20 | 表格与图片插入 | 1. 实现表格生成与插入<br>2. 处理图片插入<br>3. 优化排版效果 | 表格和图片可正确插入 | 使用Cursor生成文档处理代码 |
| 08-21~08-23 | 报告质量检查 | 1. 实现拼写检查<br>2. 添加格式检查<br>3. 规范引用检查 | 报告质量检查有效 | 使用Cursor生成检查代码 |

#### 第四阶段检查项

- [ ] 报告数据组织合理
- [ ] 完整度检查功能有效
- [ ] Word模板填充准确
- [ ] 表格生成与插入正常
- [ ] 图片插入正常
- [ ] 排版效果良好
- [ ] 报告质量检查有效

### 第五阶段：测试、优化与部署 (2024-08-26 至 2024-09-13)

#### 第12周：测试与优化

| 日期 | 工作内容 | 具体任务 | 完成标准 | AI辅助要点 |
|------|---------|---------|---------|------------|
| 08-26~08-27 | 单元测试 | 1. 编写模型测试<br>2. 添加API测试<br>3. 实现工具函数测试 | 单元测试覆盖率≥80% | 使用Cursor生成测试代码 |
| 08-28~08-30 | 集成测试 | 1. 编写流程测试<br>2. 测试数据库交互<br>3. 测试异步任务 | 所有集成点测试通过 | 使用Cursor生成集成测试 |
| 09-02~09-03 | 性能优化 | 1. 分析性能瓶颈<br>2. 优化数据库查询<br>3. 优化前端加载 | 系统响应时间满足要求 | 使用Cursor生成性能分析 |
| 09-04~09-06 | 安全加固 | 1. 实现输入验证<br>2. 添加权限检查<br>3. 加密敏感数据 | 系统安全性满足要求 | 使用Cursor生成安全相关代码 |

#### 第13周：部署准备与文档

| 日期 | 工作内容 | 具体任务 | 完成标准 | AI辅助要点 |
|------|---------|---------|---------|------------|
| 09-09~09-10 | 部署脚本准备 | 1. 编写Docker Compose文件<br>2. 创建部署脚本<br>3. 准备环境配置 | 部署脚本可正常运行 | 使用Cursor生成部署脚本 |
| 09-11~09-12 | 用户文档编写 | 1. 编写用户手册<br>2. 创建操作指南<br>3. 编写API文档 | 文档完整，指南清晰 | 使用Cursor生成文档内容 |
| 09-13 | 系统部署 | 1. 配置生产环境<br>2. 部署系统<br>3. 验证系统功能 | 系统成功部署并可用 | 使用Cursor生成部署检查清单 |

#### 第五阶段检查项

- [ ] 单元测试覆盖率达标
- [ ] 集成测试全部通过
- [ ] 系统性能满足要求
- [ ] 安全措施有效
- [ ] 部署脚本可靠
- [ ] 用户文档完整
- [ ] 系统成功部署

## 三、Cursor AI辅助开发策略

为高效利用Cursor进行AI辅助开发，特制定以下策略：

### 代码生成策略

1. **模型类生成**
   - 提供完整的数据库表设计
   - 指定关系和约束
   - 要求生成SQLAlchemy模型类

2. **API视图生成**
   - 提供资源名称和操作
   - 说明权限要求
   - 要求生成Flask AppBuilder API视图

3. **前端组件生成**
   - 提供详细的组件功能描述
   - 说明交互要求
   - 要求生成HTML/CSS/JS代码

4. **测试代码生成**
   - 提供被测函数代码
   - 说明测试场景
   - 要求生成pytest测试用例

### 工作流优化

1. **每日工作计划**
   - 早晨：制定当日任务清单
   - 白天：核心开发工作
   - 晚上：代码审查和优化

2. **问题解决流程**
   - 遇到问题时，先明确定义问题
   - 使用Cursor寻找解决方案
   - 选择最佳方案并实施

3. **代码质量保障**
   - 使用Cursor生成代码后进行人工审查
   - 确保代码符合项目规范
   - 定期重构优化代码

## 四、每日工作模板

为保持开发节奏和进度跟踪，每日工作采用以下模板：

```
# 日期：YYYY-MM-DD
## 今日目标
- [ ] 目标1
- [ ] 目标2
- [ ] 目标3

## 完成情况
- [x] 任务1
- [ ] 任务2 (原因：...)
- [x] 任务3

## 问题与解决
1. 问题：...
   解决：...
2. 问题：...
   解决：...

## 明日计划
- [ ] 计划1
- [ ] 计划2

## 进度评估
- 当前进度：[正常/提前/延迟]
- 风险点：...
- 调整措施：...
```

## 五、沟通与反馈机制

作为单人开发团队，建立有效的自我反馈机制至关重要：

1. **每周回顾**
   - 每周五进行工作回顾
   - 总结一周进展
   - 调整下周计划

2. **关键节点评审**
   - 每个阶段结束进行自我评审
   - 检查所有检查项
   - 必要时寻求外部技术评审

3. **用户反馈收集**
   - 在可能的情况下，邀请潜在用户进行测试
   - 收集并分析反馈
   - 及时调整优先级和方案

## 六、风险管理

预先识别可能的风险并制定应对措施：

| 风险类型 | 具体风险 | 可能性 | 影响 | 应对措施 |
|---------|---------|-------|------|---------|
| 技术风险 | LLM API集成困难 | 中 | 高 | 提前进行POC测试，准备替代方案 |
| 技术风险 | PDF文档解析复杂 | 高 | 中 | 分阶段实现，降低首版要求 |
| 进度风险 | 个人精力不足 | 中 | 高 | 合理规划每日工作量，优先核心功能 |
| 质量风险 | AI生成代码质量不稳定 | 中 | 中 | 建立代码审查机制，设置质量标准 |
| 外部风险 | LLM服务不稳定 | 低 | 高 | 准备本地回退方案，实现请求缓存 |

## 七、资源与工具清单

### 开发环境

- 操作系统：Windows 10/11 或 macOS
- IDE：VSCode + Cursor插件
- 版本控制：Git + GitHub
- 本地开发服务器：Docker Desktop

### 开发工具

- 后端框架：Flask AppBuilder
- 前端工具：Bootstrap 4, jQuery
- 数据库工具：DBeaver/pgAdmin
- API测试：Postman/Insomnia
- 文档工具：Markdown编辑器

### 参考资源

- Flask AppBuilder文档：https://flask-appbuilder.readthedocs.io/
- Python-docx文档：https://python-docx.readthedocs.io/
- MinerU文档：https://github.com/opendatalab/MinerU
- PaddleOCR文档：https://github.com/PaddlePaddle/PaddleOCR

---

**注意事项：**
1. 本执行计划仅作为指导，可根据实际进展进行调整
2. 单人开发需特别注意工作量分配，避免过度疲劳
3. 充分利用AI工具提升效率，但保持对代码质量的控制
4. 遇到难题及时调整策略，不要长时间陷入单一问题 