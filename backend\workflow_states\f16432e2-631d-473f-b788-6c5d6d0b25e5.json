{"workflow_id": "f16432e2-631d-473f-b788-6c5d6d0b25e5", "current_stage": "data_extraction", "next_action": "human_interaction", "pending_interactions": ["confirm"], "human_interactions": [{"interaction_type": "confirm", "field_name": "template_confirmation", "original_value": {"type": "template_confirmation", "message": "我识别到您需要生成会议纪要。系统找到了以下匹配的模板：", "templates": [{"template_id": "2.key_definitions", "template_name": "会议纪要模板", "template_path": "C:\\scripts\\agent\\multi-agents\\backend\\templates\\wordtemplates\\4.template_数字科技中心会议纪要模板 copy.docx", "key_definitions_path": "C:\\scripts\\agent\\multi-agents\\backend\\templates\\keysdefinition\\2.key_definitions.md", "field_definitions": [{"field_name": "meeting_title", "description": "会议的名称，一次会议只有一个名称，20字内", "example": "5月月报项目进度评审会", "format": "普通文本", "must": true, "type": "string", "default": "\"<无会议名称>\"", "length": "", "source": ""}, {"field_name": "meeting_time", "description": "会议召开的时间，为北京时间", "example": "2025年6月26日（星期四）上午9:00", "format": "普通文本", "must": true, "type": "string", "default": "当前系统时间", "length": "", "source": ""}, {"field_name": "meeting_location", "description": "会议召开的地点，一般为某处会议室", "example": "广州地铁设计大厦0513会议室（白云区云城北二路129号）", "format": "普通文本", "must": true, "type": "string", "default": "\"<无会议地点>\"", "length": "", "source": ""}, {"field_name": "meeting_theme", "description": "议题，会上讨论了什么内容，一句或一段话，50字内", "example": "项目开展情况检查", "format": "普通文本", "must": true, "type": "string", "default": "\"<无会议议题>\"", "length": "50", "source": "上传内容"}, {"field_name": "meeting_host", "description": "会议主持人是谁？一般为人名", "example": "谢特赐", "format": "人名", "must": true, "type": "string", "default": "\"<无会议主持人>\"", "length": "12", "source": "上传内容"}, {"field_name": "meeting_recorder", "description": "会议记录人是谁？一般为人名", "example": "谢特赐", "format": "人名", "must": true, "type": "string", "default": "\"<无会议记录人>\"", "length": "12", "source": "上传内容"}, {"field_name": "symmary_receiver", "description": "谁应该接受这个会议纪要，如果是人名，可以有多个人，每个人之间用、隔开或者直接注明所有参会人员", "example": "所有参会人员", "format": "人名列表或组织代号", "must": true, "type": "string", "default": "\"<所有参会人员>\"", "length": "12", "source": "上传内容"}, {"field_name": "attend", "description": "参加会议的人员有哪些？和参会部分并列，放到表格中", "example": "桂林、李远峰、赵梓茗", "format": "行单元格", "must": true, "type": "array", "default": "\"\"", "length": "20", "source": "上传内容"}, {"field_name": "detail", "description": "解决这些问题的截止时间是什么时候？", "example": "数字科技中心", "format": "表格行", "must": true, "type": "array", "default": "会议召开时间的一个星期后", "length": "30", "source": "上传内容"}, {"field_name": "other", "description": "会议还有哪些其他安排，一段话描述，每个安排一句话，形成一段话，可以没有", "example": "", "format": "段落", "must": false, "type": "string", "default": "\"\"", "length": "100", "source": "上传内容"}], "placeholder_count": 10, "description": "动态加载的会议纪要模板"}], "default_selection": 0, "options": [{"value": "confirm", "label": "确认使用推荐模板"}, {"value": "select_other", "label": "选择其他模板"}, {"value": "cancel", "label": "取消生成"}]}, "user_input": null, "user_choice": null, "timestamp": "2025-07-25 16:59:21.990617", "is_completed": false}, {"interaction_type": "template_confirmation", "field_name": null, "original_value": null, "user_input": null, "user_choice": "confirm", "timestamp": "2025-07-25T16:59:26.038634", "is_completed": true}, {"interaction_type": "confirm", "field_name": "extraction_confirmation", "original_value": null, "user_input": null, "user_choice": null, "timestamp": "2025-07-25 16:59:33.943127", "is_completed": false}, {"interaction_type": "template_confirmation", "field_name": null, "original_value": null, "user_input": null, "user_choice": "confirm", "timestamp": "2025-07-25T16:59:41.041291", "is_completed": true}, {"interaction_type": "confirm", "field_name": "extraction_confirmation", "original_value": null, "user_input": null, "user_choice": null, "timestamp": "2025-07-25 16:59:49.955823", "is_completed": false}], "extraction_results": [{"field_name": "meeting_title", "field_value": "季度总结会议纪要", "confidence": 0.8, "source_location": null, "extraction_method": "llm_intelligent", "is_verified": false, "needs_human_input": false, "field_definition": {"field_name": "meeting_title", "description": "会议的名称，一次会议只有一个名称，20字内", "example": "5月月报项目进度评审会", "format": "普通文本", "must": true, "type": "string", "default": "\"<无会议名称>\"", "length": "", "source": ""}}, {"field_name": "meeting_time", "field_value": "2025年1月31日 下午3:00-5:00", "confidence": 0.9, "source_location": null, "extraction_method": "llm_intelligent", "is_verified": false, "needs_human_input": false, "field_definition": {"field_name": "meeting_time", "description": "会议召开的时间，为北京时间", "example": "2025年6月26日（星期四）上午9:00", "format": "普通文本", "must": true, "type": "string", "default": "当前系统时间", "length": "", "source": ""}}, {"field_name": "meeting_location", "field_value": "总部大会议室", "confidence": 0.8, "source_location": null, "extraction_method": "llm_intelligent", "is_verified": false, "needs_human_input": false, "field_definition": {"field_name": "meeting_location", "description": "会议召开的地点，一般为某处会议室", "example": "广州地铁设计大厦0513会议室（白云区云城北二路129号）", "format": "普通文本", "must": true, "type": "string", "default": "\"<无会议地点>\"", "length": "", "source": ""}}, {"field_name": "meeting_theme", "field_value": "第四季度业绩总结、年度目标完成情况评估、下一年度规划讨论", "confidence": 0.9, "source_location": null, "extraction_method": "llm_intelligent", "is_verified": false, "needs_human_input": false, "field_definition": {"field_name": "meeting_theme", "description": "议题，会上讨论了什么内容，一句或一段话，50字内", "example": "项目开展情况检查", "format": "普通文本", "must": true, "type": "string", "default": "\"<无会议议题>\"", "length": "50", "source": "上传内容"}}, {"field_name": "meeting_host", "field_value": "总经理", "confidence": 0.8, "source_location": null, "extraction_method": "llm_intelligent", "is_verified": false, "needs_human_input": false, "field_definition": {"field_name": "meeting_host", "description": "会议主持人是谁？一般为人名", "example": "谢特赐", "format": "人名", "must": true, "type": "string", "default": "\"<无会议主持人>\"", "length": "12", "source": "上传内容"}}, {"field_name": "meeting_recorder", "field_value": "秘书处", "confidence": 0.8, "source_location": null, "extraction_method": "llm_intelligent", "is_verified": false, "needs_human_input": false, "field_definition": {"field_name": "meeting_recorder", "description": "会议记录人是谁？一般为人名", "example": "谢特赐", "format": "人名", "must": true, "type": "string", "default": "\"<无会议记录人>\"", "length": "12", "source": "上传内容"}}, {"field_name": "symmary_receiver", "field_value": "各部门经理; 项目负责人; 财务总监; 人事总监", "confidence": 0.9, "source_location": null, "extraction_method": "llm_intelligent", "is_verified": false, "needs_human_input": false, "field_definition": {"field_name": "symmary_receiver", "description": "谁应该接受这个会议纪要，如果是人名，可以有多个人，每个人之间用、隔开或者直接注明所有参会人员", "example": "所有参会人员", "format": "人名列表或组织代号", "must": true, "type": "string", "default": "\"<所有参会人员>\"", "length": "12", "source": "上传内容"}}, {"field_name": "attend", "field_value": "各部门经理\n项目负责人\n财务总监\n人事总监", "confidence": 0.9, "source_location": null, "extraction_method": "llm_intelligent", "is_verified": false, "needs_human_input": false, "field_definition": {"field_name": "attend", "description": "参加会议的人员有哪些？和参会部分并列，放到表格中", "example": "桂林、李远峰、赵梓茗", "format": "行单元格", "must": true, "type": "array", "default": "\"\"", "length": "20", "source": "上传内容"}}, {"field_name": "detail", "field_value": "task: 财务总监准备详细财务报告; deadline: 下周一前\ntask: 人事总监制定新年度招聘计划; deadline: 未指定\ntask: 各部门经理提交下年度预算申请; deadline: 未指定\ntask: 项目负责人汇总技术创新成果; deadline: 未指定", "confidence": 0.9, "source_location": null, "extraction_method": "llm_intelligent", "is_verified": false, "needs_human_input": false, "field_definition": {"field_name": "detail", "description": "解决这些问题的截止时间是什么时候？", "example": "数字科技中心", "format": "表格行", "must": true, "type": "array", "default": "会议召开时间的一个星期后", "length": "30", "source": "上传内容"}}, {"field_name": "other", "field_value": "下次会议时间：2025年2月7日 下午3:00", "confidence": 0.7999999999999999, "source_location": null, "extraction_method": "llm_intelligent", "is_verified": false, "needs_human_input": false, "field_definition": {"field_name": "other", "description": "会议还有哪些其他安排，一段话描述，每个安排一句话，形成一段话，可以没有", "example": "", "format": "段落", "must": false, "type": "string", "default": "\"\"", "length": "100", "source": "上传内容"}}], "extracted_data": {"meeting_title": "季度总结会议纪要", "meeting_time": "2025年1月31日 下午3:00-5:00", "meeting_location": "总部大会议室", "meeting_theme": "第四季度业绩总结、年度目标完成情况评估、下一年度规划讨论", "meeting_host": "总经理", "meeting_recorder": "秘书处", "symmary_receiver": "各部门经理; 项目负责人; 财务总监; 人事总监", "attend": "各部门经理\n项目负责人\n财务总监\n人事总监", "detail": "task: 财务总监准备详细财务报告; deadline: 下周一前\ntask: 人事总监制定新年度招聘计划; deadline: 未指定\ntask: 各部门经理提交下年度预算申请; deadline: 未指定\ntask: 项目负责人汇总技术创新成果; deadline: 未指定", "other": "下次会议时间：2025年2月7日 下午3:00"}, "extraction_stats": {"total_fields": 10, "extracted_fields": 10, "success_rate": 1.0, "avg_confidence": 0.85}, "template_info": {"template_id": "2.key_definitions", "template_name": "会议纪要模板", "template_path": "C:\\scripts\\agent\\multi-agents\\backend\\templates\\wordtemplates\\4.template_数字科技中心会议纪要模板 copy.docx", "key_definitions_path": "C:\\scripts\\agent\\multi-agents\\backend\\templates\\keysdefinition\\2.key_definitions.md", "field_definitions": [{"field_name": "meeting_title", "description": "会议的名称，一次会议只有一个名称，20字内", "example": "5月月报项目进度评审会", "format": "普通文本", "must": true}, {"field_name": "meeting_time", "description": "会议召开的时间，为北京时间", "example": "2025年6月26日（星期四）上午9:00", "format": "普通文本", "must": true}, {"field_name": "meeting_location", "description": "会议召开的地点，一般为某处会议室", "example": "广州地铁设计大厦0513会议室（白云区云城北二路129号）", "format": "普通文本", "must": true}, {"field_name": "meeting_theme", "description": "议题，会上讨论了什么内容，一句或一段话，50字内", "example": "项目开展情况检查", "format": "普通文本", "must": true}, {"field_name": "meeting_host", "description": "会议主持人是谁？一般为人名", "example": "谢特赐", "format": "人名", "must": true}, {"field_name": "meeting_recorder", "description": "会议记录人是谁？一般为人名", "example": "谢特赐", "format": "人名", "must": true}, {"field_name": "symmary_receiver", "description": "谁应该接受这个会议纪要，如果是人名，可以有多个人，每个人之间用、隔开或者直接注明所有参会人员", "example": "所有参会人员", "format": "人名列表或组织代号", "must": true}, {"field_name": "attend", "description": "参加会议的人员有哪些？和参会部分并列，放到表格中", "example": "桂林、李远峰、赵梓茗", "format": "行单元格", "must": true}, {"field_name": "detail", "description": "解决这些问题的截止时间是什么时候？", "example": "数字科技中心", "format": "表格行", "must": true}, {"field_name": "other", "description": "会议还有哪些其他安排，一段话描述，每个安排一句话，形成一段话，可以没有", "example": "", "format": "段落", "must": false}], "placeholder_count": 10, "description": "动态加载的会议纪要模板"}, "errors": [], "warnings": [], "requires_user_action": true, "saved_at": "2025-07-25T16:59:49.955823"}