"""
报告生成API路由
"""

from typing import List
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from database import get_db
from models.report import Report
from models.template import Template
from models.data import DataSource
from schemas.report import (
    ReportCreate, ReportResponse, ReportListResponse,
    ReportGenerationRequest, ReportGenerationResponse
)
from schemas.common import MessageResponse, PaginatedResponse

router = APIRouter(prefix="/api/reports", tags=["报告生成"])


@router.get("/", response_model=PaginatedResponse[ReportListResponse])
async def list_reports(
    page: int = 1,
    size: int = 20,
    db: Session = Depends(get_db)
):
    """获取报告列表"""
    offset = (page - 1) * size
    
    reports = db.query(Report).filter(Report.is_active == True).offset(offset).limit(size).all()
    total = db.query(Report).filter(Report.is_active == True).count()
    
    items = [ReportListResponse.model_validate(report) for report in reports]
    pages = (total + size - 1) // size
    
    return PaginatedResponse(
        items=items,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/{report_id}", response_model=ReportResponse)
async def get_report(report_id: UUID, db: Session = Depends(get_db)):
    """获取报告详情"""
    report = db.query(Report).filter(
        Report.id == report_id,
        Report.is_active == True
    ).first()
    
    if not report:
        raise HTTPException(status_code=404, detail="报告不存在")
    
    return ReportResponse.model_validate(report)


@router.post("/create", response_model=ReportResponse)
async def create_report(
    report_data: ReportCreate,
    db: Session = Depends(get_db)
):
    """创建报告记录"""
    # 验证模板存在
    template = db.query(Template).filter(
        Template.id == report_data.template_id,
        Template.is_active == True
    ).first()
    
    if not template:
        raise HTTPException(status_code=404, detail="模板不存在")
    
    # 如果指定了数据源，验证数据源存在
    if report_data.data_source_id:
        data_source = db.query(DataSource).filter(
            DataSource.id == report_data.data_source_id,
            DataSource.is_active == True
        ).first()
        
        if not data_source:
            raise HTTPException(status_code=404, detail="数据源不存在")
    
    # 创建报告记录
    report = Report(
        name=report_data.name,
        template_id=report_data.template_id,
        data_source_id=report_data.data_source_id,
        status="draft",
        created_by="system"  # TODO: 从认证信息获取
    )
    
    db.add(report)
    db.commit()
    db.refresh(report)
    
    return ReportResponse.model_validate(report)


@router.post("/generate", response_model=ReportGenerationResponse)
async def generate_report(
    request: ReportGenerationRequest,
    db: Session = Depends(get_db)
):
    """生成报告"""
    # 验证模板存在
    template = db.query(Template).filter(
        Template.id == request.template_id,
        Template.is_active == True
    ).first()
    
    if not template:
        raise HTTPException(status_code=404, detail="模板不存在")
    
    # 如果指定了数据源，验证数据源存在
    data_source = None
    if request.data_source_id:
        data_source = db.query(DataSource).filter(
            DataSource.id == request.data_source_id,
            DataSource.is_active == True
        ).first()
        
        if not data_source:
            raise HTTPException(status_code=404, detail="数据源不存在")
    
    # TODO: 实现实际的报告生成逻辑
    import time
    start_time = time.time()
    
    # 创建报告记录
    report = Report(
        name=request.report_name,
        template_id=request.template_id,
        data_source_id=request.data_source_id,
        status="generating",
        created_by="system"  # TODO: 从认证信息获取
    )
    
    db.add(report)
    db.commit()
    db.refresh(report)
    
    # 模拟报告生成过程
    time.sleep(1)  # 模拟生成时间
    
    generation_time = int(time.time() - start_time)
    file_path = f"uploads/reports/{report.id}.docx"
    
    # 更新报告状态
    report.status = "completed"
    report.file_path = file_path
    report.generation_time = generation_time
    db.commit()
    
    return ReportGenerationResponse(
        report_id=report.id,
        status="completed",
        file_path=file_path,
        generation_time=generation_time,
        message="报告生成成功"
    )


@router.put("/{report_id}/status")
async def update_report_status(
    report_id: UUID,
    status: str,
    db: Session = Depends(get_db)
):
    """更新报告状态"""
    report = db.query(Report).filter(
        Report.id == report_id,
        Report.is_active == True
    ).first()
    
    if not report:
        raise HTTPException(status_code=404, detail="报告不存在")
    
    report.status = status
    db.commit()
    
    return MessageResponse(message="报告状态更新成功")


@router.delete("/{report_id}", response_model=MessageResponse)
async def delete_report(report_id: UUID, db: Session = Depends(get_db)):
    """删除报告(软删除)"""
    report = db.query(Report).filter(
        Report.id == report_id,
        Report.is_active == True
    ).first()
    
    if not report:
        raise HTTPException(status_code=404, detail="报告不存在")
    
    report.is_active = False
    db.commit()
    
    return MessageResponse(message="报告删除成功")


@router.post("/batch-delete", response_model=MessageResponse)
async def batch_delete_reports(
    report_ids: List[UUID],
    db: Session = Depends(get_db)
):
    """批量删除报告"""
    if not report_ids:
        raise HTTPException(status_code=400, detail="请提供要删除的报告ID列表")

    # 查找存在的报告
    reports = db.query(Report).filter(
        Report.id.in_(report_ids),
        Report.is_active == True
    ).all()

    if not reports:
        raise HTTPException(status_code=404, detail="未找到指定的报告")

    # 批量软删除
    deleted_count = 0
    for report in reports:
        report.is_active = False
        deleted_count += 1

    db.commit()

    return MessageResponse(message=f"成功删除 {deleted_count} 个报告")


@router.post("/export")
async def export_reports(
    report_ids: List[UUID] = None,
    export_format: str = "json",
    db: Session = Depends(get_db)
):
    """导出报告元数据"""
    from fastapi.responses import JSONResponse
    from datetime import datetime

    # 构建查询
    query = db.query(Report).filter(Report.is_active == True)

    if report_ids:
        query = query.filter(Report.id.in_(report_ids))

    reports = query.all()

    if not reports:
        raise HTTPException(status_code=404, detail="未找到要导出的报告")

    # 准备导出数据
    export_data = []
    for report in reports:
        report_data = {
            "id": str(report.id),
            "name": report.name,
            "description": report.description,
            "template_id": str(report.template_id) if report.template_id else None,
            "data_source_id": str(report.data_source_id) if report.data_source_id else None,
            "status": report.status,
            "file_path": report.file_path,
            "file_size": report.file_size,
            "generation_time": report.generation_time,
            "quality_score": report.quality_score,
            "created_at": report.created_at.isoformat() if report.created_at else None,
            "updated_at": report.updated_at.isoformat() if report.updated_at else None
        }
        export_data.append(report_data)

    if export_format.lower() == "json":
        return JSONResponse(
            content={
                "export_time": datetime.now().isoformat(),
                "total_count": len(export_data),
                "reports": export_data
            },
            headers={"Content-Disposition": "attachment; filename=reports_export.json"}
        )
    else:
        raise HTTPException(status_code=400, detail="不支持的导出格式，目前仅支持 json")


@router.get("/statistics/summary")
async def get_report_statistics(db: Session = Depends(get_db)):
    """获取报告统计信息"""
    from sqlalchemy import func

    # 基础统计
    total_reports = db.query(Report).filter(Report.is_active == True).count()
    completed_reports = db.query(Report).filter(
        Report.is_active == True,
        Report.status == "completed"
    ).count()
    failed_reports = db.query(Report).filter(
        Report.is_active == True,
        Report.status == "failed"
    ).count()

    # 按状态分组统计
    status_stats = db.query(
        Report.status,
        func.count(Report.id).label('count')
    ).filter(Report.is_active == True).group_by(Report.status).all()

    # 按模板分组统计
    template_stats = db.query(
        Template.name,
        func.count(Report.id).label('count')
    ).join(Report, Template.id == Report.template_id)\
     .filter(Report.is_active == True)\
     .group_by(Template.name).all()

    # 质量分数统计
    quality_stats = db.query(
        func.avg(Report.quality_score).label('avg_quality'),
        func.min(Report.quality_score).label('min_quality'),
        func.max(Report.quality_score).label('max_quality')
    ).filter(
        Report.is_active == True,
        Report.quality_score.isnot(None)
    ).first()

    return {
        "summary": {
            "total_reports": total_reports,
            "completed_reports": completed_reports,
            "failed_reports": failed_reports,
            "success_rate": completed_reports / total_reports if total_reports > 0 else 0
        },
        "status_distribution": [
            {"status": status, "count": count}
            for status, count in status_stats
        ],
        "template_usage": [
            {"template_name": name, "report_count": count}
            for name, count in template_stats
        ],
        "quality_metrics": {
            "average_quality": float(quality_stats.avg_quality) if quality_stats.avg_quality else 0,
            "min_quality": float(quality_stats.min_quality) if quality_stats.min_quality else 0,
            "max_quality": float(quality_stats.max_quality) if quality_stats.max_quality else 0
        } if quality_stats else {}
    }
