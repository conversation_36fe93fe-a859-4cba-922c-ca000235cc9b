"""
文件上传API
"""

import os
import uuid
from pathlib import Path
from typing import List

from fastapi import APIRouter, UploadFile, File, HTTPException, Depends
from fastapi.responses import JSONResponse

from services.document_service import document_service
from config import settings

router = APIRouter(prefix="/api/upload", tags=["文件上传"])

# 确保上传目录存在
UPLOAD_DIR = Path(settings.upload_dir)
UPLOAD_DIR.mkdir(exist_ok=True)

ALLOWED_EXTENSIONS = {'.docx', '.txt'}
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB


@router.post("/document")
async def upload_document(file: UploadFile = File(...)):
    """
    上传文档文件
    """
    # 检查文件类型
    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in ALLOWED_EXTENSIONS:
        raise HTTPException(
            status_code=400,
            detail=f"不支持的文件类型: {file_ext}。支持的类型: {', '.join(ALLOWED_EXTENSIONS)}"
        )
    
    # 检查文件大小
    content = await file.read()
    if len(content) > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=400,
            detail=f"文件过大。最大支持: {MAX_FILE_SIZE // (1024*1024)}MB"
        )
    
    # 生成唯一文件名
    file_id = str(uuid.uuid4())
    safe_filename = f"{file_id}_{file.filename}"
    file_path = UPLOAD_DIR / safe_filename
    
    # 保存文件
    with open(file_path, 'wb') as f:
        f.write(content)
    
    # 解析文档
    doc_result = document_service.parse_document(file_path)
    
    if not doc_result.success:
        # 删除失败的文件
        file_path.unlink(missing_ok=True)
        raise HTTPException(
            status_code=400,
            detail=f"文档解析失败: {doc_result.error}"
        )
    
    return {
        "file_id": file_id,
        "filename": file.filename,
        "file_path": str(file_path),
        "document_type": doc_result.document_type.value,
        "text_length": len(doc_result.raw_text),
        "paragraph_count": len(doc_result.paragraphs),
        "table_count": len(doc_result.tables),
        "metadata": doc_result.metadata,
        "preview": doc_result.raw_text[:200] + "..." if len(doc_result.raw_text) > 200 else doc_result.raw_text
    }


@router.get("/document/{file_id}")
async def get_document_content(file_id: str):
    """
    获取已上传文档的完整内容
    """
    # 查找文件
    matching_files = list(UPLOAD_DIR.glob(f"{file_id}_*"))
    
    if not matching_files:
        raise HTTPException(status_code=404, detail="文件不存在")
    
    file_path = matching_files[0]
    
    # 重新解析文档
    doc_result = document_service.parse_document(file_path)
    
    if not doc_result.success:
        raise HTTPException(
            status_code=500,
            detail=f"文档解析失败: {doc_result.error}"
        )
    
    return {
        "file_id": file_id,
        "filename": file_path.name,
        "document_type": doc_result.document_type.value,
        "raw_text": doc_result.raw_text,
        "paragraphs": doc_result.paragraphs,
        "tables": doc_result.tables,
        "metadata": doc_result.metadata
    }


@router.delete("/document/{file_id}")
async def delete_document(file_id: str):
    """
    删除已上传的文档
    """
    # 查找文件
    matching_files = list(UPLOAD_DIR.glob(f"{file_id}_*"))
    
    if not matching_files:
        raise HTTPException(status_code=404, detail="文件不存在")
    
    # 删除文件
    for file_path in matching_files:
        file_path.unlink()
    
    return {"message": "文件删除成功", "file_id": file_id}


@router.get("/list")
async def list_uploaded_documents():
    """
    列出所有已上传的文档
    """
    documents = []
    
    for file_path in UPLOAD_DIR.iterdir():
        if file_path.is_file():
            # 解析文件名获取file_id
            filename = file_path.name
            if '_' in filename:
                file_id = filename.split('_')[0]
                original_name = '_'.join(filename.split('_')[1:])
                
                # 获取文件信息
                stat = file_path.stat()
                
                documents.append({
                    "file_id": file_id,
                    "filename": original_name,
                    "file_size": stat.st_size,
                    "upload_time": stat.st_ctime,
                    "file_type": file_path.suffix.lower()
                })
    
    return {"documents": documents, "total": len(documents)}
