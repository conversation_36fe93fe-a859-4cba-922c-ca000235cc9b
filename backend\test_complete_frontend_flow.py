#!/usr/bin/env python3
"""
测试完整的前端流程
"""

import requests
import json
import time

def test_complete_frontend_flow():
    """测试完整的前端流程"""
    
    print("🧪 测试完整的前端流程")
    
    try:
        # 第1步: 启动新工作流
        print(f"\n📋 第1步: 启动新工作流...")
        
        start_response = requests.post(
            'http://localhost:8000/api/workflow/langgraph/start',
            json={
                'user_input': '请帮我生成会议纪要',
                'files': []
            }
        )
        
        if start_response.status_code == 200:
            result = start_response.json()
            workflow_id = result.get('workflow_id')
            
            print(f"✅ 工作流启动成功: {workflow_id}")
            print(f"   状态: {result.get('status')}")
            print(f"   需要用户操作: {result.get('data', {}).get('requires_user_action')}")
            
            # 第2步: 等待并确认模板
            print(f"\n📋 第2步: 确认模板...")
            time.sleep(2)
            
            confirm_response = requests.post(
                f'http://localhost:8000/api/workflow/langgraph/resume/{workflow_id}',
                json={'action': 'confirm'}
            )
            
            if confirm_response.status_code == 200:
                confirm_result = confirm_response.json()
                print(f"✅ 模板确认成功: {confirm_result.get('status')}")
                
                # 第3步: 等待数据提取完成
                print(f"\n📋 第3步: 等待数据提取完成...")
                
                for i in range(10):  # 最多等待30秒
                    time.sleep(3)
                    print(f"  检查状态 ({i+1}/10)...")
                    
                    status_response = requests.get(f'http://localhost:8000/api/workflow/langgraph/status/{workflow_id}')
                    
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        
                        print(f"    当前阶段: {status_data.get('current_stage')}")
                        print(f"    待处理交互: {status_data.get('pending_interactions', [])}")
                        print(f"    需要用户操作: {status_data.get('requires_user_action')}")
                        
                        # 检查是否到达数据确认阶段
                        if (status_data.get('current_stage') == 'data_extraction' and 
                            'supplement' in status_data.get('pending_interactions', []) and
                            status_data.get('requires_user_action')):
                            
                            print(f"\n✅ 到达数据确认阶段！")
                            print(f"   提取结果数量: {len(status_data.get('extraction_results', []))}")
                            
                            # 显示提取结果预览
                            extraction_results = status_data.get('extraction_results', [])
                            if extraction_results:
                                print(f"\n📊 提取结果预览 (前3个):")
                                for j, result in enumerate(extraction_results[:3]):
                                    field_name = result.get('field_name', 'unknown')
                                    field_value = result.get('field_value', 'N/A')
                                    confidence = result.get('confidence', 0)
                                    print(f"      {j+1}. {field_name}: {field_value} (置信度: {confidence:.2f})")
                            
                            print(f"\n🎯 此时前端应该显示数据确认界面！")
                            print(f"   - 工作流ID: {workflow_id}")
                            print(f"   - 交互类型: data_confirmation")
                            print(f"   - 提取结果: {len(extraction_results)} 个字段")
                            
                            # 第4步: 模拟用户确认数据
                            print(f"\n📋 第4步: 模拟用户确认数据...")
                            time.sleep(2)
                            
                            data_confirm_response = requests.post(
                                f'http://localhost:8000/api/workflow/langgraph/resume/{workflow_id}',
                                json={'action': 'confirm'}
                            )
                            
                            if data_confirm_response.status_code == 200:
                                data_confirm_result = data_confirm_response.json()
                                print(f"✅ 数据确认成功: {data_confirm_result.get('status')}")
                                
                                # 第5步: 等待报告生成完成
                                print(f"\n📋 第5步: 等待报告生成完成...")
                                time.sleep(5)
                                
                                final_status_response = requests.get(f'http://localhost:8000/api/workflow/langgraph/status/{workflow_id}')
                                
                                if final_status_response.status_code == 200:
                                    final_status = final_status_response.json()
                                    print(f"✅ 最终状态检查成功")
                                    print(f"   当前阶段: {final_status.get('current_stage')}")
                                    print(f"   需要用户操作: {final_status.get('requires_user_action')}")
                                    
                                    if not final_status.get('requires_user_action'):
                                        print(f"\n🎉 完整流程测试成功！")
                                        print(f"   工作流ID: {workflow_id}")
                                        print(f"   前端应该显示报告生成完成")
                                    else:
                                        print(f"\n⚠️ 工作流仍需要用户操作")
                                else:
                                    print(f"❌ 最终状态检查失败")
                            else:
                                print(f"❌ 数据确认失败: {data_confirm_response.text}")
                            
                            return workflow_id
                        
                        # 如果工作流已完成
                        elif not status_data.get('requires_user_action'):
                            print(f"\n✅ 工作流已完成")
                            return workflow_id
                    else:
                        print(f"    ❌ 状态检查失败: {status_response.status_code}")
                
                print(f"\n⚠️ 等待超时，数据提取可能需要更长时间")
                return workflow_id
            else:
                print(f"❌ 模板确认失败: {confirm_response.text}")
        else:
            print(f"❌ 工作流启动失败: {start_response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    return None

if __name__ == "__main__":
    workflow_id = test_complete_frontend_flow()
    
    if workflow_id:
        print(f"\n📝 测试总结:")
        print(f"   工作流ID: {workflow_id}")
        print(f"   前端URL: http://localhost:5173")
        print(f"   状态API: http://localhost:8000/api/workflow/langgraph/status/{workflow_id}")
        print(f"\n💡 现在可以在前端界面中观察数据确认界面是否正确显示")
