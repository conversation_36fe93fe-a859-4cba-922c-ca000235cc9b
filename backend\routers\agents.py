"""
智能体交互API路由
提供智能体会话管理、工作流执行、人机交互等接口
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Dict, Any, List, Optional
from uuid import UUID
from datetime import datetime
import logging

from database import get_db
from models.agent import AgentSession, ConversationHistory, ToolCall
from schemas.agent import (
    AgentSessionCreate, AgentSessionResponse, 
    ConversationCreate, ConversationResponse,
    WorkflowExecuteRequest, WorkflowExecuteResponse,
    HumanInteractionRequest, HumanInteractionResponse
)
from agents.report_generation_graph import ReportGenerationGraph
from agents.langgraph_state import UserInput

router = APIRouter(prefix="/agents", tags=["智能体"])
logger = logging.getLogger(__name__)

# 全局工作流实例
report_workflow = ReportGenerationGraph()


@router.post("/sessions", response_model=AgentSessionResponse)
async def create_session(
    session_data: AgentSessionCreate,
    db: Session = Depends(get_db)
):
    """创建新的智能体会话"""
    try:
        session = AgentSession(
            user_id=session_data.user_id,
            session_name=session_data.session_name,
            session_type=session_data.session_type,
            status="active",
            context_data=session_data.context_data or {}
        )
        
        db.add(session)
        db.commit()
        db.refresh(session)
        
        logger.info(f"创建智能体会话: {session.id}")
        
        return AgentSessionResponse(
            id=session.id,
            session_name=session.session_name,
            session_type=session.session_type,
            status=session.status,
            current_agent=session.current_agent,
            current_stage=session.current_stage,
            context_data=session.context_data,
            created_at=session.created_at
        )
        
    except Exception as e:
        logger.error(f"创建会话失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建会话失败: {str(e)}")


@router.get("/sessions/{session_id}", response_model=AgentSessionResponse)
async def get_session(
    session_id: UUID,
    db: Session = Depends(get_db)
):
    """获取智能体会话信息"""
    session = db.query(AgentSession).filter(AgentSession.id == session_id).first()
    
    if not session:
        raise HTTPException(status_code=404, detail="会话未找到")
    
    return AgentSessionResponse(
        id=session.id,
        session_name=session.session_name,
        session_type=session.session_type,
        status=session.status,
        current_agent=session.current_agent,
        current_stage=session.current_stage,
        context_data=session.context_data,
        created_at=session.created_at,
        updated_at=session.updated_at,
        completed_at=session.completed_at
    )


@router.get("/sessions/{session_id}/conversations", response_model=List[ConversationResponse])
async def get_session_conversations(
    session_id: UUID,
    limit: int = 50,
    db: Session = Depends(get_db)
):
    """获取会话的对话历史"""
    conversations = db.query(ConversationHistory).filter(
        ConversationHistory.session_id == session_id
    ).order_by(ConversationHistory.created_at.desc()).limit(limit).all()
    
    return [
        ConversationResponse(
            id=conv.id,
            session_id=conv.session_id,
            message_type=conv.message_type,
            sender=conv.sender,
            content=conv.content,
            message_metadata=conv.message_metadata,
            tool_calls=conv.tool_calls,
            attachments=conv.attachments,
            created_at=conv.created_at
        )
        for conv in conversations
    ]


@router.post("/sessions/{session_id}/conversations", response_model=ConversationResponse)
async def add_conversation(
    session_id: UUID,
    conversation_data: ConversationCreate,
    db: Session = Depends(get_db)
):
    """添加对话记录"""
    try:
        # 验证会话存在
        session = db.query(AgentSession).filter(AgentSession.id == session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="会话未找到")
        
        conversation = ConversationHistory(
            session_id=session_id,
            message_type=conversation_data.message_type,
            sender=conversation_data.sender,
            content=conversation_data.content,
            message_metadata=conversation_data.message_metadata,
            tool_calls=conversation_data.tool_calls,
            attachments=conversation_data.attachments
        )
        
        db.add(conversation)
        db.commit()
        db.refresh(conversation)
        
        return ConversationResponse(
            id=conversation.id,
            session_id=conversation.session_id,
            message_type=conversation.message_type,
            sender=conversation.sender,
            content=conversation.content,
            message_metadata=conversation.message_metadata,
            tool_calls=conversation.tool_calls,
            attachments=conversation.attachments,
            created_at=conversation.created_at
        )
        
    except Exception as e:
        logger.error(f"添加对话记录失败: {e}")
        raise HTTPException(status_code=500, detail=f"添加对话记录失败: {str(e)}")


@router.post("/workflows/execute", response_model=WorkflowExecuteResponse)
async def execute_workflow(
    request: WorkflowExecuteRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """执行智能体工作流"""
    try:
        # 创建用户输入对象
        user_input = UserInput(
            user_request=request.user_request,
            document_paths=request.document_paths,
            template_id=request.template_id,
            additional_context=request.additional_context or {}
        )
        
        # 异步执行工作流
        if request.async_execution:
            background_tasks.add_task(
                _execute_workflow_background,
                user_input,
                request.session_id,
                db
            )
            
            return WorkflowExecuteResponse(
                workflow_id="pending",
                status="started",
                message="工作流已开始异步执行"
            )
        else:
            # 同步执行工作流
            result = await report_workflow.run_workflow(user_input)
            
            # 如果提供了session_id，更新会话状态
            if request.session_id:
                await _update_session_with_result(request.session_id, result, db)
            
            return WorkflowExecuteResponse(
                workflow_id=result["workflow_id"],
                status=result["status"],
                final_state=result.get("final_state"),
                errors=result.get("errors", []),
                report_generation=result.get("report_generation"),
                message="工作流执行完成"
            )
            
    except Exception as e:
        logger.error(f"工作流执行失败: {e}")
        raise HTTPException(status_code=500, detail=f"工作流执行失败: {str(e)}")


@router.post("/workflows/{workflow_id}/resume", response_model=WorkflowExecuteResponse)
async def resume_workflow(
    workflow_id: str,
    user_response: HumanInteractionRequest,
    db: Session = Depends(get_db)
):
    """恢复暂停的工作流（处理人工交互）"""
    try:
        # 构建用户响应数据
        response_data = {
            "action": user_response.action,
            "field_updates": user_response.field_updates or {},
            "selected_option": user_response.selected_option,
            "additional_input": user_response.additional_input or {}
        }
        
        # 恢复工作流执行
        result = await report_workflow.resume_workflow(workflow_id, response_data)
        
        return WorkflowExecuteResponse(
            workflow_id=workflow_id,
            status=result["status"],
            final_state=result.get("final_state"),
            errors=result.get("errors", []),
            message="工作流恢复执行完成"
        )
        
    except Exception as e:
        logger.error(f"工作流恢复失败: {e}")
        raise HTTPException(status_code=500, detail=f"工作流恢复失败: {str(e)}")


@router.get("/workflows/{workflow_id}/status")
async def get_workflow_status(workflow_id: str):
    """获取工作流状态"""
    try:
        # 从工作流管理器获取状态
        status_info = await report_workflow.get_workflow_status(workflow_id)

        if not status_info:
            raise HTTPException(status_code=404, detail="工作流未找到")

        return {
            "workflow_id": workflow_id,
            "status": status_info.get("status", "unknown"),
            "current_stage": status_info.get("current_stage", "unknown"),
            "progress": status_info.get("progress", 0.0),
            "message": status_info.get("message", ""),
            "start_time": status_info.get("start_time"),
            "last_update": status_info.get("last_update"),
            "stage_details": status_info.get("stage_details", {}),
            "errors": status_info.get("errors", [])
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取工作流状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取工作流状态失败: {str(e)}")


@router.get("/workflows")
async def list_workflows(
    status: Optional[str] = None,
    limit: int = 50,
    offset: int = 0
):
    """获取工作流列表"""
    try:
        workflows = await report_workflow.list_workflows(
            status_filter=status,
            limit=limit,
            offset=offset
        )

        return {
            "workflows": workflows,
            "total": len(workflows),
            "limit": limit,
            "offset": offset
        }

    except Exception as e:
        logger.error(f"获取工作流列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取工作流列表失败: {str(e)}")


@router.delete("/workflows/{workflow_id}")
async def cancel_workflow(workflow_id: str):
    """取消工作流执行"""
    try:
        result = await report_workflow.cancel_workflow(workflow_id)

        if not result:
            raise HTTPException(status_code=404, detail="工作流未找到")

        return {
            "workflow_id": workflow_id,
            "status": "cancelled",
            "message": "工作流已取消"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消工作流失败: {e}")
        raise HTTPException(status_code=500, detail=f"取消工作流失败: {str(e)}")


@router.post("/sessions/{session_id}/interactions", response_model=HumanInteractionResponse)
async def handle_human_interaction(
    session_id: UUID,
    interaction_request: HumanInteractionRequest,
    db: Session = Depends(get_db)
):
    """处理人机交互请求"""
    try:
        # 验证会话存在
        session = db.query(AgentSession).filter(AgentSession.id == session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="会话未找到")
        
        # 记录交互历史
        conversation = ConversationHistory(
            session_id=session_id,
            message_type="human_interaction",
            sender="user",
            content=f"用户交互: {interaction_request.action}",
            message_metadata={
                "action": interaction_request.action,
                "field_updates": interaction_request.field_updates,
                "selected_option": interaction_request.selected_option
            }
        )
        
        db.add(conversation)
        db.commit()
        
        return HumanInteractionResponse(
            interaction_id=str(conversation.id),
            status="processed",
            message="人机交互处理完成",
            next_action="continue_workflow"
        )
        
    except Exception as e:
        logger.error(f"人机交互处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"人机交互处理失败: {str(e)}")


@router.delete("/sessions/{session_id}")
async def delete_session(
    session_id: UUID,
    db: Session = Depends(get_db)
):
    """删除智能体会话"""
    session = db.query(AgentSession).filter(AgentSession.id == session_id).first()
    
    if not session:
        raise HTTPException(status_code=404, detail="会话未找到")
    
    try:
        db.delete(session)
        db.commit()
        
        logger.info(f"删除智能体会话: {session_id}")
        
        return {"message": "会话删除成功"}

    except Exception as e:
        logger.error(f"删除会话失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除会话失败: {str(e)}")


@router.post("/tools/{tool_name}/execute")
async def execute_tool_directly(
    tool_name: str,
    tool_input: Dict[str, Any],
    db: Session = Depends(get_db)
):
    """直接执行指定工具"""
    try:
        # 导入工具类
        from agents.tools.template_identification import TemplateIdentificationTool
        from agents.tools.template_parsing import TemplateParsingTool
        from agents.tools.data_extraction import DataExtractionTool
        from agents.tools.report_generation import ReportGenerationTool

        # 工具映射
        tool_mapping = {
            "template_identification": TemplateIdentificationTool,
            "template_parsing": TemplateParsingTool,
            "data_extraction": DataExtractionTool,
            "report_generation": ReportGenerationTool
        }

        if tool_name not in tool_mapping:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的工具: {tool_name}。支持的工具: {', '.join(tool_mapping.keys())}"
            )

        # 实例化并执行工具
        tool_class = tool_mapping[tool_name]
        tool_instance = tool_class()

        result = await tool_instance.execute(tool_input)

        return {
            "tool_name": tool_name,
            "execution_result": result,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"工具执行失败: {e}")
        raise HTTPException(status_code=500, detail=f"工具执行失败: {str(e)}")


# 辅助函数
async def _execute_workflow_background(user_input: UserInput, session_id: Optional[UUID], db: Session):
    """后台执行工作流"""
    try:
        result = await report_workflow.run_workflow(user_input)
        
        if session_id:
            await _update_session_with_result(session_id, result, db)
            
        logger.info(f"后台工作流执行完成: {result['workflow_id']}")
        
    except Exception as e:
        logger.error(f"后台工作流执行失败: {e}")


async def _update_session_with_result(session_id: UUID, result: Dict[str, Any], db: Session):
    """使用工作流结果更新会话"""
    try:
        session = db.query(AgentSession).filter(AgentSession.id == session_id).first()
        if session:
            session.status = result["status"]
            session.context_data = result.get("final_state", {})
            
            if result["status"] == "completed":
                from datetime import datetime
                session.completed_at = datetime.now()
            
            db.commit()
            
    except Exception as e:
        logger.error(f"更新会话状态失败: {e}")
