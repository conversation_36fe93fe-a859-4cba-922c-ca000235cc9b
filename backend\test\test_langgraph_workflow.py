"""
测试LangGraph工作流接口
测试 /api/workflow/langgraph/start 接口的各种场景
"""

import pytest
import asyncio
import requests
import json
import io
from pathlib import Path
from typing import Dict, Any

# 测试配置
BASE_URL = "http://localhost:8000"
WORKFLOW_ENDPOINT = f"{BASE_URL}/api/workflow/langgraph/start"


class TestLangGraphWorkflow:
    """LangGraph工作流接口测试类"""

    def test_health_check(self):
        """测试服务健康状态"""
        try:
            response = requests.get(f"{BASE_URL}/health", timeout=5)
            print(f"健康检查响应: {response.status_code}")
            assert response.status_code == 200
        except requests.exceptions.RequestException as e:
            pytest.fail(f"服务连接失败: {e}")

    def test_simple_text_input(self):
        """测试纯文本输入（无文件）"""
        print("\n=== 测试纯文本输入 ===")
        
        # 准备请求数据
        data = {
            'text_input': '你好，我想生成一个会议纪要'
        }
        
        try:
            response = requests.post(WORKFLOW_ENDPOINT, data=data, timeout=30)
            print(f"响应状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            if response.status_code != 200:
                print(f"错误响应内容: {response.text}")
                
            assert response.status_code == 200, f"期望状态码200，实际{response.status_code}"
            
            # 解析响应
            result = response.json()
            print(f"响应结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            # 验证响应结构
            assert "success" in result
            assert "workflow_id" in result
            assert "message" in result
            
            print("✅ 纯文本输入测试通过")
            
        except requests.exceptions.RequestException as e:
            pytest.fail(f"请求失败: {e}")
        except json.JSONDecodeError as e:
            pytest.fail(f"响应JSON解析失败: {e}")

    def test_conversation_intent(self):
        """测试对话意图识别"""
        print("\n=== 测试对话意图识别 ===")
        
        test_cases = [
            "你好，请问你能做什么？",
            "我想了解一下这个系统的功能",
            "帮我介绍一下报告生成的流程"
        ]
        
        for text in test_cases:
            print(f"\n测试文本: {text}")
            
            data = {'text_input': text}
            
            try:
                response = requests.post(WORKFLOW_ENDPOINT, data=data, timeout=30)
                print(f"响应状态码: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"响应消息: {result.get('message', 'N/A')}")
                    print(f"工作流状态: {result.get('status', 'N/A')}")
                else:
                    print(f"错误响应: {response.text}")
                    
            except Exception as e:
                print(f"请求异常: {e}")

    def test_report_generation_intent(self):
        """测试报告生成意图识别"""
        print("\n=== 测试报告生成意图识别 ===")
        
        test_cases = [
            "我需要生成一个会议纪要",
            "帮我制作项目报告",
            "请根据文档生成总结报告"
        ]
        
        for text in test_cases:
            print(f"\n测试文本: {text}")
            
            data = {'text_input': text}
            
            try:
                response = requests.post(WORKFLOW_ENDPOINT, data=data, timeout=30)
                print(f"响应状态码: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"响应消息: {result.get('message', 'N/A')}")
                    print(f"工作流状态: {result.get('status', 'N/A')}")
                    
                    # 检查是否需要用户进一步操作
                    requires_action = result.get('data', {}).get('requires_user_action', False)
                    print(f"需要用户操作: {requires_action}")
                else:
                    print(f"错误响应: {response.text}")
                    
            except Exception as e:
                print(f"请求异常: {e}")

    def test_with_text_file(self):
        """测试带文本文件的请求"""
        print("\n=== 测试文本文件上传 ===")
        
        # 创建测试文本文件
        test_content = """
        会议纪要测试内容
        
        会议时间：2024年7月25日 14:00-16:00
        会议地点：会议室A
        主持人：张经理
        参会人员：李工程师、王设计师、赵分析师
        
        会议议题：
        1. 项目进展汇报
        2. 技术方案讨论
        3. 下阶段工作安排
        
        会议内容：
        1. 项目当前完成度为75%，基本按计划推进
        2. 技术方案经过讨论，决定采用方案B
        3. 下周开始进入测试阶段
        
        后续安排：
        1. 李工程师负责技术实现
        2. 王设计师完善界面设计
        3. 赵分析师准备测试用例
        """
        
        # 准备文件数据
        files = {
            'files': ('test_meeting.txt', io.StringIO(test_content), 'text/plain')
        }
        
        data = {
            'text_input': '请根据上传的文档生成会议纪要'
        }
        
        try:
            response = requests.post(WORKFLOW_ENDPOINT, data=data, files=files, timeout=60)
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"响应消息: {result.get('message', 'N/A')}")
                print(f"工作流状态: {result.get('status', 'N/A')}")
                
                # 检查是否有报告生成结果
                report_data = result.get('data', {}).get('report_generation')
                if report_data:
                    print(f"报告生成状态: {report_data}")
                    
            else:
                print(f"错误响应: {response.text}")
                
        except Exception as e:
            print(f"请求异常: {e}")

    def test_error_handling(self):
        """测试错误处理"""
        print("\n=== 测试错误处理 ===")
        
        # 测试空输入
        print("\n测试空输入:")
        try:
            response = requests.post(WORKFLOW_ENDPOINT, data={}, timeout=10)
            print(f"空输入响应状态码: {response.status_code}")
            print(f"空输入响应内容: {response.text}")
        except Exception as e:
            print(f"空输入请求异常: {e}")
        
        # 测试无效文件
        print("\n测试无效文件:")
        try:
            files = {
                'files': ('invalid.xyz', io.StringIO('invalid content'), 'application/octet-stream')
            }
            data = {'text_input': '测试无效文件'}
            
            response = requests.post(WORKFLOW_ENDPOINT, data=data, files=files, timeout=10)
            print(f"无效文件响应状态码: {response.status_code}")
            print(f"无效文件响应内容: {response.text}")
        except Exception as e:
            print(f"无效文件请求异常: {e}")

    def test_api_documentation(self):
        """测试API文档可访问性"""
        print("\n=== 测试API文档 ===")
        
        try:
            # 测试OpenAPI文档
            response = requests.get(f"{BASE_URL}/docs", timeout=5)
            print(f"API文档响应状态码: {response.status_code}")
            
            # 测试OpenAPI JSON
            response = requests.get(f"{BASE_URL}/openapi.json", timeout=5)
            print(f"OpenAPI JSON响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                openapi_spec = response.json()
                # 检查是否包含我们的接口
                paths = openapi_spec.get('paths', {})
                workflow_path = '/api/workflow/langgraph/start'
                
                if workflow_path in paths:
                    print(f"✅ 接口 {workflow_path} 在API文档中找到")
                    endpoint_info = paths[workflow_path]
                    print(f"支持的方法: {list(endpoint_info.keys())}")
                else:
                    print(f"❌ 接口 {workflow_path} 在API文档中未找到")
                    print(f"可用接口: {list(paths.keys())}")
                    
        except Exception as e:
            print(f"API文档测试异常: {e}")


def initialize_database():
    """初始化数据库表"""
    print("🔧 初始化数据库表...")
    try:
        from database import create_tables
        create_tables()
        print("✅ 数据库表创建成功")
        return True
    except Exception as e:
        print(f"❌ 数据库表创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_template_confirmation_workflow():
    """测试模板确认工作流"""
    print("\n=== 测试模板确认工作流 ===")

    # 第一步：启动工作流
    print("1. 启动工作流...")
    data = {'text_input': '我需要生成一个会议纪要'}

    try:
        response = requests.post(WORKFLOW_ENDPOINT, data=data, timeout=30)
        print(f"启动响应状态码: {response.status_code}")

        if response.status_code != 200:
            print(f"启动失败: {response.text}")
            return

        result = response.json()
        workflow_id = result.get('workflow_id')
        print(f"工作流ID: {workflow_id}")

        # 检查是否需要用户确认
        requires_action = result.get('data', {}).get('requires_user_action', False)
        print(f"需要用户操作: {requires_action}")

        if requires_action:
            print("2. 模拟用户确认推荐模板...")

            # 第二步：确认模板
            resume_data = {
                "action": "confirm"
            }

            resume_response = requests.post(
                f"{BASE_URL}/api/workflow/langgraph/resume/{workflow_id}",
                json=resume_data,
                timeout=30
            )

            print(f"恢复响应状态码: {resume_response.status_code}")

            if resume_response.status_code == 200:
                resume_result = resume_response.json()
                print(f"恢复结果: {json.dumps(resume_result, indent=2, ensure_ascii=False)}")
                print("✅ 模板确认工作流测试成功")
            else:
                print(f"恢复失败: {resume_response.text}")
        else:
            print("⚠️ 工作流没有要求用户确认")

    except Exception as e:
        print(f"测试异常: {e}")


def run_manual_test():
    """手动运行测试（不依赖pytest）"""
    print("🚀 开始测试LangGraph工作流接口")
    print("=" * 50)

    # 首先初始化数据库
    if not initialize_database():
        print("❌ 数据库初始化失败，无法继续测试")
        return

    test_instance = TestLangGraphWorkflow()

    # 运行各项测试
    try:
        test_instance.test_health_check()
        test_instance.test_api_documentation()
        test_instance.test_simple_text_input()
        test_instance.test_conversation_intent()
        test_instance.test_report_generation_intent()
        test_instance.test_with_text_file()
        test_instance.test_error_handling()

        # 新增：测试模板确认工作流
        test_template_confirmation_workflow()

        print("\n" + "=" * 50)
        print("✅ 所有测试完成")

    except Exception as e:
        print(f"\n❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 直接运行测试
    run_manual_test()
