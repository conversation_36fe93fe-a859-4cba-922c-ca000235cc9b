/**
 * 左侧导航栏组件
 */

import React from 'react';
import { Layout, Menu, Avatar, Button, Typography, Tooltip } from 'antd';
import {
  UserOutlined,
  LogoutOutlined,
  HomeOutlined,
  FileTextOutlined,
  DatabaseOutlined,
  FileSearchOutlined,
  SettingOutlined,
  TeamOutlined,
  SkinOutlined,
  RobotOutlined
} from '@ant-design/icons';

const { Sider } = Layout;
const { Text } = Typography;

interface NavigationSidebarProps {
  currentTab: string;
  onTabChange: (tab: string) => void;
  currentUser: {
    username: string;
    avatar?: string | null;
  };
  onLogout: () => void;
}

const NavigationSidebar: React.FC<NavigationSidebarProps> = ({
  currentTab,
  onTabChange,
  currentUser,
  onLogout
}) => {
  const menuItems = [
    {
      key: 'main',
      icon: <HomeOutlined />,
      label: '主页',
    },
    {
      key: 'templates',
      icon: <SkinOutlined />,
      label: '模板管理',
    },
    {
      key: 'data',
      icon: <DatabaseOutlined />,
      label: '数据管理',
    },
    {
      key: 'reports',
      icon: <FileTextOutlined />,
      label: '报告管理',
    },
    {
      key: 'agents',
      icon: <RobotOutlined />,
      label: '智能体',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    }
  ];

  return (
    <Sider
      width={200}
      theme="dark"
      style={{
        background: 'linear-gradient(180deg, #001529 0%, #002140 100%)',
        borderRight: '1px solid #303030'
      }}
    >
      {/* 用户信息区域 */}
      <div style={{
        padding: '16px',
        textAlign: 'center',
        borderBottom: '1px solid #303030'
      }}>
        <Avatar
          size={48}
          icon={<UserOutlined />}
          src={currentUser.avatar}
          style={{ marginBottom: '8px' }}
        />
        <div>
          <Text style={{ color: '#fff', fontSize: '14px', fontWeight: 500 }}>
            {currentUser.username}
          </Text>
        </div>
      </div>

      {/* 导航菜单 */}
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={[currentTab]}
        onClick={({ key }) => onTabChange(key)}
        style={{
          background: 'transparent',
          border: 'none',
          flex: 1
        }}
        items={menuItems}
      />

      {/* 底部操作区域 */}
      <div style={{
        padding: '16px',
        borderTop: '1px solid #303030'
      }}>
        <Tooltip title="退出登录" placement="right">
          <Button
            type="text"
            icon={<LogoutOutlined />}
            onClick={onLogout}
            style={{
              color: '#fff',
              width: '100%',
              textAlign: 'left',
              padding: '8px 12px',
              height: 'auto'
            }}
          >
            退出登录
          </Button>
        </Tooltip>
      </div>
    </Sider>
  );
};

export default NavigationSidebar;
