/**
 * 处理结果组件
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Button,
  Space,
  Spin,
  Alert,
  Progress,
  Descriptions,
  Table,
  Tabs,
  Tag,
  Divider,
  // Modal,
  message,
} from 'antd';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  DownloadOutlined,
  ReloadOutlined,
  // EyeOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
// 模拟数据类型
interface WorkflowResult {
  workflow_id: string;
  status: string;
  file_info: any;
  extracted_data: Record<string, string>;
  generated_report: string;
  confidence_scores: Record<string, number>;
  validation_results: Record<string, any>;
  processing_time: number;
  error_message?: string;
}

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;

interface ProcessingResultProps {
  onPrev: () => void;
  onRestart: () => void;
}

const ProcessingResult: React.FC<ProcessingResultProps> = ({ onPrev, onRestart }) => {
  const [workflowResult, setWorkflowResult] = useState<WorkflowResult | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [progress, setProgress] = useState(0);
  const [currentStage, setCurrentStage] = useState('准备中...');

  useEffect(() => {
    if (!workflowResult && !isProcessing) {
      startProcessing();
    }
  }, []);

  const startProcessing = async () => {
    setIsProcessing(true);
    setError(null);
    setProgress(0);

    try {
      // 模拟处理进度
      const stages = [
        { stage: '解析文档内容...', progress: 20 },
        { stage: '初始化AI模型...', progress: 40 },
        { stage: '抽取字段信息...', progress: 70 },
        { stage: '验证抽取结果...', progress: 85 },
        { stage: '生成报告...', progress: 95 },
      ];

      for (const { stage, progress: stageProgress } of stages) {
        setCurrentStage(stage);
        setProgress(stageProgress);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // 模拟处理结果
      const mockResult: WorkflowResult = {
        workflow_id: 'mock-workflow-id',
        status: 'completed',
        file_info: {
          filename: 'test_document.txt',
          document_type: 'txt',
          text_length: 1000,
          paragraph_count: 10,
          table_count: 0
        },
        extracted_data: {
          '会议主题': '数字化转型项目进展讨论',
          '会议时间': '2024年7月4日 14:00-16:00',
          '主持人': '张总经理',
          '项目完成度': '75%'
        },
        confidence_scores: {
          '会议主题': 0.95,
          '会议时间': 0.98,
          '主持人': 0.90,
          '项目完成度': 0.85
        },
        validation_results: {
          '会议主题': { is_valid: true, confidence: 0.95 },
          '会议时间': { is_valid: true, confidence: 0.98 },
          '主持人': { is_valid: true, confidence: 0.90 },
          '项目完成度': { is_valid: true, confidence: 0.85 }
        },
        generated_report: `# 会议纪要报告

## 会议基本信息
- **会议主题**: 数字化转型项目进展讨论
- **会议时间**: 2024年7月4日 14:00-16:00
- **主持人**: 张总经理

## 项目进展
- **完成进度**: 75%

## 报告生成信息
- 生成时间: 2024年7月4日
- 数据来源: 文档自动解析
- 置信度评估: 见详细数据

---
*本报告由AI自动生成，请核验关键信息*`,
        processing_time: 5.2
      };

      setProgress(100);
      setCurrentStage('处理完成');
      setWorkflowResult(mockResult);
      message.success('文档处理完成！');

    } catch (error: any) {
      setError('处理失败');
      message.error('处理失败，请重试');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRetry = () => {
    setWorkflowResult(null);
    startProcessing();
  };

  const handleDownloadReport = () => {
    if (!workflowResult) return;

    const blob = new Blob([workflowResult.generated_report], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `报告_${workflowResult.workflow_id}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const extractionColumns: ColumnsType<any> = [
    {
      title: '字段名称',
      dataIndex: 'field_name',
      key: 'field_name',
      width: 120,
    },
    {
      title: '抽取结果',
      dataIndex: 'value',
      key: 'value',
      ellipsis: true,
      render: (text: string) => (
        <Text style={{ maxWidth: 300 }} ellipsis={{ tooltip: text }}>
          {text || '未找到'}
        </Text>
      ),
    },
    {
      title: '置信度',
      dataIndex: 'confidence',
      key: 'confidence',
      width: 100,
      render: (confidence: number) => (
        <Tag color={confidence > 0.8 ? 'green' : confidence > 0.5 ? 'orange' : 'red'}>
          {(confidence * 100).toFixed(0)}%
        </Tag>
      ),
    },
    {
      title: '验证状态',
      dataIndex: 'validation',
      key: 'validation',
      width: 100,
      render: (validation: any) => {
        const isValid = validation?.is_valid;
        return (
          <Tag color={isValid ? 'green' : 'red'} icon={isValid ? <CheckCircleOutlined /> : <CloseCircleOutlined />}>
            {isValid ? '有效' : '无效'}
          </Tag>
        );
      },
    },
  ];

  // 准备表格数据
  const extractionData = workflowResult ? Object.keys(workflowResult.extracted_data).map(fieldName => ({
    field_name: fieldName,
    value: workflowResult.extracted_data[fieldName],
    confidence: workflowResult.confidence_scores[fieldName] || 0,
    validation: workflowResult.validation_results[fieldName],
  })) : [];

  if (isProcessing) {
    return (
      <div style={{ maxWidth: 800, margin: '0 auto', padding: '24px' }}>
        <Card style={{ textAlign: 'center', padding: '40px' }}>
          <Spin size="large" />
          <Title level={3} style={{ marginTop: '24px' }}>
            正在处理文档...
          </Title>
          <Paragraph type="secondary">
            {currentStage}
          </Paragraph>
          <Progress 
            percent={progress} 
            status="active"
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
            style={{ maxWidth: 400, margin: '24px auto' }}
          />
          <Paragraph type="secondary" style={{ fontSize: '12px' }}>
            处理时间可能需要30-60秒，请耐心等待...
          </Paragraph>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ maxWidth: 800, margin: '0 auto', padding: '24px' }}>
        <Alert
          message="处理失败"
          description={error}
          type="error"
          showIcon
          action={
            <Space>
              <Button size="small" onClick={handleRetry}>
                重试
              </Button>
              <Button size="small" onClick={onPrev}>
                返回
              </Button>
            </Space>
          }
        />
      </div>
    );
  }

  if (!workflowResult) {
    return (
      <div style={{ maxWidth: 800, margin: '0 auto', padding: '24px' }}>
        <Alert
          message="等待处理"
          description="请稍候，系统正在准备处理您的文档..."
          type="info"
          showIcon
        />
      </div>
    );
  }

  const overallConfidence = extractionData.length > 0 
    ? extractionData.reduce((sum, item) => sum + item.confidence, 0) / extractionData.length 
    : 0;

  const validationRate = extractionData.length > 0
    ? extractionData.filter(item => item.validation?.is_valid).length / extractionData.length
    : 0;

  return (
    <div style={{ maxWidth: 1200, margin: '0 auto', padding: '24px' }}>
      <Title level={2}>✅ 处理完成</Title>
      <Paragraph type="secondary">
        文档处理已完成，以下是抽取结果和生成的报告。
      </Paragraph>

      <Tabs defaultActiveKey="summary">
        <TabPane tab="处理概览" key="summary">
          <Card>
            <Descriptions title="处理信息" bordered column={2}>
              <Descriptions.Item label="工作流ID">{workflowResult.workflow_id}</Descriptions.Item>
              <Descriptions.Item label="处理状态">
                <Tag color="green">已完成</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="处理时间">{workflowResult.processing_time.toFixed(2)} 秒</Descriptions.Item>
              <Descriptions.Item label="文档类型">{workflowResult.file_info.document_type.toUpperCase()}</Descriptions.Item>
              <Descriptions.Item label="文档大小">{workflowResult.file_info.text_length} 字符</Descriptions.Item>
              <Descriptions.Item label="抽取字段数">{extractionData.length} 个</Descriptions.Item>
              <Descriptions.Item label="整体置信度">
                <Tag color={overallConfidence > 0.8 ? 'green' : overallConfidence > 0.5 ? 'orange' : 'red'}>
                  {(overallConfidence * 100).toFixed(1)}%
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="验证通过率">
                <Tag color={validationRate > 0.8 ? 'green' : validationRate > 0.5 ? 'orange' : 'red'}>
                  {(validationRate * 100).toFixed(1)}%
                </Tag>
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </TabPane>

        <TabPane tab="抽取结果" key="extraction">
          <Card>
            <Table
              columns={extractionColumns}
              dataSource={extractionData}
              rowKey="field_name"
              pagination={false}
              size="middle"
            />
          </Card>
        </TabPane>

        <TabPane tab="生成报告" key="report">
          <Card
            title="生成的报告"
            extra={
              <Button icon={<DownloadOutlined />} onClick={handleDownloadReport}>
                下载报告
              </Button>
            }
          >
            <div style={{ 
              background: '#f5f5f5', 
              padding: '16px', 
              borderRadius: '6px',
              maxHeight: '600px',
              overflow: 'auto'
            }}>
              <pre style={{ 
                whiteSpace: 'pre-wrap', 
                margin: 0, 
                fontFamily: 'monospace',
                fontSize: '14px',
                lineHeight: '1.6'
              }}>
                {workflowResult.generated_report}
              </pre>
            </div>
          </Card>
        </TabPane>
      </Tabs>

      <Divider />

      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <Space>
          <Button onClick={onPrev}>
            返回配置
          </Button>
          <Button icon={<ReloadOutlined />} onClick={handleRetry}>
            重新处理
          </Button>
        </Space>
        <Button type="primary" onClick={onRestart}>
          处理新文档
        </Button>
      </div>
    </div>
  );
};

export default ProcessingResult;
