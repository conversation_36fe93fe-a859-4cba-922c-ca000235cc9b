#!/usr/bin/env python3
"""
调试API响应结构
"""

import requests
import json

def debug_api_response():
    """调试API响应结构"""
    
    workflow_id = "f9b279d4-45e4-43c9-8abf-1999a099f734"
    
    print(f"🔍 调试工作流状态API响应结构: {workflow_id}")
    
    try:
        response = requests.get(f'http://localhost:8000/api/workflow/langgraph/status/{workflow_id}')
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"\n📊 API响应结构分析:")
            print(f"根级别字段: {list(result.keys())}")
            
            # 检查关键字段
            print(f"\n🔍 关键字段分析:")
            print(f"  success: {result.get('success')}")
            print(f"  workflow_id: {result.get('workflow_id')}")
            print(f"  current_stage: {result.get('current_stage')}")
            print(f"  next_action: {result.get('next_action')}")
            print(f"  requires_user_action: {result.get('requires_user_action')}")
            print(f"  pending_interactions: {result.get('pending_interactions', [])}")
            print(f"  human_interactions数量: {len(result.get('human_interactions', []))}")
            print(f"  extraction_results数量: {len(result.get('extraction_results', []))}")
            
            # 检查人机交互
            human_interactions = result.get('human_interactions', [])
            if human_interactions:
                print(f"\n🤝 人机交互分析:")
                for i, interaction in enumerate(human_interactions):
                    print(f"  交互 {i+1}:")
                    print(f"    类型: {interaction.get('interaction_type')}")
                    print(f"    字段: {interaction.get('field_name')}")
                    print(f"    完成状态: {interaction.get('is_completed')}")
                    
                # 找到最后一个未完成的交互
                pending_interactions = [hi for hi in human_interactions if not hi.get('is_completed', True)]
                if pending_interactions:
                    last_pending = pending_interactions[-1]
                    print(f"\n⚠️ 当前待处理交互:")
                    print(f"    类型: {last_pending.get('interaction_type')}")
                    print(f"    字段: {last_pending.get('field_name')}")
                    print(f"    时间: {last_pending.get('timestamp')}")
            
            # 检查提取结果
            extraction_results = result.get('extraction_results', [])
            if extraction_results:
                print(f"\n📋 提取结果分析 (前3个):")
                for i, result_item in enumerate(extraction_results[:3]):
                    print(f"  结果 {i+1}:")
                    print(f"    字段: {result_item.get('field_name')}")
                    print(f"    值: {result_item.get('field_value')}")
                    print(f"    置信度: {result_item.get('confidence')}")
            
            print(f"\n📄 完整响应 (前500字符):")
            response_str = json.dumps(result, ensure_ascii=False, indent=2)
            print(response_str[:500] + "..." if len(response_str) > 500 else response_str)
                
        else:
            print(f"❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")

if __name__ == "__main__":
    debug_api_response()
