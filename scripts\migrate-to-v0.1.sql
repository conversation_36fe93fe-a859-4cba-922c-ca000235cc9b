-- 数据库迁移脚本：从当前版本升级到V0.1
-- 支持LangGraph智能体架构的数据结构

-- 备份现有数据
CREATE TABLE IF NOT EXISTS backup_templates AS SELECT * FROM templates;
CREATE TABLE IF NOT EXISTS backup_template_fields AS SELECT * FROM template_fields;
CREATE TABLE IF NOT EXISTS backup_data_sources AS SELECT * FROM data_sources;
CREATE TABLE IF NOT EXISTS backup_extracted_data AS SELECT * FROM extracted_data;
CREATE TABLE IF NOT EXISTS backup_reports AS SELECT * FROM reports;

-- 添加新字段到现有表
ALTER TABLE templates ADD COLUMN IF NOT EXISTS category VARCHAR(50);
ALTER TABLE templates ADD COLUMN IF NOT EXISTS version VARCHAR(20);
ALTER TABLE templates ADD COLUMN IF NOT EXISTS schema_json JSONB;
ALTER TABLE templates ADD COLUMN IF NOT EXISTS key_definitions_path VARCHAR(500);

ALTER TABLE data_sources ADD COLUMN IF NOT EXISTS file_hash VARCHAR(64);
ALTER TABLE data_sources ADD COLUMN IF NOT EXISTS extraction_count INTEGER DEFAULT 0;
ALTER TABLE data_sources ADD COLUMN IF NOT EXISTS file_metadata JSONB;

ALTER TABLE extracted_data ADD COLUMN IF NOT EXISTS session_id UUID;
ALTER TABLE extracted_data ADD COLUMN IF NOT EXISTS field_path VARCHAR(500);
ALTER TABLE extracted_data ADD COLUMN IF NOT EXISTS original_text TEXT;
ALTER TABLE extracted_data ADD COLUMN IF NOT EXISTS verification_status VARCHAR(20) DEFAULT 'pending';
ALTER TABLE extracted_data ADD COLUMN IF NOT EXISTS position_info JSONB;
ALTER TABLE extracted_data ADD COLUMN IF NOT EXISTS parent_id UUID;
ALTER TABLE extracted_data ADD COLUMN IF NOT EXISTS level INTEGER DEFAULT 1;

ALTER TABLE reports ADD COLUMN IF NOT EXISTS session_id UUID;
ALTER TABLE reports ADD COLUMN IF NOT EXISTS generation_method VARCHAR(50) DEFAULT 'ai';
ALTER TABLE reports ADD COLUMN IF NOT EXISTS quality_score DECIMAL(3,2);
ALTER TABLE reports ADD COLUMN IF NOT EXISTS download_count INTEGER DEFAULT 0;
ALTER TABLE reports ADD COLUMN IF NOT EXISTS report_metadata JSONB;

-- 创建新表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    department VARCHAR(100),
    role VARCHAR(20) NOT NULL DEFAULT 'user',
    is_active BOOLEAN DEFAULT TRUE,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100)
);

CREATE TABLE IF NOT EXISTS key_definitions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    template_id UUID NOT NULL REFERENCES templates(id) ON DELETE CASCADE,
    field_name VARCHAR(255) NOT NULL,
    parent_field VARCHAR(255),
    field_level INTEGER DEFAULT 1,
    field_path VARCHAR(500),
    description TEXT,
    example TEXT,
    format VARCHAR(50) NOT NULL DEFAULT '普通文本',
    default_value TEXT,
    length INTEGER,
    source VARCHAR(50) NOT NULL DEFAULT '上传内容',
    must BOOLEAN DEFAULT FALSE,
    field_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(template_id, field_path)
);

CREATE TABLE IF NOT EXISTS agent_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    session_name VARCHAR(255),
    session_type VARCHAR(50) DEFAULT 'report_generation',
    status VARCHAR(50) DEFAULT 'active',
    current_agent VARCHAR(100),
    current_stage VARCHAR(100),
    context_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE
);

CREATE TABLE IF NOT EXISTS conversation_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID NOT NULL REFERENCES agent_sessions(id) ON DELETE CASCADE,
    message_type VARCHAR(50) NOT NULL,
    sender VARCHAR(100),
    content TEXT NOT NULL,
    message_metadata JSONB,
    tool_calls JSONB,
    attachments JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS tool_calls (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID NOT NULL REFERENCES agent_sessions(id) ON DELETE CASCADE,
    conversation_id UUID REFERENCES conversation_history(id),
    tool_name VARCHAR(100) NOT NULL,
    tool_input JSONB NOT NULL,
    tool_output JSONB,
    status VARCHAR(50) DEFAULT 'pending',
    error_message TEXT,
    execution_time INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- 修改现有的generation_tasks表
ALTER TABLE generation_tasks ADD COLUMN IF NOT EXISTS session_id UUID;
ALTER TABLE generation_tasks ADD FOREIGN KEY (session_id) REFERENCES agent_sessions(id);

-- 添加外键约束
ALTER TABLE extracted_data ADD FOREIGN KEY (session_id) REFERENCES agent_sessions(id);
ALTER TABLE extracted_data ADD FOREIGN KEY (parent_id) REFERENCES extracted_data(id);
ALTER TABLE reports ADD FOREIGN KEY (session_id) REFERENCES agent_sessions(id);

-- 创建新索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_key_definitions_template_id ON key_definitions(template_id);
CREATE INDEX IF NOT EXISTS idx_key_definitions_field_path ON key_definitions(field_path);
CREATE INDEX IF NOT EXISTS idx_extracted_data_session_id ON extracted_data(session_id);
CREATE INDEX IF NOT EXISTS idx_extracted_data_parent_id ON extracted_data(parent_id);
CREATE INDEX IF NOT EXISTS idx_reports_session_id ON reports(session_id);
CREATE INDEX IF NOT EXISTS idx_agent_sessions_user_id ON agent_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_agent_sessions_status ON agent_sessions(status);
CREATE INDEX IF NOT EXISTS idx_conversation_history_session_id ON conversation_history(session_id);
CREATE INDEX IF NOT EXISTS idx_conversation_history_created_at ON conversation_history(created_at);
CREATE INDEX IF NOT EXISTS idx_tool_calls_session_id ON tool_calls(session_id);
CREATE INDEX IF NOT EXISTS idx_tool_calls_tool_name ON tool_calls(tool_name);
CREATE INDEX IF NOT EXISTS idx_generation_tasks_session_id ON generation_tasks(session_id);

-- 创建触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_key_definitions_updated_at BEFORE UPDATE ON key_definitions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agent_sessions_updated_at BEFORE UPDATE ON agent_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入示例数据
INSERT INTO users (username, email, password_hash, full_name, role, created_by) VALUES 
('admin', '<EMAIL>', 'hashed_password', '系统管理员', 'admin', 'system'),
('user1', '<EMAIL>', 'hashed_password', '测试用户1', 'user', 'system')
ON CONFLICT (username) DO NOTHING;

-- 更新模板数据
UPDATE templates SET category = '会议纪要' WHERE name LIKE '%会议纪要%';
UPDATE templates SET category = '工程报告' WHERE name LIKE '%超限报告%';

COMMIT;
