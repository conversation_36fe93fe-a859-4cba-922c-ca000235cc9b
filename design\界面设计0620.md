# 界面设计（0620版）

## 主页面

### 导航栏（界面左侧，可左右拖动宽度，动态调整主界面占比）
- 标签页：模板管理、数据管理、结果管理
- 系统设置栏：系统设置、UI选择

### 主操作界面（右侧，可与导航栏动态调整宽度）

#### 文件上传区（主操作区顶部）
- 支持上传Word（docx）文件，上传后显示文件名称，可重新上传

#### 结果交互区（主操作区中部，支持多类型数据展示）
- 展示模板数据、抽取数据、以docx为基础的结果报告数据
- 支持文字、表格、图片、docx文件、结构化数据等多种类型
- 支持编辑：如模板识别结果、数据抽取结果、报告生成结果
- 支持scroll bar上下滑动

#### 状态区（主操作区中下部）
- 下拉选择模板、数据，右侧显示大模型交互状态（模板分析、信息抽取、报告生成）

#### 功能沟通区（主操作区底部）
- 用户输入命令，驱动AI智能体完成各项功能
- 右下角有"确定"按钮（缩小为原1/3），提交输入内容

---

## 模板管理
- 列表展示所有模板，支持增删改查和下载
- 列表字段：模板名称、创建人、创建时间、模板描述、占位符数量、被引用次数
- 支持表头排序（默认按创建时间倒序）
- 新增模板：弹窗上传Word模板，填写名称、描述
- 编辑模板：弹窗编辑名称、描述、重新上传文件
- 下载模板：支持下载原文件
- 占位符数量：弹窗显示详情，支持复制
- 列表底部有横向分页器，支持大量模板管理

## 数据管理
- 列表展示所有数据，支持增删改查和下载
- 列表字段：数据名称、关联模板、生成时间、来源数据（Word）、抽取结果预览
- 支持表头排序（默认按生成时间倒序）
- 新增数据：弹窗上传数据，填写名称、选择关联模板、上传数据源（可选）
- 编辑数据：弹窗编辑名称、重新上传数据文件
- 来源数据：点击弹窗，可下载原数据
- 抽取结果预览：展开查看结构化数据、表格、图片等内容
- 列表底部有横向分页器，支持大量数据管理

## 结果管理
- 列表展示所有已生成报告，支持增删改查和下载
- 列表字段：报告名称、关联模板、关联数据、生成时间、报告版本、报告预览
- 支持表头排序（默认按生成时间倒序）
- 生成报告：选择模板、数据，填写报告名称、描述，启动报告生成
- 报告详情：预览报告内容、生成日志、填充数据明细
- 下载报告：支持下载Word文件
- 报告预览：在线浏览，支持分页、跳转、放大缩小
- 列表底部有横向分页器，支持大量报告管理

---

## 交互与AI智能体支持
- 所有核心操作（模板分析、信息抽取、报告生成）均可通过自然语言驱动，AI智能体（Qwen-32B+LangGraph）负责理解、处理和反馈
- 结果交互区可实时展示AI处理结果，支持用户编辑和二次确认
- 嵌入模型（bge-large-zh-v1.5，硅基流动接口）支持语义检索、相似度匹配等高级功能

---

## 其他说明
- 全局支持Word文档的上传、编辑、下载，报告生成过程保持原有排版不变
- 所有列表均支持分页、排序、弹窗编辑、下载等核心交互
- 删除了用户管理、权限、项目管理等与新架构无关的界面和描述 