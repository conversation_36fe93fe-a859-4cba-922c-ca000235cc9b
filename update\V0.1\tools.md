
# 智能模板识别工具
功能：基于用户输入的自然语言描述，智能分析文本生成意图，识别最适合的报告模板类型，支持多模板匹配和用户确认选择。

## 核心流程
1. **意图分析阶段**：解析用户输入，提取关键词和生成需求
2. **模板匹配阶段**：基于意图分析结果匹配合适的模板
3. **置信度评估阶段**：评估匹配结果的准确性和可信度
4. **用户确认阶段**：向用户展示匹配结果并支持手动选择

## 输入参数
- 用户输入文本（自然语言描述的生成需求）
- 用户上下文信息（历史使用记录、偏好设置等）

## 数据访问
- 所有可用模板的名称和描述信息
- 模板关键词库和分类标签
- 用户历史使用记录和偏好数据

## 输出结果
- 推荐的Word模板文件名称（按匹配度排序）
- 对应的模板关键信息文件路径
- 匹配置信度和推荐理由

## 人工干预机制
- **单一匹配**：高置信度匹配时，显示推荐模板供用户确认
- **多重匹配**：多个候选模板时，提供选择界面供用户决策
- **无匹配**：未找到合适模板时，引导用户重新描述或创建新模板

## 配合的前端控件
- **模板卡片展示**：可视化展示推荐模板的预览和描述
- **置信度指示器**：显示每个推荐模板的匹配置信度
- **选择按钮组**：支持快速选择或查看更多模板选项

# 智能模板解析工具
功能：解析选定Word模板中的占位符结构，结合模板关键信息定义文件，生成针对性的数据提取prompt，为后续的智能提取做准备。

## 核心流程
1. **占位符识别阶段**：扫描Word模板，识别所有{{placeholder}}格式的占位符
2. **定义匹配阶段**：将占位符与模板关键信息文件中的字段定义进行匹配
3. **Prompt生成阶段**：基于字段定义生成结构化的数据提取prompt
4. **验证检查阶段**：检查生成的prompt的完整性和准确性

## 输入参数
- Word模板文件名称和路径
- 模板关键信息文件（包含字段定义、格式要求、示例等）

## 数据访问
- Word模板文件内容和结构信息
- 模板关键信息文件（字段定义、验证规则、默认值等）
- 占位符解析规则和格式标准

## 输出结果
- 结构化的模板关键信息提取prompt
- 占位符映射表（占位符与字段定义的对应关系）
- 解析日志（记录解析过程和发现的问题）

## 人工干预机制
- **自动解析**：大多数情况下无需人工干预，自动完成解析
- **异常处理**：发现未定义占位符或格式错误时，提示用户处理
- **质量确认**：可选的人工审核生成的prompt质量

## 配合的前端控件
- **解析进度指示器**：显示模板解析的实时进度
- **占位符列表**：展示识别到的所有占位符及其状态
- **错误提示面板**：显示解析过程中发现的问题和建议


# 智能数据提取与验证工具
功能：基于模板关键信息提取prompt，对用户上传的文件、用户输入的文本进行智能解析和提取，自动完成数据完整性检查，对于提取不到的内容使用默认值填充，并提供统一的人工确认和补充界面。

## 核心流程
1. **智能提取阶段**：根据模板关键信息文件中的字段定义，从用户上传文件和输入文本中提取关键信息
2. **自动验证阶段**：立即检查提取结果的完整性、格式正确性和必填项合规性
3. **结果整合阶段**：将提取结果、验证状态、缺失项分析整合为统一的数据结构
4. **人工确认阶段**：向用户展示完整的提取和验证结果，支持一站式确认和补充

## 输入参数
- 模板关键信息提取prompt
- 用户上传文件（支持多文件）
- 用户输入的文本内容
- 模板关键信息文件（字段定义、验证规则、默认值）

## 数据访问
- 用户上传文件内容解析
- 用户输入文本内容
- 模板关键信息文件（字段定义、格式要求、默认值、验证规则）
- 历史提取记录（用于学习优化）

## 输出结果
- 提取数据文件（md格式，包含提取值、置信度、来源位置）
- 验证报告（完整性评分、缺失字段列表、格式错误提示）
- 操作建议（补充建议、默认值应用建议、质量改进建议）

## 人工干预机制
工具完成提取和验证后，向用户展示统一的确认界面，包含：

### 数据展示区域
- **已成功提取**：显示提取成功的字段，标注置信度和来源
- **使用默认值**：显示采用默认值的字段，允许用户修改
- **缺失或异常**：显示未能提取或格式异常的字段，提供补充选项

### 用户操作选项
1. **直接确认**：对当前提取结果满意，继续下一步报告生成
2. **手动补充**：在界面中直接编辑和补充缺失或错误的字段值
3. **补充文件**：上传新的文件进行补充提取，系统自动重新运行提取和验证
4. **调整模板**：发现模板定义问题时，可跳转到模板编辑进行调整

## 配合的前端控件
- **分类展示卡片**：按提取状态分类显示字段（成功/默认/缺失）
- **置信度指示器**：可视化显示每个字段的提取置信度
- **内联编辑器**：支持直接在结果页面编辑字段值
- **文件上传区域**：支持拖拽上传补充文件
- **操作按钮组**：确认、补充、重新提取等操作按钮
- **进度指示器**：显示整体数据完整性进度

# 智能报告生成工具
功能：基于Word模板和已验证的提取数据，智能生成高质量的报告文件，支持实时预览、质量检查和多格式输出。

## 核心流程
1. **数据映射阶段**：将已确认的提取数据精确映射到模板占位符
2. **内容生成阶段**：基于模板结构和数据生成完整报告内容
3. **质量检查阶段**：自动检查格式规范性、内容完整性和逻辑一致性
4. **文件输出阶段**：生成最终报告文件并提供预览和下载

## 输入参数
- 已验证的提取数据文件（md格式，包含置信度和验证状态）
- Word模板文件（包含占位符和格式定义）
- 生成配置（输出格式、质量要求等）

## 数据访问
- 提取数据文件（经过人工确认的结构化数据）
- Word模板文件内容和格式信息
- 报告生成规则和质量标准
- 历史生成记录（用于优化生成效果）

## 输出结果
- Word报告文件（主要格式，保持模板原有格式）
- 生成日志（记录数据填充过程和质量检查结果）
- 质量评估报告（完整性、准确性、格式规范性评分）

## 人工干预机制
生成完成后提供综合的预览和确认界面：

### 预览展示区域
- **报告预览**：支持分页浏览、缩放、全屏预览的Word文档展示
- **填充状态**：显示哪些占位符已成功填充、哪些使用了默认值
- **质量指标**：显示报告完整性、格式规范性等质量评分

### 用户操作选项
1. **直接下载**：对报告满意，直接下载Word文件
2. **导出PDF**：生成PDF格式的报告文件（可选功能）
3. **重新生成**：调整生成参数后重新生成报告
4. **返回修改**：返回数据提取步骤修改数据内容
5. **保存草稿**：保存当前报告到结果目录，稍后继续处理

## 配合的前端控件
- **Word文档预览器**：支持分页、缩放、全屏的文档预览组件
- **质量仪表盘**：可视化显示报告质量指标和完成度
- **下载按钮组**：支持Word、PDF等多种格式的下载选项
- **编辑跳转按钮**：快速返回到数据编辑页面
- **文件管理面板**：查看和管理已生成的报告文件