"""
模板相关数据模型
"""

from sqlalchemy import Column, String, Text, Integer, Boolean, ForeignKey
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from .core import BaseModel


class Template(BaseModel):
    """模板表"""

    __tablename__ = "templates"

    name = Column(String(255), nullable=False, comment="模板名称")
    description = Column(Text, nullable=True, comment="模板描述")
    category = Column(String(50), nullable=True, comment="模板分类")
    file_path = Column(String(500), nullable=False, comment="文件路径")
    file_size = Column(Integer, nullable=True, comment="文件大小(字节)")
    mime_type = Column(String(100), nullable=True, comment="MIME类型")
    schema_json = Column(JSONB, nullable=True, comment="字段结构JSON")
    placeholder_count = Column(Integer, default=0, comment="占位符数量")
    version = Column(String(20), nullable=True, comment="模板版本")
    key_definitions_path = Column(String(500), nullable=True, comment="关键信息定义文件路径")

    # 关联关系
    fields = relationship("TemplateField", back_populates="template", cascade="all, delete-orphan")
    key_definitions = relationship("KeyDefinition", back_populates="template", cascade="all, delete-orphan")
    reports = relationship("Report", back_populates="template")
    extracted_data = relationship("ExtractedData", back_populates="template")

    def __repr__(self):
        return f"<Template(id={self.id}, name='{self.name}', version='{self.version}')>"


class TemplateField(BaseModel):
    """模板字段表"""

    __tablename__ = "template_fields"

    template_id = Column(UUID(as_uuid=True), ForeignKey("templates.id"), nullable=False, comment="模板ID")
    field_name = Column(String(255), nullable=False, comment="字段名称")
    field_label = Column(String(255), nullable=True, comment="字段显示名")

    # 基于expect_procedures.md的字段定义规范
    description = Column(Text, nullable=True, comment="字段描述，用于嵌入在提示词中进行信息提取")
    example = Column(Text, nullable=True, comment="字段样例，用于嵌入在提示词中进行信息提取")
    format = Column(String(50), nullable=False, default="普通文本", comment="字段类型（普通文本/表格行/段落/人名/单独行等）")
    default = Column(Text, nullable=True, comment="默认值，有些有默认值，有些默认值为空")
    length = Column(Integer, nullable=True, comment="篇幅限制")
    source = Column(String(50), nullable=False, default="上传内容", comment="信息来源（上传内容/系统时间/既有知识/外部搜索）")
    must = Column(Boolean, default=False, comment="是否必须字段（true/false）")

    # 保留原有的其他字段
    field_order = Column(Integer, default=0, comment="字段顺序")
    validation_rules = Column(JSONB, nullable=True, comment="验证规则")

    # 关联关系
    template = relationship("Template", back_populates="fields")

    def __repr__(self):
        return f"<TemplateField(id={self.id}, name='{self.field_name}', format='{self.format}', must={self.must})>"