#!/usr/bin/env python3
"""
检查template_fields表结构
"""

import os

# 设置环境变量
os.environ['DATABASE_URL'] = 'postgresql://report_user:report_pass@localhost:5432/report_gen'

def check_template_fields():
    """检查template_fields表结构"""
    print("🔍 检查template_fields表结构...")
    
    try:
        from database import engine
        from sqlalchemy import text
        
        with engine.connect() as conn:
            # 检查template_fields表的字段
            result = conn.execute(text("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = 'template_fields'
                ORDER BY ordinal_position
            """))
            columns = [(row[0], row[1], row[2], row[3]) for row in result.fetchall()]
            
            print("✅ template_fields表字段:")
            for col_name, data_type, nullable, default in columns:
                print(f"  - {col_name}: {data_type} {'NULL' if nullable == 'YES' else 'NOT NULL'} {f'DEFAULT {default}' if default else ''}")
            
            # 检查是否包含expect_procedures.md要求的字段
            required_fields = ['description', 'example', 'format', 'default', 'length', 'source', 'must']
            existing_fields = [col[0] for col in columns]
            
            print("\n🎯 字段对比检查:")
            for field in required_fields:
                if field in existing_fields:
                    print(f"  ✅ {field}: 存在")
                else:
                    print(f"  ❌ {field}: 缺失")
            
            # 检查额外字段
            extra_fields = [field for field in existing_fields if field not in required_fields + ['id', 'template_id', 'field_name', 'field_label', 'field_order', 'validation_rules', 'created_at', 'updated_at', 'created_by', 'is_active']]
            if extra_fields:
                print(f"\n📝 额外字段: {extra_fields}")
            
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    check_template_fields()
