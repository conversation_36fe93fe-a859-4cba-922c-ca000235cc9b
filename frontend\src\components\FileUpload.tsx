/**
 * 文件上传组件
 */

import React, { useState } from 'react';
import { Upload, Button, Card, Typography, Space, Alert, Progress, message } from 'antd';
import { InboxOutlined, FileTextOutlined, DeleteOutlined } from '@ant-design/icons';
import type { UploadProps } from 'antd';

const { Dragger } = Upload;
const { Title, Text, Paragraph } = Typography;

interface FileUploadProps {
  onNext: () => void;
}

const FileUpload: React.FC<FileUploadProps> = ({ onNext }) => {
  const [uploadedFile, setUploadedFile] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const uploadProps: UploadProps = {
    name: 'file',
    multiple: false,
    accept: '.docx,.txt',
    showUploadList: false,
    beforeUpload: async (file) => {
      // 检查文件类型
      const allowedTypes = ['application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'];
      const isValidType = allowedTypes.includes(file.type) || file.name.endsWith('.docx') || file.name.endsWith('.txt');

      if (!isValidType) {
        setError('只支持 .docx 和 .txt 文件格式');
        return false;
      }

      // 检查文件大小 (50MB)
      const isValidSize = file.size / 1024 / 1024 < 50;
      if (!isValidSize) {
        setError('文件大小不能超过 50MB');
        return false;
      }

      setError(null);
      setUploading(true);
      setUploadProgress(0);

      try {
        // 模拟上传进度
        const progressInterval = setInterval(() => {
          setUploadProgress((prev) => {
            if (prev >= 90) {
              clearInterval(progressInterval);
              return 90;
            }
            return prev + 10;
          });
        }, 200);

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 2000));

        clearInterval(progressInterval);
        setUploadProgress(100);

        setTimeout(() => {
          // 模拟上传结果
          const mockResult = {
            file_id: 'mock-file-id',
            filename: file.name,
            document_type: file.name.endsWith('.docx') ? 'docx' : 'txt',
            text_length: 1000,
            paragraph_count: 10,
            table_count: 0,
            preview: '这是一个模拟的文档预览内容...'
          };

          setUploadedFile(mockResult);
          setUploading(false);
          setUploadProgress(0);
          message.success('文件上传成功！');
        }, 500);

      } catch (error: any) {
        setUploading(false);
        setUploadProgress(0);
        setError('文件上传失败');
      }

      return false; // 阻止默认上传行为
    },
  };

  const handleRemoveFile = async () => {
    if (uploadedFile) {
      setUploadedFile(null);
      message.success('文件已删除');
    }
  };

  const handleNext = () => {
    if (uploadedFile) {
      onNext();
    }
  };

  return (
    <div style={{ maxWidth: 800, margin: '0 auto', padding: '24px' }}>
      <Title level={2}>📄 上传文档</Title>
      <Paragraph type="secondary">
        请上传需要处理的文档文件。支持 .docx 和 .txt 格式，文件大小不超过 50MB。
      </Paragraph>

      {!uploadedFile ? (
        <Card>
          <Dragger {...uploadProps} style={{ padding: '40px 20px' }}>
            <p className="ant-upload-drag-icon">
              <InboxOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
            </p>
            <p className="ant-upload-text" style={{ fontSize: '16px', marginBottom: '8px' }}>
              点击或拖拽文件到此区域上传
            </p>
            <p className="ant-upload-hint" style={{ color: '#666' }}>
              支持 .docx 和 .txt 格式，单个文件不超过 50MB
            </p>
          </Dragger>

          {uploading && (
            <div style={{ marginTop: '16px' }}>
              <Progress 
                percent={uploadProgress} 
                status="active"
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
              />
              <Text type="secondary">正在上传文件...</Text>
            </div>
          )}
        </Card>
      ) : (
        <Card>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Space>
                <FileTextOutlined style={{ fontSize: '24px', color: '#52c41a' }} />
                <div>
                  <Title level={4} style={{ margin: 0 }}>
                    {uploadedFile.filename}
                  </Title>
                  <Text type="secondary">
                    {uploadedFile.document_type.toUpperCase()} • {uploadedFile.text_length} 字符 • {uploadedFile.paragraph_count} 段落
                    {uploadedFile.table_count > 0 && ` • ${uploadedFile.table_count} 表格`}
                  </Text>
                </div>
              </Space>
              <Button 
                type="text" 
                danger 
                icon={<DeleteOutlined />}
                onClick={handleRemoveFile}
              >
                删除
              </Button>
            </div>

            <Alert
              message="文档预览"
              description={
                <div style={{ maxHeight: '120px', overflow: 'auto', marginTop: '8px' }}>
                  <Text code style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>
                    {uploadedFile.preview}
                  </Text>
                </div>
              }
              type="info"
              showIcon
            />

            <div style={{ textAlign: 'right', marginTop: '16px' }}>
              <Button type="primary" size="large" onClick={handleNext}>
                下一步：配置字段
              </Button>
            </div>
          </Space>
        </Card>
      )}
    </div>
  );
};

export default FileUpload;
