"""
用户相关数据模型
"""

from sqlalchemy import Column, String, Boolean, DateTime
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from .core import BaseModel


class User(BaseModel):
    """用户表"""
    
    __tablename__ = "users"
    
    username = Column(String(50), unique=True, nullable=False, comment="用户名")
    email = Column(String(100), unique=True, nullable=True, comment="邮箱地址")
    password_hash = Column(String(255), nullable=False, comment="密码哈希")
    full_name = Column(String(100), nullable=True, comment="真实姓名")
    department = Column(String(100), nullable=True, comment="部门")
    role = Column(String(20), nullable=False, default="user", comment="角色")
    is_active = Column(Boolean, default=True, nullable=False, comment="是否激活")
    last_login_at = Column(DateTime, nullable=True, comment="最后登录时间")
    
    # 关联关系
    agent_sessions = relationship("AgentSession", back_populates="user", cascade="all, delete-orphan")
    # templates = relationship("Template", back_populates="creator")  # 暂时注释掉
    # data_sources = relationship("DataSource", back_populates="creator")  # 暂时注释掉
    # reports = relationship("Report", back_populates="creator")  # 暂时注释掉
    # review_records = relationship("ReviewRecord", back_populates="user")  # 暂时注释掉
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', role='{self.role}')>"
    
    def check_password(self, password: str) -> bool:
        """检查密码"""
        # TODO: 实现密码验证逻辑
        return True
    
    def is_admin(self) -> bool:
        """检查是否为管理员"""
        return self.role == "admin"
