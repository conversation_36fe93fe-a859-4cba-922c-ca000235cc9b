一、当前问题分析：
界面问题：
1.智能化程度不足，希望改善流程，形成agentic的应用模式；
2.增加对模板、数据和生成结果报告的单独管理界面；
3.应用界面中上下栏冗余；
智能体效果问题：
1.目前没有智能体，需要用langgraph进行开发，实现human in the loop应用效果；
2.未来需要实现多智能体协同（除报告生成外，还有报告审查，信息查询，表格制作等功能），目前仅实现基于模板的关键信息提取与报告生成；
3.缺乏人机交互和确认环节；
数据提取问题：
1.没有充分利用提供的数据模板，应该严格按照数据模板中的内容进行提取，需要提取模板关键数据markdown文件内容，生成相关的prompt再进行提取；
2.数据提取后，需要进行人工确认，可以进行人工交互，完善数据，如人工补充信息，再进行确认，对于没有提取到的数据，由人工决定是否留空，还是再进行补充；
结果生成问题：
1.生成的结果报告文件，没有遵循模板文件中的word版式，结果生成不应该是生成一个新的word文件，而是基于模板，打开模板后，进行内容替换，添加，保持原模板中的文字、表格、图片等样式不变；
架构问题：
1.该架构暂时能满足当前应用，但功能应具有可扩展性；


二、改进目标：
1.完成基于会议纪要模板的文件内容提取、文件生成，基于langgraph开发，具备human in the loop过程，对生成过程中存在的：模板文件选择、缺失信息补全、提取信息确认环节进行确认；
2.长期目标，除报告生成外，增加报告审查，信息查询，表格制作等功能，实现多智能体协同；


三、具体改进需求：
数据结构改进：
1.模板数据要求，模板数据可以参考C:\scripts\agent\report-gen\sample_report\数字科技中心会议纪要\4.template_数字科技中心会议纪要模板.docx，在模板数据中，具有占位符"{{placeholder}}"，比如{{attend.person}}，占位符可以嵌套，表达内容除了字段名称外，更包含了字段的样式，在生成报告的时候，必须通过打开模板->修改，填充内容->另存为文件的方式完成，这样才能保证生成的成果文件严格遵循了模板样式，减少修改工作量。
2.关键信息定义要求，关键信息定义可参考C:\scripts\agent\report-gen\sample_report\数字科技中心会议纪要\2.key_definitions.md，在关键信息文件中，第一行代表该文件名称，整个文件按markdown格式存储，根据层次，存在嵌套关系，关键信息定义和模板数据一一对应，是模板数据的延伸补充，具体字段定义如下：
description:对该字段数据的描述，用于嵌入到prompt中，进行数据提取；
example:该字段的实际例子，用于嵌入到prompt中，进行数据提取；
format:字段格式，包括：普通文本、表格行、段落、人名、单独行等，即可用于数据提取，又可用于结果文本组合；
default:默认值，若提取不到，则用默认值
length:字段一般长度
source:字段来源，包括：上传内容、系统时间、既有知识、外部搜索四类，其中，上传内容指的是，通过本次在对话框中上传的内容进行提取；系统时间指的是当前的计算机系统时间；既有知识指的是，从当前的知识库中提取的知识；外部搜索，通过浏览器，搜索引擎等方式，搜索得到的知识
must:是否必须，如果是必须的文本，提取不到，则需要要求用户确认是否为空，或者手动补充
3.提取数据要求，提取数据可参考C:\scripts\agent\report-gen\sample_report\数字科技中心会议纪要\3.extracted_keys.md，提取的数据，需要按照层级关系，组织成树形结构，便于后续的报告生成。
4.提取结果数据要求，可参考C:\scripts\agent\report-gen\sample_report\数字科技中心会议纪要\5.output_最终生成效果.docx，结果数据必须保持模板文件的样式。
4.存储要求，要求利用数据库存储文件类的数据，可自行根据数据要求，设计数据库表结构。

智能体设计：
1.智能体基于langgraph开发，智能体运作方式为"意图识别智能体"->"任务智能体"结构，通过意图识别智能体，判断用户输入意图后，将任务交付到"任务智能体"进行执行，平台需要预留接口，可以加入多个"任务智能体"；
2.每个智能体的定义通过prompt文件完成，所有关于智能体的文件，统一在C:\scripts\agent\report-gen\backend\agents目录下统一管理，不同类型的文件建立不同的文件夹，如prompt文件夹存储不同智能体的prompt文件；
3.不同的"任务智能体"之间共用一组工具，工具的定义在C:\scripts\agent\report-gen\backend\agents\tools目录下，工具的输入输出，需要进行规范化定义，便于智能体的调用。
4.目前需要实现的智能体包括"意图识别智能体"，"报告生成智能体"，其中"意图识别智能体"是基础智能体，其他智能体都是在"意图识别智能体"的调度下进行执行。
5.意图识别智能体的设计与交互逻辑设计可参考C:\scripts\agent\report-gen\update\V0.1\intention_identification.md
6.报告生成智能体的设计与交互逻辑设计可参考C:\scripts\agent\report-gen\update\V0.1\report_generation.md
7.现阶段需要实现的工具可参考C:\scripts\agent\report-gen\update\V0.1\tools.md


界面改进要求：
1.重构界面，形成简单的界面风格，从左到右，分别为导航栏、历史对话栏、主操作界面；
2.导航栏：
2.1 从顶到底，分别为登录状态、标签页、系统设置栏
2.1.1 登录状态：显示当前登录用户名，用户名右侧有注销字样
2.1.2 标签页：分别标注主界面、模板管理、数据管理、结果管理，用于切换不同的操作界面
2.1.3 系统设置栏：用户管理、系统设置、UI选择
3.历史对话栏：
3.1 从顶到底，分别为历史对话记录，用于显示当前用户的历史对话记录
4.主操作界面：
4.1 从顶到底，分别为结果交互区、功能沟通区
4.1.1 结果交互区：用户反馈大模型处理过程，内容，结果，提供选择；
4.1.2 功能沟通区：用户输入不同的命令，完成大部分该产品功能的实现，增加上传按钮，可上传需要处理的文件，右侧具有输入按钮，将输入内容提交至平台


