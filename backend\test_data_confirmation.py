#!/usr/bin/env python3
"""
测试数据确认工作流
"""

import requests
import json
import tempfile
import os

BASE_URL = "http://localhost:8000"

def create_test_file():
    """创建测试文件"""
    content = """
会议纪要

时间：2024年7月25日 14:00-15:30
地点：会议室A
主持人：张三
参会人员：李四、王五、赵六

会议议题：
1. 项目进度汇报
2. 下阶段工作安排
3. 资源配置讨论

会议内容：
1. 项目进度汇报
   - 当前完成度：80%
   - 主要成果：完成了核心功能开发
   - 遇到的问题：测试环境配置有待优化

2. 下阶段工作安排
   - 完成剩余功能开发
   - 进行全面测试
   - 准备上线部署

3. 资源配置讨论
   - 需要增加2名测试人员
   - 服务器资源需要扩容

决议事项：
1. 下周完成功能开发
2. 申请增加测试人员
3. 联系运维部门扩容服务器

下次会议时间：2024年8月1日 14:00
"""
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(content)
        return f.name

def test_complete_data_extraction_workflow():
    """测试完整的数据提取和确认工作流"""
    print("🧪 测试完整的数据提取和确认工作流")
    print("=" * 60)
    
    # 创建测试文件
    test_file_path = create_test_file()
    
    try:
        # 第一步：启动工作流（带文件上传）
        print("1. 启动工作流（带文件上传）...")
        
        with open(test_file_path, 'rb') as f:
            files = {'files': ('meeting_notes.txt', f, 'text/plain')}
            data = {'text_input': '请根据上传的文档生成会议纪要'}
            
            response = requests.post(
                f"{BASE_URL}/api/workflow/langgraph/start",
                files=files,
                data=data,
                timeout=30
            )
        
        print(f"启动响应状态码: {response.status_code}")
        
        if response.status_code != 200:
            print(f"启动失败: {response.text}")
            return
            
        result = response.json()
        workflow_id = result.get('workflow_id')
        print(f"工作流ID: {workflow_id}")
        
        # 检查是否需要模板确认
        requires_action = result.get('data', {}).get('requires_user_action', False)
        print(f"需要模板确认: {requires_action}")
        
        if requires_action:
            print("\n2. 模拟用户确认推荐模板...")
            
            # 第二步：确认模板
            resume_data = {
                "action": "confirm"
            }
            
            resume_response = requests.post(
                f"{BASE_URL}/api/workflow/langgraph/resume/{workflow_id}",
                json=resume_data,
                timeout=30
            )
            
            print(f"模板确认响应状态码: {resume_response.status_code}")
            
            if resume_response.status_code == 200:
                resume_result = resume_response.json()
                print("✅ 模板确认成功")
                
                # 检查是否需要数据确认
                requires_data_action = resume_result.get('data', {}).get('requires_user_action', False)
                print(f"需要数据确认: {requires_data_action}")
                
                if requires_data_action:
                    print("\n3. 检查提取的数据...")
                    
                    # 获取最终状态
                    final_state = resume_result.get('data', {}).get('final_state', {})
                    extraction_results = final_state.get('extraction_results', [])
                    
                    print(f"提取了 {len(extraction_results)} 个字段:")
                    for i, result in enumerate(extraction_results[:5]):  # 只显示前5个
                        field_name = result.get('field_name', '')
                        field_value = result.get('field_value', '')
                        confidence = result.get('confidence', 0)
                        needs_input = result.get('needs_human_input', False)
                        
                        status = "❌ 需要补充" if needs_input else "✅ 已提取"
                        print(f"  {i+1}. {field_name}: {field_value[:50]}{'...' if len(field_value) > 50 else ''}")
                        print(f"     {status} (置信度: {confidence:.2f})")
                    
                    if len(extraction_results) > 5:
                        print(f"  ... 还有 {len(extraction_results) - 5} 个字段")
                    
                    print("\n4. 模拟用户确认数据...")
                    
                    # 第三步：确认数据
                    data_confirm_data = {
                        "action": "confirm"
                    }
                    
                    data_confirm_response = requests.post(
                        f"{BASE_URL}/api/workflow/langgraph/resume/{workflow_id}",
                        json=data_confirm_data,
                        timeout=30
                    )
                    
                    print(f"数据确认响应状态码: {data_confirm_response.status_code}")
                    
                    if data_confirm_response.status_code == 200:
                        data_confirm_result = data_confirm_response.json()
                        print("✅ 数据确认成功")
                        
                        # 检查最终状态
                        final_state = data_confirm_result.get('data', {}).get('final_state', {})
                        current_stage = final_state.get('current_stage', 'unknown')
                        print(f"当前阶段: {current_stage}")
                        
                        # 检查是否还需要进一步操作
                        requires_more_action = data_confirm_result.get('data', {}).get('requires_user_action', False)
                        if requires_more_action:
                            print("⚠️ 需要进一步的用户操作")
                        else:
                            print("✅ 工作流完成，可以进入下一阶段")
                    else:
                        print(f"数据确认失败: {data_confirm_response.text}")
                else:
                    print("⚠️ 没有要求数据确认")
            else:
                print(f"模板确认失败: {resume_response.text}")
        else:
            print("⚠️ 工作流没有要求模板确认")
            
    except Exception as e:
        print(f"测试异常: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        if os.path.exists(test_file_path):
            os.unlink(test_file_path)

if __name__ == "__main__":
    test_complete_data_extraction_workflow()
