"""
模板验证服务
负责模板与关键字段定义的同步验证、占位符检测和一致性检查
"""

import os
import re
import json
import logging
from typing import Dict, List, Optional, Set, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from docx import Document
from pathlib import Path

logger = logging.getLogger(__name__)


class ValidationLevel(Enum):
    """验证级别"""
    ERROR = "error"      # 错误，必须修复
    WARNING = "warning"  # 警告，建议修复
    INFO = "info"       # 信息，可选修复


@dataclass
class ValidationIssue:
    """验证问题"""
    level: ValidationLevel
    type: str
    message: str
    location: Optional[str] = None
    suggestion: Optional[str] = None
    field_name: Optional[str] = None


@dataclass
class PlaceholderInfo:
    """占位符信息"""
    placeholder_text: str
    field_name: str
    pattern_type: str  # 模式类型: {{field}}, {field}, [field], <field>
    location: str      # 在文档中的位置
    context: str       # 上下文
    line_number: Optional[int] = None


@dataclass
class FieldDefinition:
    """字段定义"""
    field_name: str
    description: str
    example: str
    format: str
    default: str
    length: Optional[int]
    source: str
    must: bool
    
    # 从key_definitions.md解析的额外信息
    raw_content: str = ""
    line_start: int = 0
    line_end: int = 0


@dataclass
class TemplateValidationResult:
    """模板验证结果"""
    is_valid: bool
    issues: List[ValidationIssue]
    placeholders: List[PlaceholderInfo]
    field_definitions: List[FieldDefinition]
    
    # 统计信息
    total_placeholders: int
    matched_placeholders: int
    unmatched_placeholders: int
    missing_definitions: int
    unused_definitions: int
    
    # 匹配关系
    placeholder_to_definition: Dict[str, str]  # placeholder -> field_name
    definition_to_placeholder: Dict[str, List[str]]  # field_name -> [placeholders]
    
    # 质量评分
    consistency_score: float  # 一致性评分 (0-1)
    completeness_score: float  # 完整性评分 (0-1)
    overall_score: float      # 总体评分 (0-1)


class TemplateValidationService:
    """模板验证服务"""
    
    def __init__(self):
        # 支持的占位符模式
        self.placeholder_patterns = {
            'double_brace': r'\{\{([^}]+)\}\}',  # {{field_name}}
            'single_brace': r'\{([^}]+)\}',      # {field_name}
            'square_bracket': r'\[([^\]]+)\]',   # [field_name]
            'angle_bracket': r'<([^>]+)>',       # <field_name>
        }
        
        # 字段定义解析模式
        self.field_definition_pattern = r'^# ([^\n]+)\n((?:^[^#\n].*\n?)*)'
        
        # 字段属性模式
        self.field_attribute_patterns = {
            'description': r'^description[:：](.+)$',
            'example': r'^example[:：](.+)$',
            'format': r'^format[:：](.+)$',
            'default': r'^default[:：](.+)$',
            'length': r'^length[:：]\s*(\d+)$',
            'source': r'^source[:：](.+)$',
            'must': r'^must[:：]\s*(true|false)$',
        }
    
    async def validate_template_consistency(self, template_path: str, 
                                          key_definitions_path: str) -> TemplateValidationResult:
        """
        验证模板与关键字段定义的一致性
        
        Args:
            template_path: 模板文件路径
            key_definitions_path: 关键字段定义文件路径
            
        Returns:
            TemplateValidationResult: 验证结果
        """
        try:
            # 提取模板占位符
            placeholders = await self._extract_template_placeholders(template_path)
            
            # 解析字段定义
            field_definitions = await self._parse_key_definitions(key_definitions_path)
            
            # 执行一致性检查
            validation_result = await self._perform_consistency_check(
                placeholders, field_definitions, template_path, key_definitions_path
            )
            
            logger.info(f"模板验证完成: {template_path}")
            return validation_result
            
        except Exception as e:
            logger.error(f"模板验证失败: {e}")
            return TemplateValidationResult(
                is_valid=False,
                issues=[ValidationIssue(
                    level=ValidationLevel.ERROR,
                    type="validation_error",
                    message=f"验证过程异常: {str(e)}"
                )],
                placeholders=[],
                field_definitions=[],
                total_placeholders=0,
                matched_placeholders=0,
                unmatched_placeholders=0,
                missing_definitions=0,
                unused_definitions=0,
                placeholder_to_definition={},
                definition_to_placeholder={},
                consistency_score=0.0,
                completeness_score=0.0,
                overall_score=0.0
            )
    
    async def _extract_template_placeholders(self, template_path: str) -> List[PlaceholderInfo]:
        """提取模板占位符"""
        placeholders = []
        
        try:
            if not os.path.exists(template_path):
                raise FileNotFoundError(f"模板文件不存在: {template_path}")
            
            # 检查文件扩展名
            file_ext = Path(template_path).suffix.lower()
            
            if file_ext == '.docx':
                placeholders = await self._extract_from_docx(template_path)
            elif file_ext == '.txt' or file_ext == '.md':
                placeholders = await self._extract_from_text(template_path)
            else:
                logger.warning(f"不支持的模板文件格式: {file_ext}")
            
            logger.info(f"从模板中提取到 {len(placeholders)} 个占位符")
            return placeholders
            
        except Exception as e:
            logger.error(f"占位符提取失败: {e}")
            return []
    
    async def _extract_from_docx(self, docx_path: str) -> List[PlaceholderInfo]:
        """从DOCX文件提取占位符"""
        placeholders = []
        
        try:
            doc = Document(docx_path)
            
            # 提取段落中的占位符
            for para_idx, paragraph in enumerate(doc.paragraphs):
                if paragraph.text.strip():
                    para_placeholders = self._find_placeholders_in_text(
                        paragraph.text,
                        f"段落_{para_idx + 1}",
                        paragraph.text[:100] + "..." if len(paragraph.text) > 100 else paragraph.text
                    )
                    placeholders.extend(para_placeholders)
            
            # 提取表格中的占位符
            for table_idx, table in enumerate(doc.tables):
                for row_idx, row in enumerate(table.rows):
                    for cell_idx, cell in enumerate(row.cells):
                        if cell.text.strip():
                            cell_placeholders = self._find_placeholders_in_text(
                                cell.text,
                                f"表格_{table_idx + 1}_行_{row_idx + 1}_列_{cell_idx + 1}",
                                cell.text
                            )
                            placeholders.extend(cell_placeholders)
            
            return placeholders
            
        except Exception as e:
            logger.error(f"DOCX占位符提取失败: {e}")
            return []
    
    async def _extract_from_text(self, text_path: str) -> List[PlaceholderInfo]:
        """从文本文件提取占位符"""
        placeholders = []
        
        try:
            with open(text_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                if line.strip():
                    line_placeholders = self._find_placeholders_in_text(
                        line,
                        f"行_{line_num}",
                        line.strip(),
                        line_num
                    )
                    placeholders.extend(line_placeholders)
            
            return placeholders
            
        except Exception as e:
            logger.error(f"文本占位符提取失败: {e}")
            return []
    
    def _find_placeholders_in_text(self, text: str, location: str, 
                                 context: str, line_number: Optional[int] = None) -> List[PlaceholderInfo]:
        """在文本中查找占位符"""
        placeholders = []
        
        for pattern_name, pattern in self.placeholder_patterns.items():
            matches = re.finditer(pattern, text)
            for match in matches:
                placeholder_text = match.group(0)
                field_name = match.group(1).strip()
                
                # 清理字段名（移除多余的空格和特殊字符）
                field_name = re.sub(r'[^\w\u4e00-\u9fff_]', '', field_name)
                
                if field_name:  # 确保字段名不为空
                    placeholder_info = PlaceholderInfo(
                        placeholder_text=placeholder_text,
                        field_name=field_name,
                        pattern_type=pattern_name,
                        location=location,
                        context=context,
                        line_number=line_number
                    )
                    placeholders.append(placeholder_info)
        
        return placeholders
    
    async def _parse_key_definitions(self, key_definitions_path: str) -> List[FieldDefinition]:
        """解析关键字段定义文件"""
        field_definitions = []
        
        try:
            if not os.path.exists(key_definitions_path):
                logger.warning(f"关键字段定义文件不存在: {key_definitions_path}")
                return []
            
            with open(key_definitions_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 按字段分割内容
            field_sections = re.split(r'\n# ', content)
            
            for section_idx, section in enumerate(field_sections):
                if section.strip():
                    # 确保以#开头
                    if not section.startswith('#'):
                        section = '# ' + section
                    
                    field_definition = self._parse_field_section(section, section_idx)
                    if field_definition:
                        field_definitions.append(field_definition)
            
            logger.info(f"解析到 {len(field_definitions)} 个字段定义")
            return field_definitions
            
        except Exception as e:
            logger.error(f"字段定义解析失败: {e}")
            return []
    
    def _parse_field_section(self, section: str, section_idx: int) -> Optional[FieldDefinition]:
        """解析单个字段定义段落"""
        try:
            lines = section.strip().split('\n')
            if not lines:
                return None
            
            # 提取字段名（第一行，去掉#号）
            field_name = lines[0].replace('#', '').strip()
            if not field_name:
                return None
            
            # 解析字段属性
            attributes = {
                'description': '',
                'example': '',
                'format': '普通文本',
                'default': '',
                'length': None,
                'source': '上传内容',
                'must': False
            }
            
            for line in lines[1:]:
                line = line.strip()
                if not line:
                    continue
                
                # 尝试匹配各个属性
                for attr_name, pattern in self.field_attribute_patterns.items():
                    match = re.match(pattern, line, re.IGNORECASE)
                    if match:
                        value = match.group(1).strip()
                        
                        if attr_name == 'length':
                            try:
                                attributes[attr_name] = int(value)
                            except ValueError:
                                attributes[attr_name] = None
                        elif attr_name == 'must':
                            attributes[attr_name] = value.lower() == 'true'
                        else:
                            attributes[attr_name] = value
                        break
            
            return FieldDefinition(
                field_name=field_name,
                description=attributes['description'],
                example=attributes['example'],
                format=attributes['format'],
                default=attributes['default'],
                length=attributes['length'],
                source=attributes['source'],
                must=attributes['must'],
                raw_content=section,
                line_start=section_idx * 10,  # 估算行号
                line_end=(section_idx + 1) * 10
            )
            
        except Exception as e:
            logger.error(f"字段段落解析失败: {e}")
            return None
    
    async def _perform_consistency_check(self, placeholders: List[PlaceholderInfo],
                                       field_definitions: List[FieldDefinition],
                                       template_path: str,
                                       key_definitions_path: str) -> TemplateValidationResult:
        """执行一致性检查"""
        issues = []
        placeholder_to_definition = {}
        definition_to_placeholder = {}
        
        # 创建字段名到定义的映射
        definition_map = {fd.field_name: fd for fd in field_definitions}
        
        # 初始化定义到占位符的映射
        for fd in field_definitions:
            definition_to_placeholder[fd.field_name] = []
        
        matched_count = 0
        
        # 检查每个占位符是否有对应的字段定义
        for placeholder in placeholders:
            field_name = placeholder.field_name
            matched_definition = None
            
            # 精确匹配
            if field_name in definition_map:
                matched_definition = definition_map[field_name]
            else:
                # 模糊匹配
                for def_name, definition in definition_map.items():
                    if self._is_field_match(field_name, def_name):
                        matched_definition = definition
                        break
            
            if matched_definition:
                placeholder_to_definition[placeholder.placeholder_text] = matched_definition.field_name
                definition_to_placeholder[matched_definition.field_name].append(placeholder.placeholder_text)
                matched_count += 1
            else:
                # 未找到匹配的字段定义
                issues.append(ValidationIssue(
                    level=ValidationLevel.ERROR,
                    type="unmatched_placeholder",
                    message=f"占位符 '{placeholder.placeholder_text}' 没有对应的字段定义",
                    location=placeholder.location,
                    suggestion=f"请在 {key_definitions_path} 中添加 '{field_name}' 的字段定义",
                    field_name=field_name
                ))
        
        # 检查未使用的字段定义
        unused_definitions = 0
        for field_name, definition in definition_map.items():
            if field_name not in definition_to_placeholder or not definition_to_placeholder[field_name]:
                unused_definitions += 1
                level = ValidationLevel.ERROR if definition.must else ValidationLevel.WARNING
                issues.append(ValidationIssue(
                    level=level,
                    type="unused_definition",
                    message=f"字段定义 '{field_name}' 在模板中没有对应的占位符",
                    suggestion=f"请在 {template_path} 中添加 {{{{{field_name}}}}} 占位符或删除该字段定义",
                    field_name=field_name
                ))
        
        # 检查必填字段
        for field_name, definition in definition_map.items():
            if definition.must and (field_name not in definition_to_placeholder or 
                                  not definition_to_placeholder[field_name]):
                issues.append(ValidationIssue(
                    level=ValidationLevel.ERROR,
                    type="missing_required_placeholder",
                    message=f"必填字段 '{field_name}' 在模板中缺少占位符",
                    suggestion=f"请在模板中添加 {{{{{field_name}}}}} 占位符",
                    field_name=field_name
                ))
        
        # 检查占位符格式一致性
        self._check_placeholder_format_consistency(placeholders, issues)
        
        # 计算评分
        total_placeholders = len(placeholders)
        unmatched_placeholders = total_placeholders - matched_count
        missing_definitions = unmatched_placeholders
        
        consistency_score = matched_count / total_placeholders if total_placeholders > 0 else 1.0
        completeness_score = 1.0 - (unused_definitions / len(field_definitions)) if field_definitions else 1.0
        
        # 根据问题严重程度调整评分
        error_count = len([issue for issue in issues if issue.level == ValidationLevel.ERROR])
        warning_count = len([issue for issue in issues if issue.level == ValidationLevel.WARNING])
        
        penalty = (error_count * 0.2) + (warning_count * 0.1)
        overall_score = max(0.0, (consistency_score + completeness_score) / 2 - penalty)
        
        # 判断是否通过验证
        is_valid = error_count == 0 and consistency_score >= 0.8 and completeness_score >= 0.7
        
        return TemplateValidationResult(
            is_valid=is_valid,
            issues=issues,
            placeholders=placeholders,
            field_definitions=field_definitions,
            total_placeholders=total_placeholders,
            matched_placeholders=matched_count,
            unmatched_placeholders=unmatched_placeholders,
            missing_definitions=missing_definitions,
            unused_definitions=unused_definitions,
            placeholder_to_definition=placeholder_to_definition,
            definition_to_placeholder=definition_to_placeholder,
            consistency_score=round(consistency_score, 3),
            completeness_score=round(completeness_score, 3),
            overall_score=round(overall_score, 3)
        )
    
    def _is_field_match(self, placeholder_name: str, definition_name: str) -> bool:
        """判断占位符名称与字段定义名称是否匹配"""
        # 规范化名称（移除下划线、转小写）
        p_name = placeholder_name.replace('_', '').lower()
        d_name = definition_name.replace('_', '').lower()
        
        # 精确匹配
        if p_name == d_name:
            return True
        
        # 包含匹配
        if p_name in d_name or d_name in p_name:
            return True
        
        # 检查是否是常见的同义词
        synonyms = {
            'time': ['时间', 'datetime', 'date'],
            'title': ['标题', 'name', 'subject'],
            'host': ['主持人', 'moderator', 'chair'],
            'location': ['地点', 'place', 'venue'],
            'attendees': ['参会人员', 'participants', 'members']
        }
        
        for english, chinese_list in synonyms.items():
            if (english in p_name and any(c in d_name for c in chinese_list)) or \
               (english in d_name and any(c in p_name for c in chinese_list)):
                return True
        
        return False
    
    def _check_placeholder_format_consistency(self, placeholders: List[PlaceholderInfo], 
                                            issues: List[ValidationIssue]) -> None:
        """检查占位符格式一致性"""
        pattern_counts = {}
        
        for placeholder in placeholders:
            pattern_type = placeholder.pattern_type
            pattern_counts[pattern_type] = pattern_counts.get(pattern_type, 0) + 1
        
        if len(pattern_counts) > 1:
            # 存在多种格式
            most_common_pattern = max(pattern_counts, key=pattern_counts.get)
            
            for placeholder in placeholders:
                if placeholder.pattern_type != most_common_pattern:
                    issues.append(ValidationIssue(
                        level=ValidationLevel.WARNING,
                        type="inconsistent_placeholder_format",
                        message=f"占位符格式不一致: '{placeholder.placeholder_text}' 使用了 {placeholder.pattern_type} 格式",
                        location=placeholder.location,
                        suggestion=f"建议统一使用 {most_common_pattern} 格式",
                        field_name=placeholder.field_name
                    ))
    
    async def suggest_template_improvements(self, validation_result: TemplateValidationResult) -> List[str]:
        """建议模板改进方案"""
        suggestions = []
        
        if not validation_result.is_valid:
            suggestions.append("模板验证未通过，请根据以下问题进行修复：")
        
        # 根据问题类型分组建议
        error_issues = [issue for issue in validation_result.issues if issue.level == ValidationLevel.ERROR]
        warning_issues = [issue for issue in validation_result.issues if issue.level == ValidationLevel.WARNING]
        
        if error_issues:
            suggestions.append(f"🔴 发现 {len(error_issues)} 个严重问题：")
            for issue in error_issues[:5]:  # 最多显示5个
                if issue.suggestion:
                    suggestions.append(f"  • {issue.suggestion}")
        
        if warning_issues:
            suggestions.append(f"🟡 发现 {len(warning_issues)} 个警告问题：")
            for issue in warning_issues[:3]:  # 最多显示3个
                if issue.suggestion:
                    suggestions.append(f"  • {issue.suggestion}")
        
        # 评分建议
        if validation_result.consistency_score < 0.8:
            suggestions.append("📊 一致性评分较低，建议检查占位符与字段定义的匹配关系")
        
        if validation_result.completeness_score < 0.7:
            suggestions.append("📊 完整性评分较低，建议清理未使用的字段定义")
        
        # 通用改进建议
        if validation_result.overall_score < 0.9:
            suggestions.extend([
                "💡 改进建议：",
                "  • 使用统一的占位符格式（推荐 {{field_name}}）",
                "  • 确保所有必填字段都有对应的占位符",
                "  • 定期清理未使用的字段定义",
                "  • 使用描述性的字段名称"
            ])
        
        return suggestions
    
    def get_validation_summary(self, validation_result: TemplateValidationResult) -> Dict[str, Any]:
        """获取验证摘要信息"""
        return {
            "validation_status": "通过" if validation_result.is_valid else "未通过",
            "overall_score": validation_result.overall_score,
            "consistency_score": validation_result.consistency_score,
            "completeness_score": validation_result.completeness_score,
            "statistics": {
                "total_placeholders": validation_result.total_placeholders,
                "matched_placeholders": validation_result.matched_placeholders,
                "unmatched_placeholders": validation_result.unmatched_placeholders,
                "total_definitions": len(validation_result.field_definitions),
                "unused_definitions": validation_result.unused_definitions,
                "required_fields": len([fd for fd in validation_result.field_definitions if fd.must])
            },
            "issues_summary": {
                "errors": len([issue for issue in validation_result.issues if issue.level == ValidationLevel.ERROR]),
                "warnings": len([issue for issue in validation_result.issues if issue.level == ValidationLevel.WARNING]),
                "info": len([issue for issue in validation_result.issues if issue.level == ValidationLevel.INFO])
            }
        }


# 全局模板验证服务实例
template_validation_service = TemplateValidationService()