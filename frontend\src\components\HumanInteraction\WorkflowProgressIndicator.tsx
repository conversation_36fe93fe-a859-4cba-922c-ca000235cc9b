/**
 * 工作流进度指示器
 * 可视化显示多阶段处理流程的进度状态
 */

import React from 'react';
import {
  Steps,
  Card,
  Typography,
  Progress,
  Tag,
  Space,
  Timeline,
  Statistic,
  Row,
  Col,
  Alert,
  Spin,
} from 'antd';
import {
  FileTextOutlined,
  RobotOutlined,
  CheckCircleOutlined,
  EditOutlined,
  FileMarkdownOutlined,
  ClockCircleOutlined,
  LoadingOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

// 工作流阶段枚举
export enum WorkflowStage {
  DOCUMENT_UPLOAD = 'document_upload',
  DOCUMENT_ANALYSIS = 'document_analysis', 
  FIELD_EXTRACTION = 'field_extraction',
  HUMAN_VALIDATION = 'human_validation',
  REPORT_GENERATION = 'report_generation',
  COMPLETED = 'completed'
}

// 阶段状态枚举
export enum StageStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  ERROR = 'error',
  SKIPPED = 'skipped'
}

// 工作流阶段信息
export interface WorkflowStageInfo {
  stage: WorkflowStage;
  title: string;
  description: string;
  status: StageStatus;
  start_time?: Date;
  end_time?: Date;
  duration?: number; // 秒
  progress?: number; // 0-100
  sub_tasks?: SubTask[];
  error_message?: string;
  metrics?: Record<string, any>;
}

// 子任务信息
export interface SubTask {
  name: string;
  status: StageStatus;
  progress?: number;
  description?: string;
}

// 工作流进度数据
export interface WorkflowProgress {
  workflow_id: string;
  document_name: string;
  current_stage: WorkflowStage;
  overall_progress: number; // 0-100
  total_duration: number; // 秒
  estimated_remaining: number; // 秒
  stages: WorkflowStageInfo[];
  is_paused?: boolean;
  pause_reason?: string;
}

interface WorkflowProgressIndicatorProps {
  progress: WorkflowProgress;
  showDetailedView?: boolean;
  compact?: boolean;
  onStageClick?: (stage: WorkflowStage) => void;
}

const WorkflowProgressIndicator: React.FC<WorkflowProgressIndicatorProps> = ({
  progress,
  showDetailedView = false,
  compact = false,
  onStageClick
}) => {
  
  // 获取阶段状态对应的颜色
  const getStatusColor = (status: StageStatus): string => {
    switch (status) {
      case StageStatus.COMPLETED: return '#52c41a';
      case StageStatus.IN_PROGRESS: return '#1890ff';
      case StageStatus.ERROR: return '#f5222d';
      case StageStatus.SKIPPED: return '#faad14';
      default: return '#d9d9d9';
    }
  };

  // 获取阶段状态对应的图标
  const getStatusIcon = (status: StageStatus) => {
    switch (status) {
      case StageStatus.COMPLETED: return <CheckCircleOutlined />;
      case StageStatus.IN_PROGRESS: return <LoadingOutlined spin />;
      case StageStatus.ERROR: return <ExclamationCircleOutlined />;
      default: return <ClockCircleOutlined />;
    }
  };

  // 获取阶段图标
  const getStageIcon = (stage: WorkflowStage) => {
    switch (stage) {
      case WorkflowStage.DOCUMENT_UPLOAD: return <FileTextOutlined />;
      case WorkflowStage.DOCUMENT_ANALYSIS: return <RobotOutlined />;
      case WorkflowStage.FIELD_EXTRACTION: return <RobotOutlined />;
      case WorkflowStage.HUMAN_VALIDATION: return <EditOutlined />;
      case WorkflowStage.REPORT_GENERATION: return <FileMarkdownOutlined />;
      case WorkflowStage.COMPLETED: return <CheckCircleOutlined />;
      default: return <ClockCircleOutlined />;
    }
  };

  // 获取Steps组件的状态
  const getStepStatus = (stage: WorkflowStageInfo, index: number) => {
    const currentStageIndex = progress.stages.findIndex(s => s.stage === progress.current_stage);
    
    if (stage.status === StageStatus.ERROR) return 'error';
    if (stage.status === StageStatus.COMPLETED) return 'finish';
    if (index === currentStageIndex && stage.status === StageStatus.IN_PROGRESS) return 'process';
    if (index < currentStageIndex) return 'finish';
    return 'wait';
  };

  // 格式化持续时间
  const formatDuration = (seconds: number): string => {
    if (seconds < 60) return `${seconds.toFixed(0)}秒`;
    if (seconds < 3600) return `${(seconds / 60).toFixed(1)}分钟`;
    return `${(seconds / 3600).toFixed(1)}小时`;
  };

  // 获取当前阶段信息
  const currentStageInfo = progress.stages.find(s => s.stage === progress.current_stage);

  if (compact) {
    // 简化视图
    return (
      <Card size="small">
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Space>
            <Spin spinning={currentStageInfo?.status === StageStatus.IN_PROGRESS} size="small" />
            <Text strong>{currentStageInfo?.title || '处理中'}</Text>
            <Tag color={getStatusColor(currentStageInfo?.status || StageStatus.PENDING)}>
              {progress.overall_progress.toFixed(0)}%
            </Tag>
          </Space>
          <Progress 
            percent={progress.overall_progress} 
            size="small" 
            style={{ minWidth: '100px' }}
            status={currentStageInfo?.status === StageStatus.ERROR ? 'exception' : 'active'}
          />
        </div>
      </Card>
    );
  }

  return (
    <div>
      {/* 总体进度概览 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]} align="middle">
          <Col span={12}>
            <Title level={4} style={{ margin: 0 }}>
              📄 {progress.document_name}
            </Title>
            <Text type="secondary">工作流ID: {progress.workflow_id}</Text>
          </Col>
          <Col span={12}>
            <Row gutter={16}>
              <Col span={8}>
                <Statistic
                  title="总体进度"
                  value={progress.overall_progress}
                  suffix="%"
                  valueStyle={{ color: progress.overall_progress === 100 ? '#52c41a' : '#1890ff' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="已用时间"
                  value={formatDuration(progress.total_duration)}
                  valueStyle={{ fontSize: '16px' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="预计剩余"
                  value={formatDuration(progress.estimated_remaining)}
                  valueStyle={{ fontSize: '16px', color: '#faad14' }}
                />
              </Col>
            </Row>
          </Col>
        </Row>

        {/* 整体进度条 */}
        <Progress
          percent={progress.overall_progress}
          status={currentStageInfo?.status === StageStatus.ERROR ? 'exception' : 'active'}
          strokeColor={{
            '0%': '#108ee9',
            '100%': '#87d068',
          }}
          style={{ marginTop: '16px' }}
        />

        {/* 暂停提示 */}
        {progress.is_paused && (
          <Alert
            message="工作流已暂停"
            description={progress.pause_reason || '等待人工验证'}
            type="warning"
            showIcon
            style={{ marginTop: '16px' }}
          />
        )}
      </Card>

      {/* 阶段步骤 */}
      <Card title="处理进度" style={{ marginBottom: showDetailedView ? '16px' : '0' }}>
        <Steps
          current={progress.stages.findIndex(s => s.stage === progress.current_stage)}
          size="small"
          direction="horizontal"
        >
          {progress.stages.map((stage, index) => (
            <Step
              key={stage.stage}
              title={stage.title}
              description={stage.description}
              status={getStepStatus(stage, index)}
              icon={getStageIcon(stage.stage)}
              onClick={() => onStageClick?.(stage.stage)}
              style={{ cursor: onStageClick ? 'pointer' : 'default' }}
            />
          ))}
        </Steps>
      </Card>

      {/* 详细视图 */}
      {showDetailedView && (
        <Card title="详细进度">
          <Timeline>
            {progress.stages.map((stage, index) => {
              const isActive = stage.stage === progress.current_stage;
              const isCompleted = stage.status === StageStatus.COMPLETED;
              
              return (
                <Timeline.Item
                  key={stage.stage}
                  dot={getStatusIcon(stage.status)}
                  color={getStatusColor(stage.status)}
                >
                  <div>
                    <Title level={5} style={{ margin: '0 0 8px 0' }}>
                      {getStageIcon(stage.stage)} {stage.title}
                    </Title>
                    
                    <Paragraph style={{ marginBottom: '8px' }}>
                      {stage.description}
                    </Paragraph>

                    {/* 阶段进度 */}
                    {(isActive || stage.progress !== undefined) && stage.progress !== undefined && (
                      <Progress 
                        percent={stage.progress} 
                        size="small" 
                        style={{ marginBottom: '8px' }}
                        status={stage.status === StageStatus.ERROR ? 'exception' : 'active'}
                      />
                    )}

                    {/* 时间信息 */}
                    <Space style={{ marginBottom: '8px' }}>
                      {stage.start_time && (
                        <Text type="secondary">
                          开始: {stage.start_time.toLocaleTimeString()}
                        </Text>
                      )}
                      {stage.end_time && (
                        <Text type="secondary">
                          完成: {stage.end_time.toLocaleTimeString()}
                        </Text>
                      )}
                      {stage.duration && (
                        <Text type="secondary">
                          耗时: {formatDuration(stage.duration)}
                        </Text>
                      )}
                    </Space>

                    {/* 子任务 */}
                    {stage.sub_tasks && stage.sub_tasks.length > 0 && (
                      <div style={{ marginLeft: '16px', marginBottom: '8px' }}>
                        <Text strong>子任务:</Text>
                        {stage.sub_tasks.map((task, taskIndex) => (
                          <div key={taskIndex} style={{ marginTop: '4px' }}>
                            <Space>
                              {getStatusIcon(task.status)}
                              <Text>{task.name}</Text>
                              {task.progress !== undefined && (
                                <Progress 
                                  percent={task.progress} 
                                  size="small" 
                                  style={{ width: '100px' }}
                                />
                              )}
                            </Space>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* 错误信息 */}
                    {stage.error_message && (
                      <Alert
                        message="错误信息"
                        description={stage.error_message}
                        type="error"
                        size="small"
                        style={{ marginTop: '8px' }}
                      />
                    )}

                    {/* 阶段指标 */}
                    {stage.metrics && Object.keys(stage.metrics).length > 0 && (
                      <div style={{ marginTop: '8px' }}>
                        <Text strong>指标数据:</Text>
                        <div style={{ marginTop: '4px' }}>
                          {Object.entries(stage.metrics).map(([key, value]) => (
                            <Tag key={key} style={{ marginBottom: '4px' }}>
                              {key}: {typeof value === 'number' ? value.toFixed(2) : value}
                            </Tag>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </Timeline.Item>
              );
            })}
          </Timeline>
        </Card>
      )}
    </div>
  );
};

export default WorkflowProgressIndicator;