#!/usr/bin/env python3
"""
测试数据提取功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.template_service import template_service
from services.data_extraction_service import data_extraction_service

async def test_template_service():
    """测试模板服务"""
    print("🧪 测试模板服务")
    print("=" * 50)

    # 检查模板目录
    print(f"模板目录: {template_service.templates_dir}")
    print(f"关键字定义目录: {template_service.keysdefinition_dir}")
    print(f"关键字定义目录存在: {template_service.keysdefinition_dir.exists()}")

    if template_service.keysdefinition_dir.exists():
        md_files = list(template_service.keysdefinition_dir.glob("*.md"))
        print(f"找到 {len(md_files)} 个.md文件:")
        for md_file in md_files:
            print(f"  - {md_file}")

    # 测试获取可用模板
    templates = template_service.get_available_templates()
    print(f"找到 {len(templates)} 个模板:")
    
    for template in templates:
        print(f"  - {template['template_name']} (ID: {template['template_id']})")
        print(f"    模板路径: {template['template_path']}")
        print(f"    定义路径: {template['key_definitions_path']}")
        print(f"    字段数量: {len(template['field_definitions'])}")
        
        # 显示前几个字段定义
        for i, field_def in enumerate(template['field_definitions'][:3]):
            print(f"    字段 {i+1}: {field_def['field_name']} - {field_def.get('description', 'N/A')}")
        
        if len(template['field_definitions']) > 3:
            print(f"    ... 还有 {len(template['field_definitions']) - 3} 个字段")
        print()

async def test_data_extraction():
    """测试数据提取"""
    print("🧪 测试数据提取")
    print("=" * 50)
    
    # 测试文档内容
    test_content = """
会议纪要

时间：2024年7月25日 14:00-15:30
地点：会议室A
主持人：张三
参会人员：李四、王五、赵六

会议议题：
1. 项目进度汇报
2. 下阶段工作安排
3. 资源配置讨论

会议内容：
1. 项目进度汇报
   - 当前完成度：80%
   - 主要成果：完成了核心功能开发
   - 遇到的问题：测试环境配置有待优化

2. 下阶段工作安排
   - 完成剩余功能开发
   - 进行全面测试
   - 准备上线部署

3. 资源配置讨论
   - 需要增加2名测试人员
   - 服务器资源需要扩容

决议事项：
1. 下周完成功能开发
2. 申请增加测试人员
3. 联系运维部门扩容服务器

下次会议时间：2024年8月1日 14:00
"""
    
    # 获取模板
    templates = template_service.get_available_templates()
    if not templates:
        print("❌ 没有找到可用模板")
        return
    
    template = templates[0]
    template_id = template['template_id']
    workflow_id = "test_workflow_001"
    
    print(f"使用模板: {template['template_name']}")
    print(f"文档内容长度: {len(test_content)} 字符")
    print()
    
    # 执行数据提取
    print("开始数据提取...")
    result = await data_extraction_service.extract_data_from_document(
        test_content, template_id, workflow_id
    )
    
    if result['success']:
        print("✅ 数据提取成功!")
        print(f"提取了 {len(result['extraction_results'])} 个字段")
        
        if 'file_path' in result:
            print(f"结果已保存到: {result['file_path']}")
        
        print("\n📊 提取结果:")
        for extraction_result in result['extraction_results']:
            field_name = extraction_result['field_name']
            field_value = extraction_result['field_value']
            confidence = extraction_result['confidence']
            
            print(f"  {field_name}: {field_value[:100]}{'...' if len(field_value) > 100 else ''}")
            print(f"    置信度: {confidence:.2f}")
            print()
    else:
        print(f"❌ 数据提取失败: {result.get('error', 'Unknown error')}")

async def main():
    """主函数"""
    print("🚀 开始测试数据提取功能")
    print("=" * 60)
    
    await test_template_service()
    await test_data_extraction()
    
    print("✅ 测试完成")

if __name__ == "__main__":
    asyncio.run(main())
