#!/usr/bin/env python3
"""
测试状态持久化修复
"""

import requests
import tempfile
import os
import json
import time

def test_state_persistence():
    """测试状态持久化修复"""
    
    # 创建测试文档
    test_content = """
    技术评审会议纪要
    
    会议时间：2025年1月25日 下午2:00-4:00
    会议地点：技术中心会议室
    主持人：技术总监
    参会人员：架构师、前端工程师、后端工程师、测试工程师、产品经理
    
    会议议题：
    1. 系统架构设计评审
    2. 技术选型讨论
    3. 开发计划制定
    
    讨论内容：
    1. 微服务架构设计方案通过评审
    2. 前端框架选择React + TypeScript
    3. 后端采用Python FastAPI
    4. 数据库使用PostgreSQL + Redis
    
    决议事项：
    1. 架构师完善设计文档，本周五前完成
    2. 前端工程师搭建项目脚手架，下周一开始
    3. 后端工程师设计API接口，下周三前完成
    4. 测试工程师制定测试策略，下周五前提交
    
    会议记录人：产品经理
    """
    
    print("🚀 开始测试状态持久化修复...")
    
    # 第1步: 启动工作流
    print("\n📝 第1步: 启动工作流...")
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(test_content)
        temp_file_path = f.name
    
    try:
        with open(temp_file_path, 'rb') as f:
            files = {'files': ('tech_review.txt', f, 'text/plain')}
            data = {'text_input': '请帮我生成会议纪要'}
            
            response = requests.post(
                'http://localhost:8000/api/workflow/langgraph/start',
                files=files,
                data=data
            )
        
        print(f"启动状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            workflow_id = result.get('workflow_id')
            print(f"工作流ID: {workflow_id}")
            print(f"工作流状态: {result.get('status')}")
            
            requires_user_action = result.get('data', {}).get('requires_user_action', False)
            print(f"需要用户操作: {requires_user_action}")
            
            if requires_user_action:
                print("\n✅ 工作流正确暂停，等待用户确认模板")
                
                # 第2步: 确认模板
                print("\n📋 第2步: 确认推荐的模板...")
                time.sleep(2)
                
                confirm_response = requests.post(
                    f'http://localhost:8000/api/workflow/langgraph/resume/{workflow_id}',
                    json={'action': 'confirm'}
                )
                
                print(f"模板确认状态码: {confirm_response.status_code}")
                if confirm_response.status_code == 200:
                    confirm_result = confirm_response.json()
                    print(f"确认状态: {confirm_result.get('status')}")
                    
                    # 第3步: 检查状态持久化
                    print("\n🔍 第3步: 检查状态持久化...")
                    
                    # 等待一段时间让工作流执行
                    time.sleep(5)
                    
                    # 检查状态
                    status_response = requests.get(
                        f'http://localhost:8000/api/workflow/langgraph/status/{workflow_id}'
                    )
                    
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        
                        print(f"📊 状态检查结果:")
                        print(f"  当前阶段: {status_data.get('current_stage')}")
                        print(f"  下一步动作: {status_data.get('next_action')}")
                        print(f"  待处理交互: {status_data.get('pending_interactions', [])}")
                        print(f"  人机交互数量: {len(status_data.get('human_interactions', []))}")
                        print(f"  提取结果数量: {len(status_data.get('extraction_results', []))}")
                        print(f"  提取数据数量: {len(status_data.get('extracted_data', {}))}")
                        print(f"  需要用户操作: {status_data.get('requires_user_action', False)}")
                        print(f"  错误: {status_data.get('errors', [])}")
                        
                        # 检查是否有实际的状态数据
                        has_meaningful_state = (
                            status_data.get('current_stage') or
                            status_data.get('pending_interactions') or
                            status_data.get('extraction_results') or
                            status_data.get('extracted_data')
                        )
                        
                        if has_meaningful_state:
                            print("\n🎉 状态持久化成功！工作流状态已正确保存")
                            
                            # 如果需要用户操作，进行数据确认
                            if status_data.get('requires_user_action'):
                                print("\n✅ 第4步: 确认提取的数据...")
                                
                                data_confirm_response = requests.post(
                                    f'http://localhost:8000/api/workflow/langgraph/resume/{workflow_id}',
                                    json={'action': 'confirm'}
                                )
                                
                                if data_confirm_response.status_code == 200:
                                    print("✅ 数据确认成功！")
                                else:
                                    print(f"❌ 数据确认失败: {data_confirm_response.status_code}")
                        else:
                            print("\n❌ 状态持久化失败！状态数据为空")
                    else:
                        print(f"❌ 状态检查失败: {status_response.status_code}")
                        
                else:
                    print(f"❌ 模板确认失败: {confirm_response.status_code}")
            else:
                print("❌ 工作流没有正确暂停等待用户输入")
        else:
            print(f"❌ 启动工作流失败: {response.status_code}")
            print(response.text)
            
    finally:
        # 清理临时文件
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
    
    print("\n✅ 状态持久化测试完成")

if __name__ == "__main__":
    test_state_persistence()
