# 报告智能生成工具 - 开发文档

## 📋 项目概述

报告智能生成工具是一个基于AI技术的文档处理解决方案，支持自动信息抽取和智能报告生成。

### 🎯 核心功能
- **文档上传与解析** - 支持docx/txt格式文档
- **智能信息抽取** - 基于通义千问qwq-32b-preview模型的字段抽取
- **报告自动生成** - 结构化报告生成
- **Web界面操作** - 基于React的现代化前端界面
- **端到端工作流** - 从文档上传到报告生成的完整自动化流程

## 🏗️ 技术架构

### 后端技术栈
- **框架**: FastAPI (Python 3.11+)
- **包管理**: uv (现代Python包管理器)
- **数据库**: PostgreSQL + SQLAlchemy ORM
- **AI模型**: 通义千问 qwq-32b-preview (DashScope API)
- **文档处理**: python-docx, 自定义txt解析器
- **智能体框架**: 自研LangGraph风格智能体系统

### 前端技术栈
- **框架**: React 19 + TypeScript
- **UI库**: Ant Design 5 (中文本地化)
- **状态管理**: Zustand (轻量级状态管理)
- **构建工具**: Vite (快速构建)
- **HTTP客户端**: Axios (API通信)

## 📁 项目结构

```
backend/
├── app.py                  # FastAPI应用入口
├── config.py              # 配置管理
├── database.py            # 数据库连接
├── pyproject.toml         # uv项目配置
├── .env                   # 环境变量配置
├── DEVELOPMENT.md         # 开发文档
├── api/                   # API路由
│   ├── upload.py          # 文件上传API
│   └── workflow.py        # 工作流API
├── agents/                # 智能体模块
│   ├── base_agent.py      # 基础智能体框架
│   └── extraction_agent.py # 信息抽取智能体
├── models/                # 数据模型
│   ├── template.py        # 模板模型
│   ├── data_source.py     # 数据源模型
│   └── report.py          # 报告模型
├── routers/               # 路由模块
│   ├── templates_router.py
│   ├── data_router.py
│   └── reports_router.py
├── services/              # 服务层
│   ├── llm_service.py     # LLM服务
│   ├── document_service.py # 文档处理服务
│   └── workflow_service.py # 端到端工作流服务
├── test/                  # 测试文件目录
│   ├── README.md          # 测试说明文档
│   ├── run_tests.py       # 统一测试运行脚本
│   ├── documents/         # 测试文档
│   │   ├── test_meeting.txt
│   │   └── test_project_report.docx
│   ├── scripts/           # 测试脚本
│   │   ├── test_db.py
│   │   ├── test_llm_simple.py
│   │   ├── test_document_service.py
│   │   ├── test_upload.py
│   │   ├── test_workflow.py
│   │   └── ...
│   └── data/              # 测试数据（预留）
└── uploads/               # 上传文件目录

frontend/
├── src/
│   ├── App.tsx            # 主应用组件
│   ├── main.tsx           # 应用入口
│   ├── App.css            # 全局样式
│   ├── index.css          # 基础样式
│   ├── components/        # React组件
│   │   ├── FileUpload.tsx      # 文件上传组件
│   │   ├── FieldConfiguration.tsx # 字段配置组件
│   │   └── ProcessingResult.tsx   # 处理结果组件
│   ├── services/          # API服务
│   │   └── api.ts         # API服务封装
│   └── stores/            # 状态管理
│       └── useAppStore.ts # Zustand状态管理
├── package.json           # 前端依赖
├── vite.config.ts         # Vite配置
└── index.html             # HTML入口
```

## 🚀 开发环境搭建

### 1. 环境要求
- **Python 3.11+** (推荐使用Python 3.11或3.12)
- **uv** (现代Python包管理器)
- **Node.js 18+** (推荐使用LTS版本)
- **PostgreSQL 14+** (数据库服务)
- **Git** (版本控制)

### 2. 后端设置
```bash
# 进入后端目录
cd backend

# 使用uv安装依赖
uv sync

# 配置环境变量
# 编辑 .env 文件，设置以下变量：
# DATABASE_URL=postgresql://username:password@localhost:5432/report_gen
# DASHSCOPE_API_KEY=your_api_key_here
# APP_NAME=报告智能生成工具

# 初始化数据库（如果需要）
uv run python test/scripts/recreate_db.py

# 启动开发服务器
uv run python app.py
# 服务将运行在 http://localhost:8000
```

### 3. 前端设置
```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
# 前端将运行在 http://localhost:5173
```

### 4. 验证安装
```bash
# 后端健康检查
curl http://localhost:8000/health

# 运行测试套件
cd backend
uv run python test/run_tests.py
```

## 🧪 测试指南

### 测试目录结构
```
test/
├── README.md              # 详细测试说明
├── run_tests.py          # 统一测试运行脚本
├── documents/            # 测试文档
│   ├── test_meeting.txt
│   └── test_project_report.docx
└── scripts/              # 测试脚本
    ├── test_db.py                    # 数据库连接测试
    ├── test_llm_simple.py          # LLM基础功能测试
    ├── test_document_service.py     # 文档解析测试
    ├── test_upload.py               # 文件上传API测试
    ├── test_workflow.py             # 端到端工作流测试
    └── ...
```

### 快速测试
```bash
cd backend

# 运行所有核心测试
uv run python test/run_tests.py

# 运行特定测试
uv run python test/scripts/test_db.py          # 数据库测试
uv run python test/scripts/test_llm_simple.py  # LLM测试
uv run python test/scripts/test_workflow.py    # 工作流测试
```

### 测试覆盖范围
- ✅ **数据库连接测试** - PostgreSQL连接和表创建
- ✅ **LLM服务测试** - 通义千问API调用和响应
- ✅ **文档解析测试** - docx/txt文档解析功能
- ✅ **文件上传测试** - 文件上传API和存储
- ✅ **智能体测试** - 信息抽取智能体功能
- ✅ **端到端工作流测试** - 完整的文档处理流程

### 测试要求
- 确保PostgreSQL服务运行
- 设置正确的DASHSCOPE_API_KEY
- 网络连接正常（用于LLM API调用）

详细测试说明请参考 [test/README.md](test/README.md)

## 🔧 配置说明

### 环境变量 (.env文件)
```bash
# 应用配置
APP_NAME=报告智能生成工具

# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/report_gen

# LLM服务配置
DASHSCOPE_API_KEY=your_dashscope_api_key_here
LLM_MODEL=qwq-32b-preview

# 文件上传配置
UPLOAD_DIR=./uploads

# CORS配置
CORS_ORIGINS=http://localhost:3000,http://localhost:5173
```

### 数据库配置
- **数据库**: PostgreSQL 14+
- **ORM**: SQLAlchemy 2.0
- **连接池**: 自动管理
- **表结构**: 自动创建和迁移
- **支持的表**: templates, data_sources, reports

### LLM配置
- **模型**: 通义千问 qwq-32b-preview
- **API**: DashScope API
- **超时**: 60秒
- **重试**: 自动重试机制

## 📝 API文档

### 核心API端点

#### 系统健康检查
- `GET /health` - 系统健康状态检查

#### 文件上传模块
- `POST /api/upload/document` - 上传文档文件
- `GET /api/upload/document/{file_id}` - 获取文档内容
- `DELETE /api/upload/document/{file_id}` - 删除文档
- `GET /api/upload/list` - 获取已上传文档列表

#### 端到端工作流模块
- `POST /api/workflow/process-document` - 上传并处理文档（完整工作流）
- `POST /api/workflow/process-uploaded-file` - 处理已上传的文件
- `GET /api/workflow/status/{workflow_id}` - 获取工作流处理状态
- `GET /api/workflow/demo-fields` - 获取预设字段模板

#### 传统模块（预留）
- `GET /api/templates/` - 模板管理
- `GET /api/data/sources` - 数据源管理
- `GET /api/reports/` - 报告管理

### API特性
- **自动文档**: Swagger UI + ReDoc
- **数据验证**: Pydantic模型验证
- **错误处理**: 统一错误响应格式
- **CORS支持**: 跨域请求支持
- **文件上传**: 支持大文件上传（50MB限制）

### API文档访问
启动后端服务后，访问以下地址查看API文档：
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## 🔄 开发工作流

### 1. 功能开发流程
1. **需求分析** - 明确功能需求和技术方案
2. **编写测试** - 先编写测试用例（TDD）
3. **实现功能** - 编写功能代码
4. **运行测试** - 确保所有测试通过
5. **集成测试** - 测试端到端功能
6. **代码审查** - 代码质量检查
7. **部署验证** - 在测试环境验证

### 2. 当前开发状态
- ✅ **基础架构** - FastAPI + PostgreSQL + React
- ✅ **LLM集成** - 通义千问API集成完成
- ✅ **文档处理** - docx/txt解析功能
- ✅ **智能体系统** - 信息抽取智能体
- ✅ **文件上传** - 完整的文件管理API
- ✅ **端到端工作流** - 从上传到报告生成
- ✅ **Web界面** - React前端界面
- 🚧 **用户认证** - 待开发
- 🚧 **模板管理** - 待完善
- 🚧 **批量处理** - 待开发

### 3. 代码规范
- **Python**: 遵循PEP 8，使用类型注解
- **TypeScript**: 严格模式，完整类型定义
- **文档**: 详细的docstring和注释
- **测试**: 每个功能都有对应测试
- **命名**: 清晰的变量和函数命名

## 🐛 故障排除

### 常见问题及解决方案

#### 1. 数据库相关问题
**问题**: 数据库连接失败
```bash
# 检查PostgreSQL服务状态
sudo systemctl status postgresql  # Linux
brew services list | grep postgresql  # macOS

# 测试数据库连接
uv run python test/scripts/test_db.py
```

**解决方案**:
- 确认PostgreSQL服务运行
- 检查DATABASE_URL格式
- 验证数据库用户权限
- 确认数据库存在

#### 2. LLM服务问题
**问题**: LLM API调用失败
```bash
# 测试LLM服务
uv run python test/scripts/test_llm_simple.py
```

**解决方案**:
- 检查DASHSCOPE_API_KEY是否正确设置
- 验证网络连接和防火墙设置
- 确认API配额和余额
- 检查模型名称是否正确

#### 3. 文件上传问题
**问题**: 文件上传失败
```bash
# 测试文件上传
uv run python test/scripts/test_upload.py
```

**解决方案**:
- 检查uploads目录是否存在和可写
- 验证文件大小限制（50MB）
- 确认磁盘空间充足
- 检查文件格式支持（docx/txt）

#### 4. 前端连接问题
**问题**: 前端无法连接后端

**解决方案**:
- 检查后端服务是否运行在8000端口
- 验证CORS配置包含前端地址
- 确认防火墙设置
- 检查浏览器控制台错误信息

#### 5. 依赖安装问题
**问题**: uv或npm依赖安装失败

**解决方案**:
```bash
# 清理并重新安装Python依赖
uv clean
uv sync

# 清理并重新安装Node.js依赖
cd frontend
rm -rf node_modules package-lock.json
npm install
```

## 📈 性能优化

### 后端性能优化
- **数据库连接池**: SQLAlchemy自动管理连接池
- **异步处理**: FastAPI异步支持，LLM调用异步化
- **缓存策略**: 文档解析结果缓存
- **批量处理**: 支持多文档批量处理
- **资源限制**: 文件大小限制，请求超时控制

### 前端性能优化
- **组件懒加载**: React.lazy动态导入
- **状态优化**: Zustand轻量级状态管理
- **API优化**: 请求去重，响应缓存
- **打包优化**: Vite快速构建，代码分割
- **用户体验**: 加载状态，进度指示

## 🔐 安全考虑

### 当前安全措施
- **文件上传安全**: 文件类型验证，大小限制
- **API安全**: CORS配置，请求验证
- **数据验证**: Pydantic模型验证
- **错误处理**: 安全的错误信息返回

### 待实现安全功能
- **用户认证**: JWT令牌认证
- **权限控制**: 基于角色的访问控制
- **数据加密**: 敏感数据加密存储
- **审计日志**: 操作日志记录

## � 部署指南

### 开发环境部署
```bash
# 后端
cd backend
uv run python app.py

# 前端
cd frontend
npm run dev
```

### 生产环境部署（待完善）
- **后端**: Docker容器化部署
- **前端**: 静态文件部署
- **数据库**: PostgreSQL集群
- **负载均衡**: Nginx反向代理

## �📚 参考资料

### 技术文档
- [FastAPI官方文档](https://fastapi.tiangolo.com/) - 后端框架
- [React官方文档](https://react.dev/) - 前端框架
- [Ant Design组件库](https://ant.design/) - UI组件
- [通义千问API文档](https://help.aliyun.com/zh/dashscope/) - LLM服务
- [uv包管理器](https://github.com/astral-sh/uv) - Python包管理
- [Zustand状态管理](https://github.com/pmndrs/zustand) - 前端状态管理

### 项目相关
- [项目测试文档](test/README.md) - 详细测试说明
- [API文档](http://localhost:8000/docs) - 在线API文档

## 📝 更新日志

### v1.0.0 (2024-07-04)
- ✅ 完成基础架构搭建
- ✅ 实现LLM服务集成
- ✅ 完成文档处理功能
- ✅ 实现智能体系统
- ✅ 完成文件上传功能
- ✅ 实现端到端工作流
- ✅ 完成Web前端界面
- ✅ 完善测试体系

### 下一版本计划
- 🚧 用户认证系统
- 🚧 模板管理功能
- 🚧 批量处理支持
- 🚧 部署自动化

---

*最后更新：2024年7月4日*
*项目状态：MVP完成，核心功能可用*
