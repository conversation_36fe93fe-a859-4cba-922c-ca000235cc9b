/**
 * 字段配置组件
 */

import React, { useState } from 'react';
import {
  Card,
  Typography,
  Button,
  Space,
  Form,
  Input,
  Select,
  Table,
  Modal,
  Dropdown,
  Divider,
  Alert,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  DownOutlined,
  FileTextOutlined,
  ProjectOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
interface TemplateField {
  field_name: string;
  description: string;
  example: string;
  format: string;
}

const PRESET_TEMPLATE_FIELDS = {
  meeting: [
    {
      field_name: '会议主题',
      description: '会议讨论的主要议题',
      example: '数字化转型项目进展讨论',
      format: '普通文本',
    },
    {
      field_name: '会议时间',
      description: '会议举行的具体时间',
      example: '2024年7月4日 14:00-16:00',
      format: '日期时间',
    },
    {
      field_name: '主持人',
      description: '会议的主持人姓名',
      example: '张总经理',
      format: '人名',
    },
    {
      field_name: '参会人数',
      description: '参加会议的总人数',
      example: '5人',
      format: '数字',
    },
  ],
  project: [
    {
      field_name: '项目名称',
      description: '项目的正式名称',
      example: '智能报告生成系统',
      format: '普通文本',
    },
    {
      field_name: '项目负责人',
      description: '项目的主要负责人姓名',
      example: '李明',
      format: '人名',
    },
    {
      field_name: '完成进度',
      description: '项目当前的完成百分比',
      example: '75%',
      format: '百分比',
    },
    {
      field_name: '预计完成时间',
      description: '项目预计的完成日期',
      example: '2024年8月31日',
      format: '日期',
    },
  ],
} as const;

const { Title, Paragraph } = Typography;
const { TextArea } = Input;

interface FieldConfigurationProps {
  onNext: () => void;
  onPrev: () => void;
}

const FieldConfiguration: React.FC<FieldConfigurationProps> = ({ onNext, onPrev }) => {
  const [templateFields, setTemplateFields] = useState<TemplateField[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [form] = Form.useForm();

  const fieldFormatOptions = [
    { label: '普通文本', value: '普通文本' },
    { label: '日期时间', value: '日期时间' },
    { label: '日期', value: '日期' },
    { label: '人名', value: '人名' },
    { label: '数字', value: '数字' },
    { label: '百分比', value: '百分比' },
    { label: '金额', value: '金额' },
    { label: '表格行', value: '表格行' },
    { label: '段落', value: '段落' },
    { label: '单独行', value: '单独行' },
  ];

  const columns: ColumnsType<TemplateField> = [
    {
      title: '字段名称',
      dataIndex: 'field_name',
      key: 'field_name',
      width: 120,
    },
    {
      title: '字段描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '示例',
      dataIndex: 'example',
      key: 'example',
      width: 150,
      ellipsis: true,
    },
    {
      title: '格式',
      dataIndex: 'format',
      key: 'format',
      width: 100,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, _record, index) => (
        <Space>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(index)}
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => {
              const newFields = templateFields.filter((_, i) => i !== index);
              setTemplateFields(newFields);
            }}
          />
        </Space>
      ),
    },
  ];

  const handleEdit = (index: number) => {
    setEditingIndex(index);
    form.setFieldsValue(templateFields[index]);
    setIsModalVisible(true);
  };

  const handleAdd = () => {
    setEditingIndex(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      const field: TemplateField = {
        field_name: values.field_name,
        description: values.description,
        example: values.example,
        format: values.format,
      };

      if (editingIndex !== null) {
        const newFields = [...templateFields];
        newFields[editingIndex] = field;
        setTemplateFields(newFields);
      } else {
        setTemplateFields([...templateFields, field]);
      }

      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const handleLoadPreset = (type: 'meeting' | 'project') => {
    setTemplateFields([...PRESET_TEMPLATE_FIELDS[type]]);
  };

  const presetMenuItems = [
    {
      key: 'meeting',
      label: '会议纪要模板',
      icon: <FileTextOutlined />,
      onClick: () => handleLoadPreset('meeting'),
    },
    {
      key: 'project',
      label: '项目报告模板',
      icon: <ProjectOutlined />,
      onClick: () => handleLoadPreset('project'),
    },
  ];

  const handleNext = () => {
    if (templateFields.length === 0) {
      Modal.warning({
        title: '请配置字段',
        content: '至少需要配置一个抽取字段才能继续。',
      });
      return;
    }
    onNext();
  };

  return (
    <div style={{ maxWidth: 1000, margin: '0 auto', padding: '24px' }}>
      <Title level={2}>⚙️ 配置抽取字段</Title>
      <Paragraph type="secondary">
        定义需要从文档中抽取的字段信息。每个字段包含名称、描述、示例和格式类型。
      </Paragraph>

      <Card>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
              添加字段
            </Button>
            <Dropdown menu={{ items: presetMenuItems }} placement="bottomLeft">
              <Button>
                加载预设模板 <DownOutlined />
              </Button>
            </Dropdown>
          </Space>
          <span style={{ color: '#666' }}>
            已配置 {templateFields.length} 个字段
          </span>
        </div>

        {templateFields.length === 0 ? (
          <Alert
            message="暂无配置字段"
            description="请添加需要抽取的字段，或选择预设模板快速开始。"
            type="info"
            showIcon
            style={{ margin: '24px 0' }}
          />
        ) : (
          <Table
            columns={columns}
            dataSource={templateFields}
            rowKey={(record, index) => `${record.field_name}-${index}`}
            pagination={false}
            size="middle"
          />
        )}

        <Divider />

        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Button onClick={onPrev}>
            上一步
          </Button>
          <Button type="primary" onClick={handleNext}>
            下一步：处理文档
          </Button>
        </div>
      </Card>

      <Modal
        title={editingIndex !== null ? '编辑字段' : '添加字段'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            format: '普通文本',
          }}
        >
          <Form.Item
            label="字段名称"
            name="field_name"
            rules={[{ required: true, message: '请输入字段名称' }]}
          >
            <Input placeholder="例如：会议主题" />
          </Form.Item>

          <Form.Item
            label="字段描述"
            name="description"
            rules={[{ required: true, message: '请输入字段描述' }]}
          >
            <TextArea
              rows={3}
              placeholder="详细描述该字段的含义，用于指导AI进行信息抽取"
            />
          </Form.Item>

          <Form.Item
            label="示例内容"
            name="example"
            rules={[{ required: true, message: '请输入示例内容' }]}
          >
            <Input placeholder="提供一个具体的示例，帮助AI理解期望的格式" />
          </Form.Item>

          <Form.Item
            label="字段格式"
            name="format"
            rules={[{ required: true, message: '请选择字段格式' }]}
          >
            <Select options={fieldFormatOptions} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default FieldConfiguration;
