"""
模板识别工具
负责根据用户需求和文档内容智能识别最适合的报告模板
"""

import os
import json
from typing import Dict, Any, List, Optional
from sqlalchemy.orm import Session

from .base_tool import BaseTool, ToolResult
from services.llm_service import llm_service
from models.template import Template
from database import get_db


class TemplateIdentificationTool(BaseTool):
    """模板识别工具"""
    
    def __init__(self):
        super().__init__("template_identification")
        self.prompt_template = self._load_prompt_template()
    
    def _load_prompt_template(self) -> str:
        """加载prompt模板"""
        try:
            prompt_path = os.path.join(
                os.path.dirname(__file__), 
                "..", "prompts", "template_identification.txt"
            )
            with open(prompt_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            self.log_error(f"加载prompt模板失败: {e}")
            return ""
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行模板识别
        
        Args:
            input_data: {
                "user_request": "用户需求描述",
                "document_content": "文档内容摘要",
                "document_type": "文档类型",
                "available_templates": "可用模板列表"
            }
        """
        try:
            # 验证输入
            required_fields = ["user_request", "document_content"]
            if not self.validate_input(input_data, required_fields):
                return self.create_result(False, error="输入数据验证失败")
            
            # 获取可用模板
            templates = await self._get_available_templates()
            if not templates:
                return self.create_result(False, error="未找到可用模板")
            
            # 构建分析prompt
            analysis_prompt = self._build_analysis_prompt(
                input_data["user_request"],
                input_data["document_content"],
                input_data.get("document_type", ""),
                templates
            )
            
            # 调用LLM进行分析
            llm_response = await llm_service.call_llm([
                {"role": "system", "content": self.prompt_template},
                {"role": "user", "content": analysis_prompt}
            ])
            
            if not llm_response.success:
                return self.create_result(False, error=f"LLM调用失败: {llm_response.error}")
            
            # 解析LLM响应
            analysis_result = self._parse_llm_response(llm_response.content)
            
            # 验证和优化结果
            validated_result = await self._validate_and_optimize_result(analysis_result, templates)
            
            self.log_info(f"模板识别完成，推荐模板: {validated_result.get('recommendation', {}).get('primary_template')}")
            
            return self.create_result(True, validated_result, message="模板识别成功")
            
        except Exception as e:
            self.log_error(f"模板识别执行失败: {e}")
            return self.create_result(False, error=str(e))
    
    async def _get_available_templates(self) -> List[Dict[str, Any]]:
        """获取可用模板列表"""
        try:
            db = next(get_db())
            templates = db.query(Template).filter(Template.is_active == True).all()

            template_list = []
            for template in templates:
                template_info = {
                    "template_id": str(template.id),
                    "template_name": template.name,
                    "template_category": template.category,
                    "description": template.description,
                    "placeholder_count": template.placeholder_count,
                    "version": template.version,
                    "key_definitions_path": template.key_definitions_path
                }
                template_list.append(template_info)

            return template_list

        except Exception as e:
            self.log_error(f"获取模板列表失败: {e}")
            return []
    
    def _build_analysis_prompt(self, user_request: str, document_content: str, 
                              document_type: str, templates: List[Dict[str, Any]]) -> str:
        """构建分析prompt"""
        template_info = "\n".join([
            f"- {t['template_name']} (ID: {t['template_id']}, 分类: {t['template_category']}, 描述: {t['description']})"
            for t in templates
        ])
        
        prompt = f"""
请分析以下信息并进行模板识别：

## 用户需求
{user_request}

## 文档内容摘要
{document_content}

## 文档类型
{document_type}

## 可用模板列表
{template_info}

请根据上述信息进行分析，并按照指定的JSON格式返回结果。
"""
        return prompt
    
    def _parse_llm_response(self, response_content: str) -> Dict[str, Any]:
        """解析LLM响应"""
        try:
            # 尝试提取JSON内容
            start_idx = response_content.find('{')
            end_idx = response_content.rfind('}') + 1
            
            if start_idx != -1 and end_idx > start_idx:
                json_content = response_content[start_idx:end_idx]
                return json.loads(json_content)
            else:
                # 如果没有找到JSON，返回默认结构
                return {
                    "analysis_result": {
                        "document_type": "未知",
                        "key_features": [],
                        "content_summary": response_content[:200]
                    },
                    "template_matches": [],
                    "recommendation": {
                        "primary_template": None,
                        "confidence_level": "low",
                        "user_confirmation_needed": True
                    },
                    "next_action": "human_interaction"
                }
                
        except json.JSONDecodeError as e:
            self.log_error(f"解析LLM响应失败: {e}")
            return {
                "analysis_result": {
                    "document_type": "解析失败",
                    "key_features": [],
                    "content_summary": "响应解析失败"
                },
                "template_matches": [],
                "recommendation": {
                    "primary_template": None,
                    "confidence_level": "low",
                    "user_confirmation_needed": True
                },
                "next_action": "error_handling"
            }
    
    async def _validate_and_optimize_result(self, analysis_result: Dict[str, Any], 
                                          templates: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证和优化分析结果"""
        try:
            # 验证推荐的模板是否存在
            recommended_template_id = analysis_result.get("recommendation", {}).get("primary_template")
            if recommended_template_id:
                template_exists = any(t["template_id"] == recommended_template_id for t in templates)
                if not template_exists:
                    self.log_warning(f"推荐的模板不存在: {recommended_template_id}")
                    analysis_result["recommendation"]["primary_template"] = None
                    analysis_result["recommendation"]["confidence_level"] = "low"
                    analysis_result["next_action"] = "human_interaction"
            
            # 确保必要字段存在
            if "template_matches" not in analysis_result:
                analysis_result["template_matches"] = []
            
            if "recommendation" not in analysis_result:
                analysis_result["recommendation"] = {
                    "primary_template": None,
                    "confidence_level": "low",
                    "user_confirmation_needed": True
                }
            
            # 设置下一步动作
            confidence_level = analysis_result["recommendation"].get("confidence_level", "low")
            if confidence_level == "high" and analysis_result["recommendation"].get("primary_template"):
                analysis_result["next_action"] = "template_parsing"
            else:
                analysis_result["next_action"] = "human_interaction"
            
            return analysis_result
            
        except Exception as e:
            self.log_error(f"结果验证失败: {e}")
            return analysis_result
    
    async def get_template_details(self, template_id: str) -> Optional[Dict[str, Any]]:
        """获取模板详细信息"""
        try:
            db = next(get_db())
            template = db.query(Template).filter(Template.id == template_id).first()
            
            if not template:
                return None
            
            return {
                "template_id": str(template.id),
                "template_name": template.name,
                "description": template.description,
                "category": template.category,
                "file_path": template.file_path,
                "placeholder_count": template.placeholder_count,
                "version": template.version,
                "created_at": template.created_at.isoformat() if template.created_at else None
            }
            
        except Exception as e:
            self.log_error(f"获取模板详情失败: {e}")
            return None
