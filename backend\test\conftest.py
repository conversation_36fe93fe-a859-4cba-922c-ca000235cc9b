"""
pytest配置文件
为LangGraph测试提供共享的fixtures和配置
"""

import pytest
import asyncio
import sys
import os
from datetime import datetime
from pathlib import Path
from unittest.mock import Mock, AsyncMock

# 配置pytest-asyncio
pytest_plugins = ('pytest_asyncio',)

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from agents.langgraph_state import UserInput, create_initial_state


@pytest.fixture
def event_loop():
    """创建事件循环fixture"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def sample_user_input():
    """示例用户输入fixture"""
    return UserInput(
        text="生成会议纪要",
        files=[{
            "filename": "meeting_record.txt",
            "content": "项目进度评审会于2025年6月26日上午9:00在会议室召开，主持人张三，参会人员李四、王五。会议讨论了项目进展情况，确定了下一步工作计划。"
        }],
        session_id="test_session_123",
        user_id="test_user_456",
        timestamp=datetime.now()
    )


@pytest.fixture
def sample_workflow_state(sample_user_input):
    """示例工作流状态fixture"""
    workflow_id = "test_workflow_789"
    return create_initial_state(sample_user_input, workflow_id)


@pytest.fixture
def mock_llm_service():
    """模拟LLM服务fixture"""
    mock_service = Mock()
    
    # 默认成功响应
    mock_response = Mock()
    mock_response.success = True
    mock_response.content = "测试响应内容"
    mock_response.error = None
    
    mock_service.call_llm = AsyncMock(return_value=mock_response)
    
    return mock_service


@pytest.fixture
def mock_document_service():
    """模拟文档服务fixture"""
    mock_service = Mock()
    
    # 模拟文档解析结果
    mock_service.parse_document.return_value = {
        "success": True,
        "content": "解析的文档内容",
        "metadata": {
            "page_count": 1,
            "word_count": 100
        }
    }
    
    return mock_service


@pytest.fixture
def sample_template_info():
    """示例模板信息fixture"""
    return {
        "template_id": "template_123",
        "template_name": "数字科技中心会议纪要",
        "template_path": "sample_report/数字科技中心会议纪要/4.template_数字科技中心会议纪要模板.docx",
        "key_definitions_path": "sample_report/数字科技中心会议纪要/2.key_definitions.md",
        "placeholder_count": 10,
        "field_definitions": [
            {
                "field_name": "meeting_title",
                "description": "会议的名称",
                "example": "项目进度评审会",
                "format": "普通文本",
                "must": True,
                "default": "",
                "length": "50",
                "source": "文档标题"
            },
            {
                "field_name": "meeting_time",
                "description": "会议召开的时间",
                "example": "2025年6月26日上午9:00",
                "format": "日期时间",
                "must": True,
                "default": "",
                "length": "30",
                "source": "文档内容"
            },
            {
                "field_name": "meeting_host",
                "description": "会议主持人",
                "example": "张三",
                "format": "普通文本",
                "must": False,
                "default": "未指定",
                "length": "20",
                "source": "文档内容"
            }
        ]
    }


@pytest.fixture
def sample_extraction_results():
    """示例提取结果fixture"""
    return [
        {
            "field_name": "meeting_title",
            "field_value": "项目进度评审会",
            "confidence": 0.9,
            "source_location": "文档标题",
            "extraction_method": "llm",
            "is_verified": False,
            "needs_human_input": False
        },
        {
            "field_name": "meeting_time",
            "field_value": "2025年6月26日上午9:00",
            "confidence": 0.85,
            "source_location": "第1段",
            "extraction_method": "llm",
            "is_verified": False,
            "needs_human_input": False
        },
        {
            "field_name": "meeting_host",
            "field_value": "张三",
            "confidence": 0.8,
            "source_location": "第1段",
            "extraction_method": "llm",
            "is_verified": False,
            "needs_human_input": False
        }
    ]


@pytest.fixture
def sample_validation_results():
    """示例验证结果fixture"""
    return [
        {
            "field_name": "meeting_title",
            "is_valid": True,
            "validation_score": 0.9,
            "error_message": None,
            "suggestions": []
        },
        {
            "field_name": "meeting_time",
            "is_valid": True,
            "validation_score": 0.85,
            "error_message": None,
            "suggestions": []
        },
        {
            "field_name": "meeting_host",
            "is_valid": True,
            "validation_score": 0.8,
            "error_message": None,
            "suggestions": []
        }
    ]


@pytest.fixture
def mock_file_content():
    """模拟文件内容fixture"""
    return """数字科技中心会议纪要

# meeting_title
description:会议的名称
example:项目进度评审会
format:普通文本
must:true
default:
length:50
source:文档标题

# meeting_time
description:会议召开的时间
example:2025年6月26日上午9:00
format:日期时间
must:true
default:
length:30
source:文档内容

# meeting_host
description:会议主持人
example:张三
format:普通文本
must:false
default:未指定
length:20
source:文档内容"""


# 测试标记
def pytest_configure(config):
    """配置pytest标记"""
    config.addinivalue_line(
        "markers", "unit: 单元测试"
    )
    config.addinivalue_line(
        "markers", "integration: 集成测试"
    )
    config.addinivalue_line(
        "markers", "slow: 慢速测试"
    )
    config.addinivalue_line(
        "markers", "llm: 需要LLM服务的测试"
    )


# 测试收集钩子
def pytest_collection_modifyitems(config, items):
    """修改测试收集"""
    for item in items:
        # 为异步测试添加标记
        if asyncio.iscoroutinefunction(item.function):
            item.add_marker(pytest.mark.asyncio)
        
        # 为包含LLM的测试添加标记
        if "llm" in item.name.lower() or "llm_service" in str(item.function):
            item.add_marker(pytest.mark.llm)


# 测试会话钩子
@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """设置测试环境"""
    print("\n🧪 设置LangGraph测试环境...")
    
    # 设置测试环境变量
    os.environ["TESTING"] = "true"
    os.environ["LOG_LEVEL"] = "DEBUG"
    
    yield
    
    print("\n🧹 清理LangGraph测试环境...")
    
    # 清理测试环境变量
    if "TESTING" in os.environ:
        del os.environ["TESTING"]
