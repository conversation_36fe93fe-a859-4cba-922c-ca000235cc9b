"""
LangGraph节点实现
定义各个智能体节点的具体实现
"""

import logging
from typing import Dict, Any
from datetime import datetime
from uuid import uuid4

from .langgraph_state import WorkflowState, NodeResult, update_state_stage, add_error, add_human_interaction
from services.llm_service import llm_service
from services.document_service import document_service
from services.template_service import template_service
from services.data_extraction_service import data_extraction_service

logger = logging.getLogger(__name__)


class LangGraphNodes:
    """LangGraph节点集合"""

    @staticmethod
    def _load_prompt_template(filename: str) -> str:
        """加载prompt模板文件"""
        try:
            import os
            prompt_path = os.path.join(
                os.path.dirname(__file__),
                "prompts",
                filename
            )
            with open(prompt_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"加载prompt模板失败 {filename}: {e}")
            return ""

    @staticmethod
    async def intention_analysis_node(state: WorkflowState) -> WorkflowState:
        """意图识别节点"""
        logger.info(f"[{state['workflow_id']}] 开始意图识别")
        
        try:
            # 更新状态
            state = update_state_stage(state, "intention_analysis")
            
            user_text = state["user_input"]["text"]
            uploaded_files = state["user_input"]["files"]

            # 加载意图识别prompt模板
            prompt_template = LangGraphNodes._load_prompt_template("intention_identification.txt")

            # 构建用户输入prompt，包含文件信息
            files_info = ""
            if uploaded_files:
                files_info = f"\n\n用户已上传文件：\n"
                for i, file_info in enumerate(uploaded_files, 1):
                    files_info += f"{i}. {file_info.get('filename', '未知文件')} (大小: {file_info.get('size', 0)} bytes)\n"

            user_prompt = f"现在请分析用户的输入内容：\n\n用户输入：{user_text}{files_info}"

            messages = [
                {"role": "system", "content": prompt_template},
                {"role": "user", "content": user_prompt}
            ]
            
            # 调用LLM
            response = await llm_service.call_llm(messages)
            
            if response.success:
                import json
                try:
                    # 尝试直接解析
                    content = response.content.strip()
                    intention_data = json.loads(content)
                except json.JSONDecodeError:
                    try:
                        # 尝试提取JSON部分（处理```json标记）
                        if "```json" in content:
                            start = content.find("```json") + 7
                            end = content.find("```", start)
                            if end != -1:
                                json_part = content[start:end].strip()
                                intention_data = json.loads(json_part)
                            else:
                                raise json.JSONDecodeError("无法找到JSON结束标记", content, 0)
                        else:
                            raise json.JSONDecodeError("无法解析JSON内容", content, 0)
                    except json.JSONDecodeError:
                        state = add_error(state, "intention_parse_error", f"意图识别结果解析失败: {content[:200]}...")
                        state["next_action"] = "error_handling"
                        return state

                state["intention_result"] = intention_data
                state["next_action"] = "route_to_task"
                logger.info(f"[{state['workflow_id']}] 意图识别成功: {intention_data['intent_type']}")
            else:
                state = add_error(state, "intention_llm_error", f"LLM调用失败: {response.error}")
                state["next_action"] = "error_handling"
                
        except Exception as e:
            logger.error(f"[{state['workflow_id']}] 意图识别异常: {e}")
            state = add_error(state, "intention_exception", str(e))
            state["next_action"] = "error_handling"
        
        return state
    
    @staticmethod
    async def template_identification_node(state: WorkflowState) -> WorkflowState:
        """模板识别节点"""
        logger.info(f"[{state['workflow_id']}] 开始模板识别")
        
        try:
            state = update_state_stage(state, "template_identification")
            
            user_text = state["user_input"]["text"]
            intention = state["intention_result"]
            
            # 使用动态模板服务识别模板
            available_templates = template_service.get_available_templates()

            if not available_templates:
                state = add_error(state, "no_templates", "未找到可用的模板")
                state["next_action"] = "error_handling"
                return state

            # 智能选择模板 - 增强版
            identified_templates = []
            template_scores = []
            
            # 获取意图识别的模板提示
            template_hints = intention.get("template_hints", [])
            keywords = intention.get("keywords", [])
            
            # 基于多维度匹配模板
            for template in available_templates:
                score = 0.0
                template_name = template["template_name"].lower()
                
                # 1. 直接关键词匹配 (权重0.4)
                for keyword in keywords:
                    if keyword in template_name:
                        score += 0.4
                
                # 2. 模板提示匹配 (权重0.3)
                for hint in template_hints:
                    if hint.lower() in template_name:
                        score += 0.3
                
                # 3. 文本相似度匹配 (权重0.3)
                if "会议" in user_text or "meeting" in user_text.lower():
                    if "会议" in template_name or "meeting" in template_name:
                        score += 0.3
                        
                if "项目" in user_text or "project" in user_text.lower():
                    if "项目" in template_name or "project" in template_name:
                        score += 0.3
                        
                if "报告" in user_text or "report" in user_text.lower():
                    if "报告" in template_name or "report" in template_name:
                        score += 0.3
                
                # 4. 文档内容相关性 (如果有上传文件)
                document_relevance = intention.get("document_relevance", 0.0)
                if document_relevance > 0.5:
                    score += 0.2 * document_relevance
                
                template_scores.append((template, score))
            
            # 按分数排序并筛选
            template_scores.sort(key=lambda x: x[1], reverse=True)
            
            # 选择分数最高的模板（分数>0.3的认为是匹配的）
            for template, score in template_scores:
                if score > 0.3:
                    identified_templates.append({
                        **template,
                        "match_score": score,
                        "match_reason": f"匹配度: {score:.2f}"
                    })
            
            # 如果没有找到匹配的模板，使用默认策略
            if not identified_templates:
                # 使用评分最高的模板作为默认选择
                if template_scores:
                    best_template = template_scores[0][0]
                    identified_templates = [best_template]

            logger.info(f"[{state['workflow_id']}] 识别到 {len(identified_templates)} 个匹配模板")

            if identified_templates:
                # 找到匹配的模板，添加用户确认交互
                state["identified_templates"] = identified_templates
                state["template_info"] = identified_templates[0]  # 默认选择第一个

                # 添加用户确认交互 - 增强版
                top_template = identified_templates[0]
                match_score = top_template.get("match_score", 0.0)
                
                # 动态生成确认消息
                if match_score > 0.8:
                    confidence_text = "高度匹配"
                elif match_score > 0.5:
                    confidence_text = "较好匹配"
                else:
                    confidence_text = "基本匹配"
                
                interaction_data = {
                    "type": "template_confirmation",
                    "message": f"智能分析完成！为您找到{confidence_text}的模板：",
                    "templates": identified_templates,
                    "default_selection": 0,
                    "match_info": {
                        "confidence": confidence_text,
                        "score": match_score,
                        "total_available": len(available_templates),
                        "matched_count": len(identified_templates)
                    },
                    "options": [
                        {"value": "confirm", "label": f"使用推荐模板 ({confidence_text})"},
                        {"value": "select_other", "label": f"浏览其他模板 ({len(available_templates)}个可选)"},
                        {"value": "custom", "label": "自定义模板要求"},
                        {"value": "cancel", "label": "取消生成"}
                    ],
                    "additional_actions": [
                        {"value": "preview_template", "label": "预览模板结构"},
                        {"value": "modify_fields", "label": "调整字段要求"}
                    ]
                }
                state = add_human_interaction(state, "confirm", "template_confirmation", interaction_data)
                state["next_action"] = "human_interaction"
                logger.info(f"[{state['workflow_id']}] 模板识别成功，等待用户确认: {identified_templates[0]['template_name']}")
            else:
                # 没有找到匹配的模板，需要用户选择
                interaction_data = {
                    "type": "template_selection",
                    "message": "未能自动识别合适的模板，请选择您需要的报告类型：",
                    "available_templates": [
                        {"id": "meeting_minutes_001", "name": "会议纪要", "description": "生成会议纪要文档"},
                        {"id": "project_report_001", "name": "项目报告", "description": "生成项目进度报告"},
                        {"id": "technical_report_001", "name": "技术报告", "description": "生成技术分析报告"}
                    ]
                }
                state = add_human_interaction(state, "select", "template_selection", interaction_data)
                state["next_action"] = "human_interaction"
                
        except Exception as e:
            logger.error(f"[{state['workflow_id']}] 模板识别异常: {e}")
            state = add_error(state, "template_identification_exception", str(e))
            state["next_action"] = "error_handling"
        
        return state
    
    @staticmethod
    async def template_parsing_node(state: WorkflowState) -> WorkflowState:
        """模板解析节点"""
        logger.info(f"[{state['workflow_id']}] 开始模板解析")

        try:
            state = update_state_stage(state, "template_parsing")

            template_info = state.get("template_info")
            logger.info(f"[{state['workflow_id']}] template_info类型: {type(template_info)}, 值: {template_info}")

            if not template_info:
                logger.error(f"[{state['workflow_id']}] 模板信息缺失")
                state = add_error(state, "template_info_missing", "模板信息缺失，无法进行模板解析")
                state["next_action"] = "error_handling"
                return state

            key_definitions_path = template_info.get("key_definitions_path")
            if not key_definitions_path:
                state = add_error(state, "key_definitions_path_missing", "模板定义文件路径缺失")
                state["next_action"] = "error_handling"
                return state
            
            # 解析key_definitions.md文件
            # 这里应该调用智能模板解析工具
            try:
                import os
                # 确保使用绝对路径
                if not os.path.isabs(key_definitions_path):
                    # 相对于项目根目录
                    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
                    key_definitions_path = os.path.join(base_dir, key_definitions_path)

                logger.info(f"尝试读取模板文件: {key_definitions_path}")
                with open(key_definitions_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 简化的解析逻辑，实际应该更复杂
                field_definitions = []
                lines = content.split('\n')
                current_field = {}
                
                for line in lines:
                    line = line.strip()
                    if line.startswith('# ') and not line.startswith('## '):
                        if current_field:
                            field_definitions.append(current_field)
                        current_field = {"field_name": line[2:]}
                    elif line.startswith('description:'):
                        current_field["description"] = line[12:]
                    elif line.startswith('example:'):
                        current_field["example"] = line[8:]
                    elif line.startswith('format:'):
                        current_field["format"] = line[7:]
                    elif line.startswith('must:'):
                        current_field["must"] = line[5:].lower() == 'true'
                
                if current_field:
                    field_definitions.append(current_field)
                
                state["template_info"]["field_definitions"] = field_definitions
                state["next_action"] = "data_extraction"
                logger.info(f"[{state['workflow_id']}] 模板解析成功，识别到 {len(field_definitions)} 个字段")
                
            except FileNotFoundError:
                state = add_error(state, "template_file_not_found", f"模板定义文件未找到: {key_definitions_path}")
                state["next_action"] = "error_handling"
                
        except Exception as e:
            logger.error(f"[{state['workflow_id']}] 模板解析异常: {e}")
            state = add_error(state, "template_parsing_exception", str(e))
            state["next_action"] = "error_handling"
        
        return state
    
    @staticmethod
    async def data_extraction_node(state: WorkflowState) -> WorkflowState:
        """数据提取节点"""
        logger.info(f"[{state['workflow_id']}] 开始数据提取")
        
        try:
            state = update_state_stage(state, "data_extraction")
            
            # 获取用户上传的文件内容
            uploaded_files = state["user_input"]["files"]
            template_info = state["template_info"]

            # 解析文档内容
            document_content = ""
            for file_info in uploaded_files:
                # 优先使用已解析的内容
                if file_info.get("content"):
                    document_content += file_info["content"] + "\n"
                # 如果没有解析内容，尝试重新解析文件
                elif file_info.get("file_path"):
                    try:
                        doc_result = document_service.parse_document(file_info["file_path"])
                        if doc_result.success:
                            document_content += doc_result.raw_text + "\n"
                            logger.info(f"重新解析文件成功: {file_info.get('filename', 'unknown')}")
                        else:
                            logger.warning(f"文件解析失败: {doc_result.error}")
                    except Exception as e:
                        logger.error(f"文件解析异常: {e}")

            # 如果没有上传文件，使用用户输入的文本
            if not document_content.strip():
                document_content = state["user_input"]["text"]

            logger.info(f"[{state['workflow_id']}] 文档内容长度: {len(document_content)} 字符")
            
            # 使用智能数据提取服务
            template_id = template_info["template_id"]
            extraction_result = await data_extraction_service.extract_data_from_document(
                document_content, template_id, state["workflow_id"]
            )

            if extraction_result["success"]:
                extraction_results = extraction_result["extraction_results"]
                state["extracted_data"] = extraction_result["data"]
                state["extraction_file_path"] = extraction_result.get("file_path")
                logger.info(f"[{state['workflow_id']}] 数据提取成功，提取了 {len(extraction_results)} 个字段")
            else:
                logger.error(f"[{state['workflow_id']}] 数据提取失败: {extraction_result.get('error', 'Unknown error')}")
                extraction_results = extraction_result.get("extraction_results", [])
                state = add_error(state, "data_extraction_failed", extraction_result.get("error", "数据提取失败"))
            
            state["extraction_results"] = extraction_results
            
            # 统计提取结果
            field_definitions = template_info.get("field_definitions", [])
            total_fields = len(field_definitions)
            extracted_fields = len([r for r in extraction_results if r["field_value"] != "未找到"])
            success_rate = extracted_fields / total_fields if total_fields > 0 else 0
            
            state["extraction_stats"] = {
                "total_fields": total_fields,
                "extracted_fields": extracted_fields,
                "success_rate": success_rate,
                "avg_confidence": sum(r["confidence"] for r in extraction_results) / len(extraction_results) if extraction_results else 0
            }
            
            # 检查是否需要人工干预 - 增强版
            needs_human_input = any(r["needs_human_input"] for r in extraction_results)
            missing_fields = [r for r in extraction_results if r["needs_human_input"]]
            low_confidence_fields = [r for r in extraction_results if r["confidence"] < 0.7]
            
            # 计算提取质量等级
            if success_rate >= 0.9:
                quality_level = "优秀"
                quality_color = "success"
            elif success_rate >= 0.7:
                quality_level = "良好"
                quality_color = "warning"
            else:
                quality_level = "需完善"
                quality_color = "error"

            if needs_human_input or len(low_confidence_fields) > 0:
                # 准备增强的人机交互数据
                interaction_data = {
                    "type": "data_extraction_review",
                    "message": f"信息提取完成 - 质量等级: {quality_level}",
                    "extraction_summary": {
                        "total_fields": total_fields,
                        "extracted_fields": extracted_fields,
                        "success_rate": success_rate,
                        "quality_level": quality_level,
                        "quality_color": quality_color
                    },
                    "extraction_results": extraction_results,
                    "missing_fields": missing_fields,
                    "low_confidence_fields": low_confidence_fields,
                    "review_sections": [
                        {
                            "title": "必填字段",
                            "fields": [r for r in extraction_results if template_info.get("field_definitions", [])],
                            "status": "完整" if not missing_fields else f"缺失{len(missing_fields)}个"
                        },
                        {
                            "title": "置信度分析", 
                            "high_confidence": len([r for r in extraction_results if r["confidence"] >= 0.8]),
                            "medium_confidence": len([r for r in extraction_results if 0.5 <= r["confidence"] < 0.8]),
                            "low_confidence": len(low_confidence_fields)
                        }
                    ],
                    "options": [
                        {"value": "supplement", "label": f"补充信息 ({len(missing_fields + low_confidence_fields)}项)", "primary": True},
                        {"value": "continue", "label": "继续生成报告", "secondary": True},
                        {"value": "retry", "label": "重新提取", "secondary": True},
                        {"value": "batch_edit", "label": "批量编辑", "secondary": True}
                    ],
                    "quick_actions": [
                        {"value": "auto_fill", "label": "智能补全", "description": "使用AI自动补充缺失信息"},
                        {"value": "template_preview", "label": "预览报告", "description": "查看当前信息生成的报告预览"}
                    ]
                }
                state = add_human_interaction(state, "supplement", "data_extraction_review", None, interaction_data)
                state["next_action"] = "human_interaction"
            else:
                # 高质量提取，简化确认流程
                interaction_data = {
                    "type": "data_extraction_success",
                    "message": f"🎉 信息提取成功！质量等级: {quality_level} (成功率: {success_rate:.1%})",
                    "extraction_summary": {
                        "total_fields": total_fields,
                        "extracted_fields": extracted_fields,
                        "success_rate": success_rate,
                        "quality_level": quality_level,
                        "avg_confidence": sum(r["confidence"] for r in extraction_results) / len(extraction_results) if extraction_results else 0
                    },
                    "extraction_results": extraction_results,
                    "options": [
                        {"value": "generate", "label": "立即生成报告", "primary": True},
                        {"value": "review", "label": "检查修改", "secondary": True},
                        {"value": "save_draft", "label": "保存草稿", "secondary": True}
                    ],
                    "preview_available": True
                }
                state = add_human_interaction(state, "confirm", "extraction_success", None, interaction_data)
                state["next_action"] = "human_interaction"
            
            logger.info(f"[{state['workflow_id']}] 数据提取完成，成功率: {success_rate:.2%}")

            # 保存状态到持久化存储
            try:
                from services.workflow_state_service import workflow_state_service

                # 准备要保存的状态数据
                state_to_save = {
                    "workflow_id": state["workflow_id"],
                    "current_stage": state.get("current_stage"),
                    "next_action": state.get("next_action"),
                    "pending_interactions": state.get("pending_interactions", []),
                    "human_interactions": [
                        {
                            "interaction_type": hi.interaction_type if hasattr(hi, 'interaction_type') else hi.get('interaction_type'),
                            "field_name": hi.field_name if hasattr(hi, 'field_name') else hi.get('field_name'),
                            "original_value": hi.original_value if hasattr(hi, 'original_value') else hi.get('original_value'),
                            "user_input": hi.user_input if hasattr(hi, 'user_input') else hi.get('user_input'),
                            "user_choice": hi.user_choice if hasattr(hi, 'user_choice') else hi.get('user_choice'),
                            "timestamp": (hi.timestamp.isoformat() if hasattr(hi.timestamp, 'isoformat') else str(hi.timestamp)) if (hasattr(hi, 'timestamp') and hi.timestamp) else (hi.get('timestamp') if isinstance(hi, dict) else None),
                            "is_completed": hi.is_completed if hasattr(hi, 'is_completed') else hi.get('is_completed', False)
                        }
                        for hi in state.get("human_interactions", [])
                    ],
                    "extraction_results": extraction_results,
                    "extracted_data": state.get("extracted_data", {}),
                    "extraction_stats": state.get("extraction_stats", {}),
                    "template_info": state.get("template_info"),
                    "errors": state.get("errors", []),
                    "warnings": state.get("warnings", []),
                    "requires_user_action": len(state.get("pending_interactions", [])) > 0
                }

                success = workflow_state_service.save_workflow_state(state["workflow_id"], state_to_save)
                if success:
                    logger.info(f"[{state['workflow_id']}] 状态已保存到持久化存储")
                else:
                    logger.warning(f"[{state['workflow_id']}] 状态保存失败")

            except Exception as save_error:
                logger.error(f"[{state['workflow_id']}] 保存状态异常: {save_error}")

        except Exception as e:
            logger.error(f"[{state['workflow_id']}] 数据提取异常: {e}")
            state = add_error(state, "data_extraction_exception", str(e))
            state["next_action"] = "error_handling"

        return state
    
    @staticmethod
    async def human_interaction_node(state: WorkflowState) -> WorkflowState:
        """人工交互节点"""
        logger.info(f"[{state['workflow_id']}] 等待人工交互")
        
        try:
            state = update_state_stage(state, "human_interaction")
            
            # 检查是否有待处理的交互
            if state["pending_interactions"]:
                # 这里应该暂停工作流，等待前端用户输入
                # 在实际实现中，这个节点会被前端的用户操作触发继续
                logger.info(f"[{state['workflow_id']}] 等待用户处理: {state['pending_interactions']}")
                state["next_action"] = "wait_for_user"
            else:
                # 没有待处理的交互，继续下一步
                state["next_action"] = "data_validation"
            
        except Exception as e:
            logger.error(f"[{state['workflow_id']}] 人工交互异常: {e}")
            state = add_error(state, "human_interaction_exception", str(e))
            state["next_action"] = "error_handling"
        
        return state

    @staticmethod
    async def conversation_response_node(state: WorkflowState) -> WorkflowState:
        """处理对话交流类意图的响应节点 - 支持流式对话"""
        logger.info(f"[{state['workflow_id']}] 处理对话交流意图")

        try:
            state = update_state_stage(state, "conversation_response")

            intention_result = state.get("intention_result", {})
            user_text = state["user_input"]["text"]

            # 使用意图识别的结果作为响应
            user_response = intention_result.get("user_response", "您好！我是报告智能生成工具的AI助手，很高兴为您服务！")

            # 构建友好的对话响应
            response_message = {
                "type": "assistant_message",
                "content": user_response,
                "intent_type": intention_result.get("intent_type", "conversation"),
                "confidence": intention_result.get("confidence", 0.0),
                "suggestions": intention_result.get("suggestions", []),
                "timestamp": datetime.now().isoformat(),
                "requires_user_action": not state["user_input"]["files"] and ("会议纪要" in user_text or "会议" in user_text)
            }

            # 将响应添加到状态中
            if "assistant_responses" not in state:
                state["assistant_responses"] = []
            state["assistant_responses"].append(response_message)

            # 根据情况决定下一步
            if state.get("next_action") != "template_identification":
                state["next_action"] = "workflow_end"
                state["status"] = "completed_with_conversation"

            logger.info(f"[{state['workflow_id']}] 对话交流意图处理完成")

        except Exception as e:
            logger.error(f"[{state['workflow_id']}] 对话响应处理异常: {e}")
            state = add_error(state, "conversation_exception", str(e))
            state["next_action"] = "error_handling"

        return state

    @staticmethod
    async def non_text_generation_response_node(state: WorkflowState) -> WorkflowState:
        """处理非文本生成意图的响应节点"""
        logger.info(f"[{state['workflow_id']}] 处理非文本生成意图")

        try:
            state = update_state_stage(state, "non_text_generation_response")

            intention_result = state.get("intention_result", {})
            user_response = intention_result.get("user_response", "抱歉，我只能帮助您生成报告。如果您需要生成报告，请告诉我具体的报告类型。")

            # 构建友好的拒绝响应
            response_message = {
                "type": "assistant_message",
                "content": user_response,
                "intent_type": intention_result.get("intent_type", "other"),
                "confidence": intention_result.get("confidence", 0.0),
                "suggestions": intention_result.get("suggestions", []),
                "timestamp": datetime.now().isoformat()
            }

            # 将响应添加到状态中
            if "assistant_responses" not in state:
                state["assistant_responses"] = []
            state["assistant_responses"].append(response_message)

            # 设置工作流结束
            state["next_action"] = "workflow_end"
            state["status"] = "completed_with_rejection"

            logger.info(f"[{state['workflow_id']}] 非文本生成意图处理完成")

        except Exception as e:
            logger.error(f"[{state['workflow_id']}] 非文本生成响应处理异常: {e}")
            state = add_error(state, "non_text_generation_exception", str(e))
            state["next_action"] = "error_handling"

        return state

    @staticmethod
    async def error_handling_node(state: WorkflowState) -> WorkflowState:
        """错误处理节点"""
        logger.info(f"[{state['workflow_id']}] 处理错误")
        
        try:
            state = update_state_stage(state, "error_handling")
            
            # 检查重试次数
            if state["retry_count"] < state["max_retries"]:
                state["retry_count"] += 1
                # 根据错误类型决定重试策略
                last_error = state["errors"][-1] if state["errors"] else None
                if last_error:
                    error_type = last_error["type"]
                    if error_type in ["intention_llm_error", "data_extraction_exception"]:
                        # 可以重试的错误
                        state["next_action"] = "intention_analysis"
                        logger.info(f"[{state['workflow_id']}] 准备重试，当前重试次数: {state['retry_count']}")
                    else:
                        # 需要人工干预的错误
                        state = add_human_interaction(state, "correct", "error_correction")
                        state["next_action"] = "human_interaction"
                else:
                    state["next_action"] = "workflow_end"
            else:
                # 超过最大重试次数
                logger.error(f"[{state['workflow_id']}] 超过最大重试次数，工作流结束")
                state["next_action"] = "workflow_end"
            
        except Exception as e:
            logger.error(f"[{state['workflow_id']}] 错误处理异常: {e}")
            state["next_action"] = "workflow_end"
        
        return state
