/**
 * 数据管理页面
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Upload,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Statistic,
  Tabs,
  Tree,
  Tooltip
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UploadOutlined,
  DatabaseOutlined,
  EyeOutlined,
  DownloadOutlined,
  FileTextOutlined,
  FolderOutlined,
  SearchOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { TabPane } = Tabs;

interface ExtractedData {
  id: string;
  name: string;
  templateId: string;
  templateName: string;
  status: 'extracted' | 'validated' | 'confirmed';
  fieldCount: number;
  completeness: number;
  createdAt: Date;
  updatedAt: Date;
  sourceFiles: string[];
}

interface KeyDefinition {
  id: string;
  key: string;
  description: string;
  example: string;
  format: string;
  defaultValue: string;
  length: number;
  source: string;
  required: boolean;
  templateId: string;
  parentKey?: string;
  children?: KeyDefinition[];
}

const DataManagement: React.FC = () => {
  const [extractedData, setExtractedData] = useState<ExtractedData[]>([]);
  const [keyDefinitions, setKeyDefinitions] = useState<KeyDefinition[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingData, setEditingData] = useState<ExtractedData | null>(null);
  const [activeTab, setActiveTab] = useState('extracted');
  const [form] = Form.useForm();

  // 模拟数据
  useEffect(() => {
    const mockExtractedData: ExtractedData[] = [
      {
        id: '1',
        name: '2024年1月15日会议数据',
        templateId: '1',
        templateName: '数字科技中心会议纪要',
        status: 'confirmed',
        fieldCount: 15,
        completeness: 95,
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-01-15'),
        sourceFiles: ['会议记录.docx', '参会人员名单.txt']
      },
      {
        id: '2',
        name: '项目预审会数据',
        templateId: '2',
        templateName: '项目预审会纪要',
        status: 'validated',
        fieldCount: 12,
        completeness: 88,
        createdAt: new Date('2024-01-10'),
        updatedAt: new Date('2024-01-12'),
        sourceFiles: ['预审材料.docx']
      },
      {
        id: '3',
        name: '技术评审数据',
        templateId: '3',
        templateName: '技术评审报告',
        status: 'extracted',
        fieldCount: 20,
        completeness: 72,
        createdAt: new Date('2024-01-08'),
        updatedAt: new Date('2024-01-08'),
        sourceFiles: ['技术方案.docx', '评审意见.txt']
      }
    ];

    const mockKeyDefinitions: KeyDefinition[] = [
      {
        id: '1',
        key: 'meeting_title',
        description: '会议标题',
        example: '数字科技中心2024年第1次会议',
        format: 'string',
        defaultValue: '',
        length: 100,
        source: 'document',
        required: true,
        templateId: '1'
      },
      {
        id: '2',
        key: 'meeting_date',
        description: '会议日期',
        example: '2024年1月15日',
        format: 'date',
        defaultValue: '',
        length: 20,
        source: 'document',
        required: true,
        templateId: '1'
      },
      {
        id: '3',
        key: 'attendees',
        description: '参会人员',
        example: '张三、李四、王五',
        format: 'array',
        defaultValue: '',
        length: 500,
        source: 'document',
        required: true,
        templateId: '1',
        children: [
          {
            id: '3-1',
            key: 'attendee_name',
            description: '参会人员姓名',
            example: '张三',
            format: 'string',
            defaultValue: '',
            length: 50,
            source: 'document',
            required: true,
            templateId: '1',
            parentKey: 'attendees'
          }
        ]
      }
    ];

    setExtractedData(mockExtractedData);
    setKeyDefinitions(mockKeyDefinitions);
  }, []);

  const extractedDataColumns = [
    {
      title: '数据名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: ExtractedData) => (
        <Space>
          <DatabaseOutlined style={{ color: '#1890ff' }} />
          <span style={{ fontWeight: 500 }}>{text}</span>
        </Space>
      ),
    },
    {
      title: '关联模板',
      dataIndex: 'templateName',
      key: 'templateName',
      render: (templateName: string) => <Tag color="blue">{templateName}</Tag>,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusConfig = {
          extracted: { color: 'orange', text: '已提取' },
          validated: { color: 'blue', text: '已验证' },
          confirmed: { color: 'green', text: '已确认' }
        };
        const config = statusConfig[status as keyof typeof statusConfig];
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '字段数量',
      dataIndex: 'fieldCount',
      key: 'fieldCount',
      render: (count: number) => <Text type="secondary">{count} 个字段</Text>,
    },
    {
      title: '完整度',
      dataIndex: 'completeness',
      key: 'completeness',
      render: (completeness: number) => (
        <span style={{ color: completeness >= 90 ? '#52c41a' : completeness >= 70 ? '#faad14' : '#ff4d4f' }}>
          {completeness}%
        </span>
      ),
    },
    {
      title: '源文件',
      dataIndex: 'sourceFiles',
      key: 'sourceFiles',
      render: (files: string[]) => (
        <div>
          {files.map((file, index) => (
            <Tag key={index} icon={<FileTextOutlined />} style={{ margin: '2px' }}>
              {file}
            </Tag>
          ))}
        </div>
      ),
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (date: Date) => date.toLocaleDateString('zh-CN'),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: ExtractedData) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewData(record)}
          >
            查看
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditData(record)}
          >
            编辑
          </Button>
          <Button
            type="text"
            icon={<DownloadOutlined />}
            onClick={() => handleExportData(record)}
          >
            导出
          </Button>
          <Popconfirm
            title="确定要删除这个数据吗？"
            onConfirm={() => handleDeleteData(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleViewData = (data: ExtractedData) => {
    message.info(`查看数据: ${data.name}`);
  };

  const handleEditData = (data: ExtractedData) => {
    setEditingData(data);
    form.setFieldsValue(data);
    setModalVisible(true);
  };

  const handleExportData = (data: ExtractedData) => {
    message.success(`导出数据: ${data.name}`);
  };

  const handleDeleteData = (id: string) => {
    setExtractedData(prev => prev.filter(d => d.id !== id));
    message.success('数据删除成功');
  };

  const handleAddData = () => {
    setEditingData(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();

      if (editingData) {
        // 编辑数据
        setExtractedData(prev => prev.map(d =>
          d.id === editingData.id
            ? { ...d, ...values, updatedAt: new Date() }
            : d
        ));
        message.success('数据更新成功');
      } else {
        // 新增数据
        const newData: ExtractedData = {
          id: Date.now().toString(),
          ...values,
          status: 'extracted',
          fieldCount: 0,
          completeness: 0,
          createdAt: new Date(),
          updatedAt: new Date(),
          sourceFiles: []
        };
        setExtractedData(prev => [...prev, newData]);
        message.success('数据创建成功');
      }

      setModalVisible(false);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleModalCancel = () => {
    setModalVisible(false);
  };

  // 构建关键字段定义树形数据
  const buildTreeData = (definitions: KeyDefinition[]) => {
    return definitions.filter(def => !def.parentKey).map(def => ({
      title: (
        <Space>
          <Text strong>{def.key}</Text>
          <Text type="secondary">({def.description})</Text>
          {def.required && <Tag color="red" size="small">必填</Tag>}
        </Space>
      ),
      key: def.id,
      icon: def.children ? <FolderOutlined /> : <FileTextOutlined />,
      children: def.children ? def.children.map(child => ({
        title: (
          <Space>
            <Text>{child.key}</Text>
            <Text type="secondary">({child.description})</Text>
            {child.required && <Tag color="red" size="small">必填</Tag>}
          </Space>
        ),
        key: child.id,
        icon: <FileTextOutlined />
      })) : undefined
    }));
  };

  // 统计数据
  const stats = {
    total: extractedData.length,
    confirmed: extractedData.filter(d => d.status === 'confirmed').length,
    validated: extractedData.filter(d => d.status === 'validated').length,
    avgCompleteness: Math.round(extractedData.reduce((sum, d) => sum + d.completeness, 0) / extractedData.length)
  };

  return (
    <div>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic title="总数据量" value={stats.total} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="已确认数据" value={stats.confirmed} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="已验证数据" value={stats.validated} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="平均完整度" value={stats.avgCompleteness} suffix="%" />
          </Card>
        </Col>
      </Row>

      {/* 数据管理标签页 */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <span>
                <DatabaseOutlined />
                提取数据
              </span>
            }
            key="extracted"
          >
            <div style={{ marginBottom: '16px' }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddData}
              >
                新增数据
              </Button>
            </div>

            <Table
              columns={extractedDataColumns}
              dataSource={extractedData}
              rowKey="id"
              loading={loading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条数据`,
              }}
            />
          </TabPane>

          <TabPane
            tab={
              <span>
                <SearchOutlined />
                字段定义
              </span>
            }
            key="definitions"
          >
            <Card title="关键字段定义" size="small">
              <Tree
                showIcon
                defaultExpandAll
                treeData={buildTreeData(keyDefinitions)}
                style={{ background: '#fafafa', padding: '16px', borderRadius: '6px' }}
              />
            </Card>
          </TabPane>
        </Tabs>
      </Card>

      {/* 新增/编辑数据弹窗 */}
      <Modal
        title={editingData ? '编辑数据' : '新增数据'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            status: 'extracted'
          }}
        >
          <Form.Item
            name="name"
            label="数据名称"
            rules={[{ required: true, message: '请输入数据名称' }]}
          >
            <Input placeholder="请输入数据名称" />
          </Form.Item>

          <Form.Item
            name="templateId"
            label="关联模板"
            rules={[{ required: true, message: '请选择关联模板' }]}
          >
            <Select placeholder="请选择关联模板">
              <Select.Option value="1">数字科技中心会议纪要</Select.Option>
              <Select.Option value="2">项目预审会纪要</Select.Option>
              <Select.Option value="3">技术评审报告</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select>
              <Select.Option value="extracted">已提取</Select.Option>
              <Select.Option value="validated">已验证</Select.Option>
              <Select.Option value="confirmed">已确认</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="sourceFiles"
            label="源文件"
          >
            <Upload
              multiple
              beforeUpload={() => false}
              fileList={[]}
            >
              <Button icon={<UploadOutlined />}>选择源文件</Button>
            </Upload>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default DataManagement;
