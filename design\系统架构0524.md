# 项目架构设计

## 1. 项目基础结构

text

Apply to architect.md

**report_gen/**

**├── backend/                    # 后端主目录**

**│   ├── chaoxian/              # 核心应用代码**

**│   │   ├── __init__.py**

**│   │   ├── app.py            # Flask应用入口**

**│   │   ├── config.py         # 配置文件**

**│   │   ├── extensions.py     # Flask扩展初始化**

**│   │   ├── security/         # 安全相关**

**│   │   │   ├── manager.py    # 安全管理器**

**│   │   │   └── views.py      # 安全视图**

**│   │   ├── models/          # 数据模型**

**│   │   │   ├── core.py      # 核心模型**

**│   │   │   ├── project.py   # 项目模型**

**│   │   │   ├── template.py  # 模板模型**

**│   │   │   ├── data.py      # 数据模型**

**│   │   │   └── report.py    # 报告模型**

**│   │   ├── views/           # 视图**

**│   │   │   ├── core.py      # 核心视图**

**│   │   │   ├── project.py   # 项目视图**

**│   │   │   ├── template.py  # 模板视图**

**│   │   │   ├── data.py      # 数据视图**

**│   │   │   └── report.py    # 报告视图**

**│   │   ├── api/             # API接口**

**│   │   │   └── v1/         # API版本1**

**│   │   ├── tasks/          # Celery任务**

**│   │   │   ├── core.py     # 核心任务**

**│   │   │   ├── template.py # 模板分析任务**

**│   │   │   ├── extract.py  # 信息抽取任务**

**│   │   │   └── report.py   # 报告生成任务**

**│   │   ├── utils/          # 工具函数**

**│   │   │   ├── core.py     # 核心工具**

**│   │   │   ├── docx.py     # Word文档处理**

**│   │   │   ├── extract.py  # 信息抽取**

**│   │   │   └── ai.py       # AI模型集成**

**│   │   └── static/         # 静态资源**

**│   ├── tests/              # 测试目录**

**│   └── requirements/       # 依赖管理**

**├── frontend/               # 前端主目录**

**│   ├── src/**

**│   │   ├── components/    # 组件**

**│   │   │   ├── common/    # 通用组件**

**│   │   │   ├── project/   # 项目组件**

**│   │   │   ├── template/  # 模板组件**

**│   │   │   ├── data/      # 数据组件**

**│   │   │   └── report/    # 报告组件**

**│   │   ├── layouts/       # 布局组件**

**│   │   ├── pages/         # 页面**

**│   │   ├── services/      # API服务**

**│   │   ├── models/        # 数据模型**

**│   │   ├── utils/         # 工具函数**

**│   │   └── assets/        # 静态资源**

**│   ├── public/            # 公共资源**

**│   └── config/            # 配置文件**

**└── docker/                # Docker配置**

**    ├── backend/**

**    │   └── Dockerfile**

**    ├── frontend/**

**    │   └── Dockerfile**

**    └── docker-compose.yml**

## 2. 后端架构（保持与Superset类似的结构）

backend/my_platform/
├── api/                    # API接口
│   ├── v1/                # API版本1
│   └── __init__.py
├── commands/              # CLI命令
├── dao/                   # 数据访问对象
├── models/               # 数据模型
├── security/             # 安全相关
│   ├── manager.py
│   └── views.py
├── views/                # 视图
├── common/              # 通用功能
├── db_engine_specs/     # 数据库引擎
├── translations/        # 国际化
└── utils/              # 工具函数

## 3. 技术栈说明

### 后端技术栈

* Python 3.12
* Flask 框架
* SQLAlchemy ORM
* Alembic 数据库迁移
* Celery 任务队列
* Redis 缓存
* JWT 认证
* OpenAPI/Swagger 文档

### 前端技术栈（新）

* React 18+
* TypeScript 4+
* Ant Design Pro
* Redux Toolkit
* React Query
* Vite
* TailwindCSS

### 数据库支持

* PostgreSQL（主数据库）
* Redis（缓存和会话）
* 支持多种数据源连接（与Superset类似）

## 4. 开发规范

现在，让我创建一个专门的Cursor rules文件，用于规范项目开发：

.cursor-rules.json+111-1

---

**{**

**  **"version"**: **"1.0"**,**

**  **"project"**: **{

**    **"name"**: **"my-platform"**,**

**    **"description"**: **"Enterprise Data Platform based on Superset archit**ecture"**

**  **}**,**

**  **"rules"**: **{

**    **"general"**: **{

**      **"encoding"**: **"utf-8"**,**

**      **"line_ending"**: **"lf"**,**

**      **"indent_style"**: **"space"**,**

**      **"indent_size"**: **4**,**

**      **"max_line_length"**: **100

**    **}**,**

现在让我们创建一个基础的项目配置文件：

pyproject.toml+48-2

**# under the License.**

**[build-system]**

**requires = ["setuptools>=40.9.0", "wheel"]**

**build-backend = "setuptools.build_meta"**

**[project]**

**name = "apache-superset"**

**# TODO REMOVE THESE DEPS FROM CODEBASE**

**paramiko = "3"  # GPL**

**pyxlsb = "1"  # GPL**

**requires = ["**setuptools>=40.9**.0"**, "wheel"**]**

**build-backend = "**setuptools.build_meta**"**

**# under the License.**

**[build-system]**

**requires = ["poetry-core>=1.0.0"]**

**build-backend = "poetry.core.masonry.api"**

**[project]**

**name = "apache-superset"**

**# TODO REMOVE THESE DEPS FROM CODEBASE**

**paramiko = "3"  # GPL**

**pyxlsb = "1"  # GPL**

让我们创建前端项目的配置文件：

package.json+61-1

---

**{**

**  **"name"**: **"my-platform-frontend"**,**

**  **"version"**: **"0.1.0"**,**

**  **"private"**: **true**,**

**  **"scripts"**: **{

**    **"dev"**: **"vite"**,**

**    **"build"**: **"tsc && vite build"**,**

**    **"preview"**: **"vite preview"**,**

**    **"test"**: **"jest"**,**

**    **"lint"**: **"eslint src --ext ts,tsx"**,**

**    **"lint:fix"**: **"eslint src --ext ts,tsx --fix"**,**

**    **"format"**: **"prettier --write **\"**src/**/*.{ts,tsx,css,less}**\"**"**,**

**    **"prepare"**: **"husky install"

**  **}**,**

最后，让我们创建一个Docker Compose配置文件：

docker-compose.yml+4-4

**    **image**: **nginx:latest

**    **# 为容器指定一个名称，便于在 Docker 中管理和识别，这里命名为 **superset_nginx。**

**    **container_name**: **superset_nginx

**    **# 设置容器的重启策略为"除非手动停止"，即在容器异常退出时自动重启。

**    **restart**: **unless-stopped

**    **# 配置容器的端口映射，将主机的 80 端口映射到容器的 80 端口，用于处理 HTTP 请求。

**    **ports**:**

**    **image**: **redis:7

**    **# 为容器指定一个名称，便于在 Docker 中管理和识别，这里命名为 **superset_cache。**

**    **container_name**: **superset_cache

**    **# 设置容器的重启策略为"除非手动停止"，即在容器异常退出时自动重启。

19 hidden lines

24 hidden lines

25 hidden lines

现在我已经为您创建了一个完整的项目脚手架结构。这个结构保持了Superset的后端架构，同时使用了新的现代化前端技术栈。以下是关键文件的说明：

1. .cursor-rules.json：

* 定义了项目的编码规范
* 包含Python和TypeScript的代码风格规则
* 定义了Git提交规范
* 设置了测试和文档要求
* 指定了必要的开发工具和扩展

1. pyproject.toml：

* 使用Poetry进行Python依赖管理
* 设置了项目的基本信息和依赖
* 配置了开发工具（black, flake8, mypy等）
* 定义了测试配置

1. frontend/package.json：

* 使用现代化的React技术栈
* 配置了TypeScript支持
* 设置了代码质量工具
* 包含了必要的开发依赖

1. docker-compose.yml：

* 定义了完整的开发环境
* 包含了所有必要的服务（后端、前端、数据库、Redis、Celery）
* 配置了服务间的网络连接
* 设置了数据持久化

## 开始使用

1. 克隆项目后，首先安装后端依赖：

   bash

   Apply to architect.md

   Run

   **cd** **backend**

   **poetry** **install**
2. 安装前端依赖：

   bash

   Apply to architect.md

   Run

   **cd** **frontend**

   **npm** **install**
3. 启动开发环境：

   bash

   Apply to architect.md

   Run

   **cd** **backend**

   **poetry** **install**

## 开发建议

1. 后端开发：

* 遵循Flask工厂模式创建应用
* 使用Blueprint组织路由
* 保持与Superset相似的目录结构
* 使用类型注解
* 编写单元测试

1. 前端开发：

* 使用Ant Design Pro的最佳实践
* 实现组件的TypeScript类型
* 使用React Query进行数据获取
* 采用模块化的状态管理

1. 代码质量：

* 提交前运行linter和formatter
* 保持测试覆盖率
* 遵循文档规范
