数字科技中心会议纪要
# meeting_title
description:会议的名称，一次会议只有一个名称，20字内
example:5月月报项目进度评审会
format:普通文本
default:"<无会议名称>"
length：50
source：上传内容
must:true
# meeting_time
description:会议召开的时间，为北京时间
example:2025年6月26日（星期四）上午9:00
format:普通文本
default:当前系统时间
length：50
source：上传内容或系统时间
must:true
# meeting_location
description:会议召开的地点，一般为某处会议室
example:广州地铁设计大厦0513会议室（白云区云城北二路129号） 
format:普通文本
default:"<无会议地点>"
length：100
source：上传内容
must:true
# meeting_theme
description:议题，会上讨论了什么内容，一句或一段话，50字内
example:项目开展情况检查
format:普通文本
default:"<无会议议题>"
length:50
source:上传内容
must:true
# meeting_host
description:会议主持人是谁？一般为人名
example:谢特赐
format:人名
default:"<无会议主持人>"
length:12
source:上传内容
must:true
# meeting_recorder
description:会议记录人是谁？一般为人名
example:谢特赐
format:人名
default:"<无会议记录人>"
length:12
source:上传内容
must:true
# symmary_receiver
description:谁应该接受这个会议纪要，如果是人名，可以有多个人，每个人之间用、隔开或者直接注明所有参会人员
example:所有参会人员
format:人名列表或组织代号
default:"<所有参会人员>"
length:12
source:上传内容
must:true
# attend
description:表格数据，参加部门和参加人，会有多组数据
example:数字科技中心，桂林、李远峰、赵梓茗 
format:表格行
default:""
length:100
source:上传内容
must:true
## dep
    description:参加会议的部门有哪些？和参会部门成员放一起，并列放到表格中
    example:数字科技中心
    format:行单元格
    default:""
    length:20
    source:上传内容
    must:true
## person
    description:参加会议的人员有哪些？和参会部分并列，放到表格中
    example:桂林、李远峰、赵梓茗
    format:行单元格
    default:""
    length:20
    source:上传内容
    must:true
# detail
description:针对议题，分别得到了哪些结论，每个细节议题单独描述，由子议题名称，当前问题，解决计划，责任人，完成时间五个部分组成
example:
项目3：基于BIM的轨道交通消防智能审查平台研发
当前问题：研究报告需优化排版，内容结构需调整。 
解决计划：
（1）按照消防审查平台修改建议进行优化； 
（2）统一封面内容和结构； 
（3）浓缩工作总结为一页（主要完成内容、效益和提升、建议与后续展望）； 
（4）优化效益和应用情况合并描述； 
（5）调整考核指标位置并补充对照表； 
（6）增强效益部分内容，提炼分维度描述； 
（7）创新点缩减为3个，明确性能指标及效果； 
（8）每个应用案例单独一页，图文结合。 
责任人：桂林 
完成时间：2025年6月30日（星期一） 
format:段落
default:""
length:500
source:上传内容
must:true
## title
    description:具体的子议题名称
    example:项目3：基于BIM的轨道交通消防智能审查平台研发
    format:单独行
    default:""
    length:50
    source:上传内容
    must:true
## problem
    description:对于子议题，当前存在的问题是什么？可有多个问题，每个问题占一行，前面需要列上编号（1）,（2）...,尾部加；号，问题结束加。号
    example:研究报告需优化排版，内容结构需调整。 
    format:单独行
    default:""
    length:100
    source:上传内容
    must:true
## actions
    description:针对子议题存在的问题，具体采取哪些措施？可有多个措施，每个措施占一行，前面需要列上编号（1）,（2）...,尾部加；号，措施结束加。号
    example:
（1）按照消防审查平台修改建议进行优化； 
（2）统一封面内容和结构； 
（3）浓缩工作总结为一页（主要完成内容、效益和提升、建议与后续展望）； 
（4）优化效益和应用情况合并描述； 
（5）调整考核指标位置并补充对照表； 
（6）增强效益部分内容，提炼分维度描述； 
（7）创新点缩减为3个，明确性能指标及效果； 
（8）每个应用案例单独一页，图文结合。 
    format:表格行
    default:""
    length:500
    source:上传内容
    must:true
## person
    description:采取措施的负责人是谁？可以有多个人，每个人之间用、隔开，或组织代号，如某某项目组
    example:桂林、李远峰、赵梓茗 
    format:普通文本
    default:""
    length:30
    source:上传内容
    must:true
## time
    description:解决这些问题的截止时间是什么时候？
    example:数字科技中心
    format:表格行
    default:会议召开时间的一个星期后
    length:30
    source:上传内容
# other
description:会议还有哪些其他安排，一段话描述，每个安排一句话，形成一段话，可以没有
example:
各项目组需尽快落实修改意见，确保验收材料质量； 
科技质量部将跟进后续进展并提供必要支持； 
若有疑问可联系苏拓（13022063916）。 
format:段落
default:""
length:100
source:上传内容
must:false
