# 超限报告智能生成工具 - 操作流程与智能体设计

**版本**: V2.0
**创建日期**: 2025年6月26日
**更新日期**: 2025年6月26日
**设计目标**: 基于LangGraph的智能报告生成流程设计（基于样例：数字科技中心会议纪要）

---

## 📋 1. 系统文件结构与管理

### 1.1 文件类型定义

基于样例文件夹 `sample_report/数字科技中心会议纪要`，系统管理以下5类文件：

#### 1.1.1 输入文件 (input_*)
- **定义**: 用户需要输入的原始内容文件
- **格式**: doc/docx/txt/pdf等
- **示例**: `1.input_住建厅项目预审会会议通知.docx`, `1.input_现场会议记录.txt`
- **管理**: 用户上传，系统临时存储，处理完成后可选择保留或删除

#### 1.1.2 模板文件 (template_*)
- **定义**: 报告生成的格式模板，包含占位符
- **格式**: docx格式，包含{{字段名}}占位符
- **示例**: `4.template_数字科技中心会议纪要模板.docx`
- **管理**: 系统永久存储，支持版本管理，用户可浏览、选择、上传新模板

#### 1.1.3 关键数据定义文件 (key_*)
- **定义**: 定义各关键字段的要求和规范
- **格式**: markdown格式，结构化字段定义
- **示例**: `2.key_definitions.md`
- **管理**: 与模板配套使用，系统永久存储，支持编辑和版本管理

#### 1.1.4 抽取数据文件 (extracted_*)
- **定义**: 智能体自动提取的结构化数据
- **格式**: markdown格式，按字段组织
- **示例**: `3.extracted_keys.md`
- **管理**: 系统生成，用户可浏览、确认、修改，支持人工补充

#### 1.1.5 输出文件 (output_*)
- **定义**: 最终生成的报告文件
- **格式**: docx格式，完整的报告文档
- **示例**: `5.output_最终生成效果.docx`
- **管理**: 系统生成，用户可预览、下载、重新生成

### 1.2 字段定义规范

基于 `key_definitions.md` 的结构，每个字段包含以下属性：

```markdown
# 字段名
description: 字段描述，用于嵌入在提示词中进行信息提取
example: 字段样例，用于嵌入在提示词中进行信息提取
format: 字段类型（普通文本/表格行/段落/人名/单独行等）
default: 默认值，有些有默认值，有些默认值为""
length: 篇幅限制
source: 信息来源（上传内容/系统时间/既有知识/外部搜索）
must: 是否必须字段（true/false）
```

**支持的字段类型**:
- `普通文本`: 单行文本内容
- `表格行`: 表格中的一行数据
- `段落`: 多行文本段落
- `人名`: 人员姓名
- `单独行`: 独立的一行内容
- `行单元格`: 表格中的单个单元格

**信息来源类型**:
- `上传内容`: 从用户上传的文件中提取
- `系统时间`: 使用当前系统时间
- `既有知识`: 从系统知识库获取
- `外部搜索`: 通过搜索引擎获取并由大模型整合

---

## 🔄 2. 核心业务流程设计

### 2.1 整体业务流程
```
模板选择 → 文档上传 → 智能抽取 → 完整性检查 → 人工确认 → 生成确认 → 报告生成 → 输出管理
```

### 2.2 详细阶段分解

#### 阶段1：模板选择阶段
**目标**: 用户选择合适的报告模板
**流程**:
1. 系统展示所有可用模板列表
2. 每个模板显示：
   - 模板名称和描述
   - 模板预览图
   - 字段数量和复杂度
   - 使用次数和评分
   - 最后更新时间
3. 用户点击查看模板详情：
   - 完整模板预览
   - 字段定义列表
   - 样例输出效果
4. 用户确认选择模板

**关键决策点**:
- 模板分类和搜索机制
- 模板推荐算法
- 模板状态管理（启用/禁用）

#### 阶段2：文档上传阶段
**目标**: 收集用户提供的原始信息文件
**流程**:
1. 系统显示当前选择的模板信息
2. 用户上传输入文件（input_*）：
   - 支持格式：doc/docx/txt/pdf
   - 支持批量上传多个文件
   - 实时显示上传进度
3. 系统对文档进行预处理：
   - 文件格式检测和转换
   - 文本内容提取和编码标准化
   - 文档结构分析（标题、段落、表格、列表）
   - 元数据提取（文件名、创建时间、大小等）
4. 系统显示文档预览：
   - 文件列表和基本信息
   - 文本内容预览
   - 质量评估结果
5. 用户确认文档质量，可删除或重新上传

**关键决策点**:
- 文件大小和数量限制
- 文档质量评估标准
- 预处理失败的处理策略

#### 阶段3：智能抽取阶段（LangGraph核心）
**目标**: 使用AI智能体从文档中抽取模板所需的信息
**流程**:
1. 系统加载模板对应的字段定义文件（key_definitions.md）
2. 启动智能抽取工作流，按字段逐一处理：
   - 根据字段的 `description` 和 `example` 构建提示词
   - 根据字段的 `source` 确定信息来源：
     * `上传内容`: 从用户文档中抽取
     * `系统时间`: 使用当前时间
     * `既有知识`: 从知识库获取
     * `外部搜索`: 调用搜索引擎并整合
   - 根据字段的 `format` 确定抽取策略
   - 计算抽取结果的置信度分数
3. 生成抽取数据文件（extracted_keys.md）
4. 实时显示抽取进度：
   - 当前处理字段
   - 已完成/总字段数
   - 成功抽取的字段列表
   - 失败或低置信度字段列表

**关键决策点**:
- 不同信息来源的处理优先级
- 置信度阈值设定（建议：高>0.8，中0.5-0.8，低<0.5）
- 抽取失败时的重试策略

#### 阶段4：完整性检查阶段
**目标**: 检查抽取结果的完整性，确保必填字段完整
**流程**:
1. 系统自动检查抽取结果：
   - 检查所有 `must: true` 字段是否有值
   - 检查字段值是否符合 `length` 限制
   - 检查字段值是否符合 `format` 要求
2. 生成完整性报告：
   - ✅ 已成功抽取的字段（显示值和置信度）
   - ⚠️ 已抽取但置信度较低的字段
   - ❌ 未抽取到的必填字段
   - ℹ️ 未抽取到的可选字段
3. 对于缺失的必填字段，系统提示用户：
   - 使用默认值（如果有 `default` 值）
   - 手动输入内容
   - 上传补充文件让系统重新抽取
4. 显示完整性检查结果统计

#### 阶段5：人工确认阶段
**目标**: 用户确认和修正抽取结果
**流程**:
1. 系统展示抽取结果确认界面：
   - 按字段分组显示（基本信息、参会人员、会议详情等）
   - 每个字段显示：字段名、抽取值、置信度、原文来源
   - 不同状态用不同颜色标识
2. 用户逐项确认：
   - 点击查看原文上下文
   - 确认抽取内容的准确性
   - 直接编辑修改错误内容
   - 为空字段补充信息
3. 对于必填字段，用户必须选择：
   - 确认当前值
   - 修改为其他值
   - 使用默认值（如果有）
4. 系统实时更新完整性状态
5. 所有必填字段确认后，启用"下一步"按钮

**关键决策点**:
- 必填字段的强制确认机制
- 批量确认功能的设计
- 修改历史的记录和回滚

#### 阶段6：生成确认阶段
**目标**: 用户确认是否按照选定模板生成报告
**流程**:
1. 系统显示生成前确认页面：
   - 选择的模板信息（名称、预览图）
   - 数据完整性摘要（已填充字段数/总字段数）
   - 关键字段值预览（会议标题、时间、地点等）
   - 预估生成时间
2. 用户确认生成选项：
   - 确认使用当前模板
   - 确认使用当前数据
   - 选择输出格式（docx/pdf）
   - 设置文件名称
3. 用户点击"开始生成"按钮
4. 系统记录生成任务并开始处理

#### 阶段7：报告生成阶段
**目标**: 基于确认的数据和模板生成最终报告
**流程**:
1. 生成智能体开始报告生成：
   - 加载模板文件（template_*.docx）
   - 将抽取数据按字段名映射到模板占位符
   - 处理不同格式的字段：
     * 普通文本：直接替换
     * 表格行：动态生成表格行
     * 段落：保持段落格式
     * 列表：生成有序/无序列表
   - 保持原模板的样式和格式
2. 质检智能体进行质量检查：
   - 占位符替换完整性检查
   - 格式一致性检查
   - 内容逻辑性检查
   - 文档结构完整性检查
3. 生成输出文件（output_*.docx）
4. 系统提供生成结果：
   - 生成成功/失败状态
   - 文件预览
   - 下载链接
   - 质量评分

**关键决策点**:
- 模板占位符的匹配策略
- 动态内容的格式处理
- 生成失败时的错误处理

#### 阶段8：输出管理阶段
**目标**: 管理生成的报告文件，支持预览、下载、重新生成
**流程**:
1. 系统将生成的报告添加到输出文件管理：
   - 文件基本信息（名称、大小、生成时间）
   - 使用的模板和数据来源
   - 质量评分和生成日志
2. 用户可以进行以下操作：
   - 在线预览报告内容
   - 下载报告文件
   - 基于相同数据重新生成
   - 修改数据后重新生成
   - 删除不需要的报告
3. 系统提供报告管理功能：
   - 报告列表和搜索
   - 报告分类和标签
   - 报告分享和权限控制

---

## 🤖 3. LangGraph智能体循环设计

### 3.1 智能体架构概述

**核心理念**: 基于字段定义的多智能体协作，每个智能体专注特定任务，通过状态传递和循环优化实现高质量的信息抽取和报告生成

**智能体列表**:
1. **文档预处理智能体** (DocumentProcessor Agent)
2. **字段抽取智能体** (FieldExtractor Agent)
3. **外部搜索智能体** (ExternalSearch Agent)
4. **数据验证智能体** (DataValidator Agent)
5. **报告生成智能体** (ReportGenerator Agent)
6. **质量检查智能体** (QualityChecker Agent)

### 3.2 智能体详细设计

#### 3.2.1 文档预处理智能体 (DocumentProcessor Agent)
**职责**: 处理用户上传的输入文件，转换为可分析的结构化内容
**输入**: 用户上传的input_*文件
**输出**: 结构化的文档内容和元数据

**核心功能**:
- 文件格式检测和转换（doc/docx/pdf/txt → 统一文本格式）
- 文本清洗和标准化（去除特殊字符、统一编码、格式化）
- 文档结构分析（识别标题、段落、表格、列表、时间、人名等）
- 元数据提取（文件名、创建时间、大小、作者等）
- 内容分块和索引（为后续精确定位和引用做准备）

**处理策略**:
- 保留原始格式信息和位置映射
- 建立内容索引，支持快速检索
- 异常文档的容错处理和质量评估

#### 3.2.2 字段抽取智能体 (FieldExtractor Agent)
**职责**: 基于字段定义从文档中抽取所需信息
**输入**: 结构化文档内容 + 字段定义文件(key_definitions.md)
**输出**: 抽取结果文件(extracted_keys.md) + 置信度评分

**核心功能**:
- 解析字段定义文件，获取每个字段的抽取要求
- 根据字段的`description`和`example`构建专门的提示词
- 根据字段的`format`选择合适的抽取策略：
  * 普通文本：关键词匹配和语义理解
  * 表格行：表格结构识别和数据提取
  * 段落：段落边界识别和内容提取
  * 人名：命名实体识别
  * 单独行：行级别的精确匹配
- 根据字段的`source`确定信息来源和处理方式
- 计算抽取置信度并记录原文位置

**抽取策略**:
- 多策略并行：关键词匹配 + 语义理解 + 模式识别
- 上下文感知：考虑前后文语境提高准确性
- 格式适配：根据不同format采用不同的抽取算法
- 置信度计算：基于匹配度、语义相关性、格式符合度

#### 3.2.3 外部搜索智能体 (ExternalSearch Agent)
**职责**: 处理需要外部信息的字段，通过搜索引擎获取并整合信息
**输入**: source为"外部搜索"的字段定义
**输出**: 搜索结果和整合后的信息

**核心功能**:
- 识别需要外部搜索的字段（source: 外部搜索）
- 根据字段描述和已有信息构建搜索查询
- 调用搜索引擎API获取相关信息
- 使用大模型整合搜索结果，生成符合字段要求的内容
- 评估信息的可靠性和相关性

**搜索策略**:
- 智能查询构建：基于字段描述和上下文
- 多源搜索：结合不同搜索引擎的结果
- 结果筛选：过滤无关和低质量信息
- 信息整合：使用大模型生成最终答案

#### 3.2.4 数据验证智能体 (DataValidator Agent)
**职责**: 验证抽取结果的完整性、准确性和一致性
**输入**: 抽取结果文件(extracted_keys.md) + 字段定义文件
**输出**: 验证报告 + 完整性检查结果

**核心功能**:
- 必填字段完整性检查（must: true的字段是否都有值）
- 字段格式验证（是否符合format要求）
- 长度限制检查（是否超出length限制）
- 默认值处理（为空字段应用default值）
- 逻辑一致性验证（相关字段间的逻辑关系）
- 数据质量评估（置信度分布、抽取成功率等）

**验证规则**:
- 必填字段检查：所有must为true的字段必须有值
- 格式验证：根据format字段验证数据格式
- 长度检查：确保内容不超过length限制
- 默认值应用：为空的非必填字段应用默认值
- 交叉验证：检查相关字段的逻辑一致性

#### 3.2.5 报告生成智能体 (ReportGenerator Agent)
**职责**: 基于验证后的数据和模板生成最终报告
**输入**: 验证后的抽取数据 + 模板文件(template_*.docx)
**输出**: 最终报告文件(output_*.docx)

**核心功能**:
- 模板文件解析和占位符识别
- 数据映射和占位符替换
- 格式保持和样式继承
- 动态内容处理（表格、列表、段落）
- 文档结构调整和优化
- 输出文件生成和保存

**生成策略**:
- 精确替换：将字段值准确替换到对应占位符
- 格式保持：保持模板的原始样式和布局
- 动态扩展：根据数据内容动态调整表格行数等
- 智能排版：自动调整段落、间距、页面布局

#### 3.2.6 质量检查智能体 (QualityChecker Agent)
**职责**: 对生成的报告进行全面质量检查和评估
**输入**: 生成的报告文件(output_*.docx)
**输出**: 质量评估报告 + 改进建议

**核心功能**:
- 占位符替换完整性检查（确保所有{{字段名}}都被正确替换）
- 格式一致性检查（字体、样式、对齐、间距等）
- 内容逻辑性检查（时间顺序、人员角色、事件关联等）
- 语法和拼写检查（文字表达的准确性）
- 文档结构完整性检查（标题、段落、表格结构等）
- 专业规范检查（符合会议纪要的标准格式）

**质量评分维度**:
- 完整性评分：所有必填字段是否都有内容
- 准确性评分：内容是否与原始信息一致
- 格式评分：是否符合模板格式要求
- 可读性评分：语言表达是否清晰流畅
- 专业性评分：是否符合行业标准

### 3.3 智能体循环机制

#### 3.3.1 主循环流程
```
文档预处理 → 字段抽取 → 外部搜索(如需要) → 数据验证 → [决策点1] → 报告生成 → 质量检查 → [决策点2] → 输出
                                                      ↓                              ↓
                                                 完整性检查                      质量优化
                                                      ↓                              ↓
                                                 人工确认 ← ← ← ← ← ← ← ← ← ← ← ← ← ←
```

#### 3.3.2 关键决策点

**决策点1：是否需要人工干预**
- 触发条件：
  - 必填字段缺失（must: true的字段为空）
  - 低置信度字段过多（>30%的字段置信度<0.5）
  - 数据验证发现严重错误
- 处理策略：
  - 暂停自动流程，进入人工确认阶段
  - 提示用户补充缺失信息
  - 等待用户确认后继续流程

**决策点2：是否需要质量优化**
- 触发条件：
  - 质量评分低于阈值（<0.8）
  - 发现格式或内容问题
  - 占位符替换不完整
- 处理策略：
  - 分析具体问题类型
  - 调整生成参数重新生成
  - 限制最大重试次数（3次）

#### 3.3.3 状态管理机制

**全局状态结构**:
```
WorkflowState {
    workflow_id: 唯一标识
    current_stage: 当前阶段
    template_info: {
        template_id: 模板ID
        template_name: 模板名称
        template_file: 模板文件路径
        key_definitions: 字段定义内容
    }
    input_documents: [
        {
            file_name: 文件名
            file_path: 文件路径
            file_type: 文件类型
            processed_content: 预处理后内容
        }
    ]
    extracted_data: {
        field_name: {
            value: 抽取值
            confidence: 置信度
            source_location: 原文位置
            extraction_method: 抽取方法
        }
    }
    validation_results: {
        completeness_score: 完整性评分
        missing_required_fields: 缺失必填字段列表
        format_errors: 格式错误列表
        suggestions: 改进建议
    }
    generation_results: {
        output_file: 输出文件路径
        quality_score: 质量评分
        generation_time: 生成耗时
        issues: 发现的问题列表
    }
    human_interventions: [
        {
            field_name: 字段名
            original_value: 原始值
            modified_value: 修改后值
            timestamp: 修改时间
        }
    ]
    iteration_count: 循环次数
    error_logs: 错误日志
    timestamps: 各阶段时间戳
}
```

**状态传递规则**:
- 每个智能体接收当前状态，更新相关部分
- 关键状态变更触发WebSocket通知前端
- 支持状态持久化和恢复
- 异常状态的自动处理和降级

---

## 🎯 4. 用户交互流程设计

### 4.1 系统管理界面

#### 4.1.1 模板管理页面
1. **模板列表页面**
   - 显示所有可用模板的卡片式列表
   - 每个模板卡片包含：
     * 模板名称和描述
     * 模板预览缩略图
     * 字段数量统计
     * 使用次数和评分
     * 创建时间和最后更新时间
     * 启用/禁用状态
   - 支持搜索、筛选、排序功能
   - 支持模板的启用/禁用操作

2. **模板详情页面**
   - 模板文件预览（template_*.docx）
   - 字段定义详情（key_definitions.md内容）
   - 使用历史和统计信息
   - 编辑和版本管理功能

3. **模板上传页面**
   - 上传模板文件（template_*.docx）
   - 上传字段定义文件（key_definitions.md）
   - 模板信息配置（名称、描述、分类等）
   - 占位符自动识别和验证

#### 4.1.2 报告生成主流程
1. **模板选择页面**
   - 展示可用模板列表
   - 模板预览和详情查看
   - 模板选择确认

2. **文档上传页面**
   - 批量上传input_*文件
   - 文件预览和质量检查
   - 上传进度和状态显示

3. **智能抽取页面**
   - 实时显示抽取进度
   - 按字段显示抽取状态
   - 抽取结果实时预览

4. **完整性检查页面**
   - 显示抽取结果统计
   - 按状态分类显示字段
   - 缺失字段的处理选项

5. **人工确认页面**
   - 分组显示所有字段
   - 逐字段确认和编辑
   - 原文对照查看功能
   - 批量确认操作

6. **生成确认页面**
   - 显示生成前的最终确认信息
   - 模板和数据摘要
   - 生成选项设置

7. **报告预览页面**
   - 生成报告的在线预览
   - 质量检查结果显示
   - 下载和重新生成功能

### 4.2 关键交互设计

#### 4.2.1 字段确认界面设计
- **字段分组显示**: 按照字段类型和重要性分组
- **状态可视化**: 用不同颜色和图标表示字段状态
  * ✅ 绿色：已确认的高置信度字段
  * ⚠️ 黄色：需要确认的中置信度字段
  * ❌ 红色：需要修正的低置信度或缺失字段
  * ℹ️ 蓝色：可选字段
- **原文对照**: 点击字段可查看原文上下文
- **快速编辑**: 支持直接编辑字段值
- **批量操作**: 支持批量确认多个字段

#### 4.2.2 必填字段处理机制
- **强制确认**: 必填字段必须有值才能进入下一步
- **默认值选项**: 为有默认值的字段提供"使用默认值"选项
- **补充信息**: 为缺失字段提供多种补充方式：
  * 手动输入
  * 上传补充文件
  * 从知识库选择
- **进度提示**: 实时显示必填字段的完成进度

#### 4.2.3 用户体验原则
- **渐进式披露**: 复杂功能分步骤展示，避免信息过载
- **即时反馈**: 操作结果立即可见，提供实时状态更新
- **容错设计**: 支持撤销和重做，防止误操作
- **智能提示**: 提供上下文相关的操作建议和帮助
- **自动保存**: 定期保存用户操作，防止数据丢失
- **记忆功能**: 记住用户的操作习惯和偏好设置

---

## 🎯 5. 成功指标和验收标准

### 5.1 功能性指标
- **字段抽取准确率**: ≥85%（基于样例数据测试）
- **必填字段完整率**: ≥95%（所有must:true字段都有值）
- **模板生成成功率**: ≥98%（占位符正确替换）
- **用户确认通过率**: ≥90%（用户对抽取结果的接受度）

### 5.2 性能指标
- **单文档处理时间**: ≤3分钟（包含抽取和生成）
- **系统响应时间**: ≤2秒（界面操作响应）
- **并发处理能力**: ≥5个任务同时处理
- **系统可用性**: ≥99.5%

### 5.3 用户体验指标
- **学习成本**: 新用户20分钟内完成首次报告生成
- **操作效率**: 比手工制作提升80%以上
- **错误率**: 用户操作错误率≤3%
- **任务完成率**: ≥95%

---

## 🔧 6. 技术实现要点

### 6.1 字段定义解析
- **Markdown解析**: 解析key_definitions.md文件结构
- **字段属性提取**: 提取description、example、format等属性
- **验证规则生成**: 根据must、length等生成验证逻辑

### 6.2 多源信息处理
- **上传内容抽取**: 从input_*文件中抽取信息
- **系统时间获取**: 自动获取当前时间作为默认值
- **外部搜索集成**: 集成搜索引擎API获取外部信息
- **知识库查询**: 从内置知识库获取标准信息

### 6.3 模板处理机制
- **占位符识别**: 识别template_*.docx中的{{字段名}}
- **格式保持**: 保持原模板的样式和布局
- **动态内容处理**: 处理表格行、列表等动态内容
- **文档生成**: 生成最终的output_*.docx文件

---

## 📝 7. 开发实施计划

### 7.1 第一阶段：核心功能开发（2周）
1. **文件管理系统**: 实现5类文件的上传、存储、管理
2. **字段定义解析**: 解析key_definitions.md文件
3. **基础抽取功能**: 实现从上传内容中抽取信息
4. **模板生成功能**: 实现基础的模板填充和文档生成

### 7.2 第二阶段：智能体集成（2周）
1. **LangGraph框架集成**: 搭建智能体协作框架
2. **多源信息处理**: 实现外部搜索和知识库查询
3. **完整性检查**: 实现必填字段检查和验证
4. **人工确认界面**: 实现用户确认和修正界面

### 7.3 第三阶段：优化和完善（1周）
1. **质量检查**: 实现生成报告的质量评估
2. **用户体验优化**: 完善界面交互和用户引导
3. **性能优化**: 优化处理速度和并发能力
4. **测试和调试**: 全面测试和问题修复

---

## 🎯 8. 验收测试用例

### 8.1 基于样例的测试
使用 `sample_report/数字科技中心会议纪要` 作为标准测试用例：

1. **输入**: `1.input_住建厅项目预审会会议通知.docx` + `1.input_现场会议记录.txt`
2. **模板**: `4.template_数字科技中心会议纪要模板.docx`
3. **字段定义**: `2.key_definitions.md`
4. **期望输出**: 与 `5.output_最终生成效果.docx` 相似的报告
5. **期望抽取结果**: 与 `3.extracted_keys.md` 相似的数据

### 8.2 功能测试用例
1. **模板管理测试**: 上传、浏览、选择模板
2. **文档上传测试**: 批量上传、格式转换、预处理
3. **智能抽取测试**: 字段抽取、置信度评估、多源信息处理
4. **完整性检查测试**: 必填字段检查、默认值应用
5. **人工确认测试**: 字段确认、修正、补充
6. **报告生成测试**: 模板填充、格式保持、文档生成
7. **质量检查测试**: 占位符检查、格式验证、内容评估

### 8.3 性能测试用例
1. **处理时间测试**: 不同大小文档的处理时间
2. **并发测试**: 多用户同时使用的性能表现
3. **稳定性测试**: 长时间运行的稳定性
4. **错误恢复测试**: 异常情况下的恢复能力

---

**总结**: 本设计方案基于实际样例（数字科技中心会议纪要）进行设计，充分考虑了5类文件的管理、字段定义的解析、多源信息的处理、人工确认的机制等关键需求。通过LangGraph智能体协作框架，实现了从文档上传到报告生成的完整流程，确保了系统的实用性和可操作性。
