#!/usr/bin/env python3
"""
完整工作流测试
测试从意图识别到数据提取的完整流程
"""

import requests
import tempfile
import os
import json
import time

def test_complete_workflow():
    """测试完整的工作流程"""
    
    # 创建测试文档
    test_content = """
    会议记录
    
    会议主题: 项目进度讨论会
    会议时间: 2024年1月15日 14:00-16:00
    会议地点: 会议室A
    主持人: 张经理
    参会人员: 李工程师、王设计师、陈分析师
    
    会议内容:
    1. 项目当前进度汇报
    2. 遇到的技术难题讨论
    3. 下一阶段工作安排
    
    决议事项:
    1. 加快开发进度，确保按时交付
    2. 技术难题由李工程师负责解决
    3. 下周三进行下一次进度检查
    
    会议结束时间: 16:00
    """
    
    print("🚀 开始完整工作流测试...")
    
    # 第1步: 启动工作流
    print("\n📝 第1步: 启动工作流...")
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(test_content)
        temp_file_path = f.name
    
    try:
        with open(temp_file_path, 'rb') as f:
            files = {'files': ('meeting_record.txt', f, 'text/plain')}
            data = {'text_input': '请帮我分析这个会议记录，提取关键信息'}
            
            response = requests.post(
                'http://localhost:8000/api/workflow/langgraph/start',
                files=files,
                data=data
            )
        
        print(f"启动状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            workflow_id = result.get('workflow_id')
            print(f"工作流ID: {workflow_id}")
            print(f"完整响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            print(f"当前阶段: {result.get('current_stage', 'unknown')}")
            requires_user_action = result.get('data', {}).get('requires_user_action', False)
            print(f"需要用户操作: {requires_user_action}")
            print(f"人机交互类型: {result.get('human_input_type')}")
            print(f"人机交互消息: {result.get('human_input_message', '')}")

            if requires_user_action:
                print("\n✅ 工作流正确暂停，等待用户确认模板")
                
                # 第2步: 确认模板
                print("\n📋 第2步: 确认推荐的模板...")
                time.sleep(1)  # 等待一秒
                
                confirm_response = requests.post(
                    f'http://localhost:8000/api/workflow/langgraph/resume/{workflow_id}',
                    json={'action': 'confirm'}
                )
                
                print(f"模板确认状态码: {confirm_response.status_code}")
                if confirm_response.status_code == 200:
                    confirm_result = confirm_response.json()
                    print(f"确认状态: {confirm_result.get('status')}")
                    print(f"当前阶段: {confirm_result.get('current_stage', 'unknown')}")
                    print(f"需要用户操作: {confirm_result.get('requires_human_input', False)}")
                    
                    # 第3步: 等待数据提取完成
                    print("\n🔍 第3步: 等待数据提取完成...")
                    time.sleep(3)  # 等待数据提取
                    
                    # 检查最终状态
                    status_response = requests.get(
                        f'http://localhost:8000/api/workflow/langgraph/status/{workflow_id}'
                    )
                    
                    if status_response.status_code == 200:
                        final_status = status_response.json()
                        print(f"最终状态: {final_status.get('status')}")
                        print(f"当前阶段: {final_status.get('current_stage', 'unknown')}")
                        print(f"提取结果: {final_status.get('extracted_data', {})}")
                        
                        if final_status.get('extracted_data'):
                            print("\n🎉 数据提取成功！")
                            extracted_data = final_status.get('extracted_data', {})
                            for key, value in extracted_data.items():
                                print(f"  {key}: {value}")
                        else:
                            print("\n⚠️ 数据提取结果为空")
                    else:
                        print(f"❌ 获取最终状态失败: {status_response.status_code}")
                else:
                    print(f"❌ 模板确认失败: {confirm_response.status_code}")
                    print(confirm_response.text)
            else:
                print("❌ 工作流没有正确暂停等待用户输入")
        else:
            print(f"❌ 启动工作流失败: {response.status_code}")
            print(response.text)
            
    finally:
        # 清理临时文件
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
    
    print("\n✅ 完整工作流测试完成")

if __name__ == "__main__":
    test_complete_workflow()
