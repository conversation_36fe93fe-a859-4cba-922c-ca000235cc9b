#!/usr/bin/env python3
"""
LLM服务测试脚本
测试通义千问DashScope API集成
"""

import os
import asyncio
import json
from datetime import datetime

# 设置环境变量
os.environ['DATABASE_URL'] = 'postgresql://report_user:report_pass@localhost:5432/report_gen'

async def test_basic_llm_call():
    """测试基础LLM调用"""
    print("🔍 测试基础LLM调用...")
    
    try:
        from services.llm_service import llm_service
        
        messages = [
            {
                "role": "system",
                "content": "你是一个有用的AI助手。"
            },
            {
                "role": "user",
                "content": "请简单介绍一下你自己，用中文回答，不超过50字。"
            }
        ]
        
        response = await llm_service.call_llm(messages)
        
        if response.success:
            print(f"✅ LLM调用成功")
            print(f"📝 响应内容: {response.content}")
            print(f"📊 Token使用: {response.usage}")
            print(f"🤖 模型: {response.model}")
            return True
        else:
            print(f"❌ LLM调用失败: {response.error}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False


async def test_information_extraction():
    """测试信息抽取功能"""
    print("\n🔍 测试信息抽取功能...")
    
    try:
        from services.llm_service import llm_service, ExtractionRequest
        
        # 模拟文档内容
        document_content = """
        会议纪要
        
        会议时间：2024年7月4日 14:00-16:00
        会议地点：数字科技中心会议室A
        主持人：张三
        参会人员：李四、王五、赵六、钱七
        
        会议主题：数字化转型项目进展讨论
        
        会议内容：
        1. 项目进展汇报
           - 系统开发完成度：85%
           - 预计完成时间：2024年8月15日
           
        2. 存在问题
           - 数据迁移遇到技术难题
           - 用户培训计划需要调整
           
        3. 下一步计划
           - 加强技术攻关
           - 制定详细的培训方案
        """
        
        # 测试字段抽取
        test_fields = [
            {
                "field_name": "会议时间",
                "description": "会议举行的具体时间",
                "example": "2024年7月4日 14:00-16:00",
                "format": "日期时间"
            },
            {
                "field_name": "主持人",
                "description": "会议的主持人姓名",
                "example": "张三",
                "format": "人名"
            },
            {
                "field_name": "会议主题",
                "description": "会议讨论的主要议题",
                "example": "数字化转型项目进展讨论",
                "format": "普通文本"
            }
        ]
        
        extraction_results = {}
        
        for field in test_fields:
            print(f"  📋 抽取字段: {field['field_name']}")
            
            request = ExtractionRequest(
                field_name=field["field_name"],
                field_description=field["description"],
                field_example=field["example"],
                field_format=field["format"],
                source_text=document_content
            )
            
            response = await llm_service.extract_field_info(request)
            
            if response.success:
                extracted_value = response.content.strip()
                extraction_results[field["field_name"]] = extracted_value
                print(f"    ✅ 抽取成功: {extracted_value}")
            else:
                print(f"    ❌ 抽取失败: {response.error}")
                extraction_results[field["field_name"]] = ""
        
        print(f"\n📊 抽取结果汇总:")
        for field_name, value in extraction_results.items():
            print(f"  {field_name}: {value}")
        
        return len([v for v in extraction_results.values() if v]) > 0
        
    except Exception as e:
        print(f"❌ 信息抽取测试异常: {e}")
        return False


async def test_data_validation():
    """测试数据验证功能"""
    print("\n🔍 测试数据验证功能...")
    
    try:
        from services.llm_service import llm_service
        
        # 测试数据
        field_name = "会议时间"
        field_value = "2024年7月4日 14:00-16:00"
        field_format = "日期时间"
        original_text = "会议时间：2024年7月4日 14:00-16:00"
        
        response = await llm_service.validate_extracted_data(
            field_name=field_name,
            field_value=field_value,
            field_format=field_format,
            original_text=original_text
        )
        
        if response.success:
            print(f"✅ 验证调用成功")
            print(f"📝 验证结果: {response.content}")
            
            try:
                validation_data = json.loads(response.content)
                is_valid = validation_data.get("is_valid", False)
                confidence = validation_data.get("confidence", 0.0)
                reason = validation_data.get("reason", "")
                
                print(f"📊 验证详情:")
                print(f"  有效性: {'✅ 有效' if is_valid else '❌ 无效'}")
                print(f"  置信度: {confidence:.2f}")
                print(f"  说明: {reason}")
                
                return is_valid
                
            except json.JSONDecodeError:
                print(f"❌ 验证结果解析失败")
                return False
        else:
            print(f"❌ 验证调用失败: {response.error}")
            return False
            
    except Exception as e:
        print(f"❌ 数据验证测试异常: {e}")
        return False


async def test_agent_integration():
    """测试智能体集成"""
    print("\n🔍 测试智能体集成...")
    
    try:
        from agents.extraction_agent import ExtractionAgent
        from agents.base_agent import AgentState
        
        # 创建测试状态
        state = AgentState(
            workflow_id="test_workflow_001",
            current_stage="初始化"
        )
        
        # 设置文档信息
        state.document_info = {
            "content": """
            项目报告
            
            项目名称：智能报告生成系统
            项目负责人：李明
            开始时间：2024年6月1日
            预计完成时间：2024年8月31日
            项目状态：进行中
            完成进度：75%
            
            项目描述：
            本项目旨在开发一个基于AI的智能报告生成系统，
            能够自动从各种文档中抽取关键信息并生成标准化报告。
            """
        }
        
        # 设置模板信息
        state.template_info = {
            "fields": [
                {
                    "field_name": "项目名称",
                    "description": "项目的正式名称",
                    "example": "智能报告生成系统",
                    "format": "普通文本"
                },
                {
                    "field_name": "项目负责人",
                    "description": "项目的主要负责人姓名",
                    "example": "李明",
                    "format": "人名"
                },
                {
                    "field_name": "完成进度",
                    "description": "项目当前的完成百分比",
                    "example": "75%",
                    "format": "百分比"
                }
            ]
        }
        
        # 创建抽取智能体
        extraction_agent = ExtractionAgent()
        
        # 执行抽取
        print("  🤖 启动信息抽取智能体...")
        updated_state = await extraction_agent.process(state)
        
        # 检查结果
        extracted_data = updated_state.extracted_data
        confidence_scores = updated_state.confidence_scores
        
        print(f"  📊 抽取结果:")
        for field_name, value in extracted_data.items():
            confidence = confidence_scores.get(field_name, 0.0)
            print(f"    {field_name}: {value} (置信度: {confidence:.2f})")
        
        # 执行验证
        print("  🔍 启动结果验证...")
        validated_state = await extraction_agent.validate_extraction_results(updated_state)
        
        validation_results = validated_state.validation_results
        print(f"  📋 验证结果:")
        for field_name, validation in validation_results.items():
            is_valid = validation.get("is_valid", False)
            confidence = validation.get("confidence", 0.0)
            print(f"    {field_name}: {'✅ 有效' if is_valid else '❌ 无效'} (置信度: {confidence:.2f})")
        
        return len(extracted_data) > 0
        
    except Exception as e:
        print(f"❌ 智能体集成测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("🚀 开始LLM服务测试\n")
    
    # 检查API密钥配置
    from config import settings
    if not settings.dashscope_api_key or settings.dashscope_api_key == "your-dashscope-api-key-here":
        print("⚠️  警告: DashScope API密钥未配置")
        print("请在.env文件中设置DASHSCOPE_API_KEY")
        print("测试将使用模拟响应\n")
    
    tests = [
        ("基础LLM调用", test_basic_llm_call),
        ("信息抽取功能", test_information_extraction),
        ("数据验证功能", test_data_validation),
        ("智能体集成", test_agent_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"{'='*50}")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 发生异常: {e}")
            results.append((test_name, False))
    
    print(f"\n{'='*50}")
    print("📊 测试结果汇总:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    print(f"\n{'='*50}")
    if all_passed:
        print("🎉 所有LLM服务测试通过！")
        print("✅ LLM集成已准备就绪")
    else:
        print("❌ 部分测试失败，请检查配置和实现")
        if not settings.dashscope_api_key or settings.dashscope_api_key == "your-dashscope-api-key-here":
            print("💡 提示: 请确保已正确配置DashScope API密钥")
    
    return all_passed


if __name__ == "__main__":
    asyncio.run(main())
