#!/usr/bin/env python3
"""
智能体集成测试脚本
"""

import asyncio

async def test_extraction_agent():
    """测试信息抽取智能体"""
    print("🔍 测试信息抽取智能体...")
    
    try:
        from agents.extraction_agent import ExtractionAgent
        from agents.base_agent import AgentState
        
        # 创建测试状态
        state = AgentState(
            workflow_id="test_workflow_001",
            current_stage="初始化"
        )
        
        # 设置文档信息
        state.document_info = {
            "content": """
            项目报告
            
            项目名称：智能报告生成系统
            项目负责人：李明
            开始时间：2024年6月1日
            预计完成时间：2024年8月31日
            项目状态：进行中
            完成进度：75%
            
            项目描述：
            本项目旨在开发一个基于AI的智能报告生成系统，
            能够自动从各种文档中抽取关键信息并生成标准化报告。
            
            主要功能：
            1. 文档解析和信息抽取
            2. 模板管理和字段定义
            3. 智能报告生成
            4. 质量验证和人工审核
            """
        }
        
        # 设置模板信息
        state.template_info = {
            "fields": [
                {
                    "field_name": "项目名称",
                    "description": "项目的正式名称",
                    "example": "智能报告生成系统",
                    "format": "普通文本"
                },
                {
                    "field_name": "项目负责人",
                    "description": "项目的主要负责人姓名",
                    "example": "李明",
                    "format": "人名"
                },
                {
                    "field_name": "完成进度",
                    "description": "项目当前的完成百分比",
                    "example": "75%",
                    "format": "百分比"
                },
                {
                    "field_name": "项目状态",
                    "description": "项目当前的状态",
                    "example": "进行中",
                    "format": "普通文本"
                }
            ]
        }
        
        # 创建抽取智能体
        extraction_agent = ExtractionAgent()
        
        # 执行抽取
        print("  🤖 启动信息抽取智能体...")
        updated_state = await extraction_agent.process(state)
        
        # 检查结果
        extracted_data = updated_state.extracted_data
        confidence_scores = updated_state.confidence_scores
        
        print(f"  📊 抽取结果:")
        for field_name, value in extracted_data.items():
            confidence = confidence_scores.get(field_name, 0.0)
            print(f"    {field_name}: {value} (置信度: {confidence:.2f})")
        
        # 执行验证
        print("  🔍 启动结果验证...")
        validated_state = await extraction_agent.validate_extraction_results(updated_state)
        
        validation_results = validated_state.validation_results
        print(f"  📋 验证结果:")
        for field_name, validation in validation_results.items():
            is_valid = validation.get("is_valid", False)
            confidence = validation.get("confidence", 0.0)
            print(f"    {field_name}: {'✅ 有效' if is_valid else '❌ 无效'} (置信度: {confidence:.2f})")
        
        # 检查统计信息
        stats = updated_state.generation_results.get("extraction_stats", {})
        if stats:
            print(f"  📈 抽取统计:")
            print(f"    总字段数: {stats.get('total_fields', 0)}")
            print(f"    成功抽取: {stats.get('extracted_fields', 0)}")
            print(f"    成功率: {stats.get('success_rate', 0):.2%}")
            print(f"    平均置信度: {stats.get('avg_confidence', 0):.2f}")
        
        return len(extracted_data) > 0
        
    except Exception as e:
        print(f"❌ 智能体集成测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_workflow_simulation():
    """测试完整工作流程模拟"""
    print("\n🔍 测试完整工作流程模拟...")
    
    try:
        from agents.extraction_agent import ExtractionAgent
        from agents.base_agent import AgentState
        
        # 模拟会议纪要文档
        meeting_content = """
        会议纪要
        
        会议主题：数字化转型项目第三季度进展评审
        会议时间：2024年7月4日 14:00-16:30
        会议地点：总部大楼A座15层会议室
        主持人：王总经理
        记录人：张秘书
        
        参会人员：
        - 王总经理（主持人）
        - 李技术总监
        - 赵项目经理
        - 钱财务经理
        - 孙人事经理
        
        会议议程：
        1. 项目进展汇报
        2. 预算执行情况
        3. 人员配置调整
        4. 下阶段工作计划
        
        主要内容：
        
        一、项目进展汇报（李技术总监）
        - 系统开发完成度：85%
        - 核心功能模块已完成测试
        - 预计8月15日完成全部开发工作
        
        二、预算执行情况（钱财务经理）
        - 已使用预算：320万元
        - 剩余预算：80万元
        - 预算执行率：80%
        
        三、人员配置（孙人事经理）
        - 当前团队规模：25人
        - 新增需求：前端开发工程师2名
        - 预计下月到岗
        
        四、决议事项：
        1. 批准增加前端开发人员
        2. 调整项目里程碑时间节点
        3. 加强质量控制和测试工作
        
        五、下次会议安排：
        时间：2024年8月1日 14:00
        地点：同本次会议
        """
        
        # 创建状态
        state = AgentState(
            workflow_id="meeting_workflow_001",
            current_stage="文档解析"
        )
        
        state.document_info = {"content": meeting_content}
        
        # 定义会议纪要模板字段
        state.template_info = {
            "fields": [
                {
                    "field_name": "会议主题",
                    "description": "会议讨论的主要议题和目的",
                    "example": "数字化转型项目第三季度进展评审",
                    "format": "普通文本"
                },
                {
                    "field_name": "会议时间",
                    "description": "会议举行的具体日期和时间",
                    "example": "2024年7月4日 14:00-16:30",
                    "format": "日期时间"
                },
                {
                    "field_name": "主持人",
                    "description": "会议的主持人姓名和职务",
                    "example": "王总经理",
                    "format": "人名"
                },
                {
                    "field_name": "参会人数",
                    "description": "参加会议的总人数",
                    "example": "5人",
                    "format": "数字"
                },
                {
                    "field_name": "项目完成度",
                    "description": "项目当前的完成百分比",
                    "example": "85%",
                    "format": "百分比"
                },
                {
                    "field_name": "预算执行率",
                    "description": "预算的执行百分比",
                    "example": "80%",
                    "format": "百分比"
                },
                {
                    "field_name": "下次会议时间",
                    "description": "下一次会议安排的时间",
                    "example": "2024年8月1日 14:00",
                    "format": "日期时间"
                }
            ]
        }
        
        # 执行抽取流程
        agent = ExtractionAgent()
        
        print("  📝 执行信息抽取...")
        result_state = await agent.process(state)
        
        print("  🔍 执行结果验证...")
        final_state = await agent.validate_extraction_results(result_state)
        
        # 输出最终结果
        print("  📊 最终抽取结果:")
        extracted_data = final_state.extracted_data
        validation_results = final_state.validation_results
        
        for field_name, value in extracted_data.items():
            validation = validation_results.get(field_name, {})
            is_valid = validation.get("is_valid", False)
            confidence = validation.get("confidence", 0.0)
            
            status = "✅" if is_valid else "❌"
            print(f"    {status} {field_name}: {value} (置信度: {confidence:.2f})")
        
        # 计算整体质量分数
        valid_count = sum(1 for v in validation_results.values() if v.get("is_valid", False))
        total_count = len(validation_results)
        quality_score = valid_count / total_count if total_count > 0 else 0
        
        print(f"  🎯 整体质量评分: {quality_score:.2%} ({valid_count}/{total_count})")
        
        return quality_score > 0.7  # 70%以上认为成功
        
    except Exception as e:
        print(f"❌ 工作流程测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("🚀 开始智能体集成测试\n")
    
    tests = [
        ("信息抽取智能体", test_extraction_agent),
        ("完整工作流程", test_workflow_simulation)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"{'='*60}")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 发生异常: {e}")
            results.append((test_name, False))
    
    print(f"\n{'='*60}")
    print("📊 智能体测试结果汇总:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    print(f"\n{'='*60}")
    if all_passed:
        print("🎉 所有智能体测试通过！")
        print("✅ 智能体框架已准备就绪")
        print("🚀 可以开始构建完整的报告生成工作流")
    else:
        print("❌ 部分测试失败，需要进一步优化")
    
    return all_passed


if __name__ == "__main__":
    asyncio.run(main())
