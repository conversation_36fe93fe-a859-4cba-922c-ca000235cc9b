/**
 * 人机交互状态管理 Hook
 * 管理字段验证流程的状态和后端通信
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { message } from 'antd';

import {
  HumanInteractionSession,
  InteractionSessionStatus,
  FieldValidationStatus,
  ValidationSession,
  ExtractedField,
  WorkflowProgress,
  WorkflowStage,
  StageStatus
} from '../components/HumanInteraction';

// API 接口类型定义
interface CreateSessionRequest {
  document_path: string;
  template_name: string;
  user_preferences?: {
    auto_advance: boolean;
    confidence_threshold: number;
    batch_validation: boolean;
  };
}

interface CreateSessionResponse {
  session_id: string;
  workflow_id: string;
  status: InteractionSessionStatus;
  validation_session: ValidationSession;
  workflow_progress: WorkflowProgress;
}

interface ValidateFieldRequest {
  session_id: string;
  field_name: string;
  validation_status: FieldValidationStatus;
  user_confirmed_value?: string;
  rejection_reason?: string;
}

interface ValidateFieldResponse {
  success: boolean;
  updated_session: HumanInteractionSession;
  next_field?: string;
  session_complete?: boolean;
}

interface CompleteSessionRequest {
  session_id: string;
  validated_data: Record<string, string>;
}

interface CompleteSessionResponse {
  success: boolean;
  generated_report: string;
  report_id: string;
  final_statistics: {
    total_validation_time: number;
    fields_validated_per_minute: number;
    accuracy_rate: number;
  };
}

// Hook 配置选项
interface UseHumanInteractionOptions {
  apiBaseUrl?: string;
  pollInterval?: number; // 轮询间隔（毫秒）
  onSessionComplete?: (reportId: string, report: string) => void;
  onSessionError?: (error: string) => void;
  onValidationProgress?: (progress: number) => void;
}

// Hook 返回值接口
interface UseHumanInteractionReturn {
  // 状态
  session: HumanInteractionSession | null;
  isLoading: boolean;
  error: string | null;
  
  // 操作方法
  createSession: (request: CreateSessionRequest) => Promise<void>;
  validateField: (fieldName: string, status: FieldValidationStatus, value?: string, reason?: string) => Promise<void>;
  completeSession: (validatedData: Record<string, string>) => Promise<void>;
  pauseSession: () => Promise<void>;
  resumeSession: () => Promise<void>;
  cancelSession: () => Promise<void>;
  
  // 工具方法
  getValidationProgress: () => number;
  getFieldByName: (fieldName: string) => ExtractedField | undefined;
  getPendingFields: () => ExtractedField[];
  getValidatedFields: () => ExtractedField[];
}

const useHumanInteraction = (options: UseHumanInteractionOptions = {}): UseHumanInteractionReturn => {
  const {
    apiBaseUrl = 'http://localhost:8000',
    pollInterval = 5000,
    onSessionComplete,
    onSessionError,
    onValidationProgress
  } = options;

  // 状态管理
  const [session, setSession] = useState<HumanInteractionSession | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 引用，用于避免闭包问题
  const sessionRef = useRef<HumanInteractionSession | null>(null);
  const pollTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 更新会话状态
  const updateSession = useCallback((newSession: HumanInteractionSession) => {
    setSession(newSession);
    sessionRef.current = newSession;
    
    // 触发进度回调
    if (onValidationProgress && newSession.validation_session) {
      const progress = (newSession.validation_session.validated_fields / newSession.validation_session.total_fields) * 100;
      onValidationProgress(progress);
    }
  }, [onValidationProgress]);

  // API 调用封装
  const apiCall = useCallback(async <T>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    data?: any
  ): Promise<T> => {
    try {
      const response = await fetch(`${apiBaseUrl}/api/human-interaction${endpoint}`, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: data ? JSON.stringify(data) : undefined,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP ${response.status}`);
      }

      return await response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '网络请求失败';
      setError(errorMessage);
      onSessionError?.(errorMessage);
      throw err;
    }
  }, [apiBaseUrl, onSessionError]);

  // 创建验证会话
  const createSession = useCallback(async (request: CreateSessionRequest) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiCall<CreateSessionResponse>('/sessions', 'POST', request);
      
      const newSession: HumanInteractionSession = {
        session_id: response.session_id,
        workflow_id: response.workflow_id,
        document_name: request.document_path.split('/').pop() || 'Unknown',
        template_name: request.template_name,
        status: response.status,
        validation_session: response.validation_session,
        workflow_progress: response.workflow_progress,
        created_at: new Date(),
        updated_at: new Date(),
        user_preferences: request.user_preferences
      };

      updateSession(newSession);
      message.success('验证会话创建成功');
    } catch (err) {
      console.error('创建会话失败:', err);
      message.error('创建验证会话失败');
    } finally {
      setIsLoading(false);
    }
  }, [apiCall, updateSession]);

  // 验证字段
  const validateField = useCallback(async (
    fieldName: string,
    status: FieldValidationStatus,
    value?: string,
    reason?: string
  ) => {
    if (!session) {
      throw new Error('没有活动的验证会话');
    }

    setIsLoading(true);
    setError(null);

    try {
      const request: ValidateFieldRequest = {
        session_id: session.session_id,
        field_name: fieldName,
        validation_status: status,
        user_confirmed_value: value,
        rejection_reason: reason
      };

      const response = await apiCall<ValidateFieldResponse>('/validate-field', 'POST', request);
      
      if (response.success) {
        updateSession(response.updated_session);
        
        if (response.session_complete) {
          message.success('所有字段验证完成！');
        }
      }
    } catch (err) {
      console.error('字段验证失败:', err);
      message.error('字段验证失败');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [session, apiCall, updateSession]);

  // 完成验证会话
  const completeSession = useCallback(async (validatedData: Record<string, string>) => {
    if (!session) {
      throw new Error('没有活动的验证会话');
    }

    setIsLoading(true);
    setError(null);

    try {
      const request: CompleteSessionRequest = {
        session_id: session.session_id,
        validated_data: validatedData
      };

      const response = await apiCall<CompleteSessionResponse>('/complete-session', 'POST', request);
      
      if (response.success) {
        // 更新会话状态为完成
        const completedSession: HumanInteractionSession = {
          ...session,
          status: InteractionSessionStatus.COMPLETED,
          updated_at: new Date(),
          statistics: response.final_statistics
        };
        
        updateSession(completedSession);
        message.success('验证会话完成，报告生成成功！');
        
        // 触发完成回调
        onSessionComplete?.(response.report_id, response.generated_report);
      }
    } catch (err) {
      console.error('完成会话失败:', err);
      message.error('完成验证会话失败');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [session, apiCall, updateSession, onSessionComplete]);

  // 暂停会话
  const pauseSession = useCallback(async () => {
    if (!session) return;

    try {
      await apiCall(`/sessions/${session.session_id}/pause`, 'POST');
      
      const pausedSession: HumanInteractionSession = {
        ...session,
        status: InteractionSessionStatus.PAUSED,
        updated_at: new Date()
      };
      
      updateSession(pausedSession);
      message.info('验证会话已暂停');
    } catch (err) {
      console.error('暂停会话失败:', err);
      message.error('暂停验证会话失败');
    }
  }, [session, apiCall, updateSession]);

  // 恢复会话
  const resumeSession = useCallback(async () => {
    if (!session) return;

    try {
      await apiCall(`/sessions/${session.session_id}/resume`, 'POST');
      
      const resumedSession: HumanInteractionSession = {
        ...session,
        status: InteractionSessionStatus.VALIDATING,
        updated_at: new Date()
      };
      
      updateSession(resumedSession);
      message.info('验证会话已恢复');
    } catch (err) {
      console.error('恢复会话失败:', err);
      message.error('恢复验证会话失败');
    }
  }, [session, apiCall, updateSession]);

  // 取消会话
  const cancelSession = useCallback(async () => {
    if (!session) return;

    try {
      await apiCall(`/sessions/${session.session_id}`, 'DELETE');
      
      const cancelledSession: HumanInteractionSession = {
        ...session,
        status: InteractionSessionStatus.CANCELLED,
        updated_at: new Date()
      };
      
      updateSession(cancelledSession);
      message.info('验证会话已取消');
    } catch (err) {
      console.error('取消会话失败:', err);
      message.error('取消验证会话失败');
    }
  }, [session, apiCall, updateSession]);

  // 获取验证进度
  const getValidationProgress = useCallback((): number => {
    if (!session?.validation_session) return 0;
    
    const { validated_fields, total_fields } = session.validation_session;
    return (validated_fields / total_fields) * 100;
  }, [session]);

  // 根据名称获取字段
  const getFieldByName = useCallback((fieldName: string): ExtractedField | undefined => {
    return session?.validation_session?.extracted_fields.find(
      field => field.field_name === fieldName
    );
  }, [session]);

  // 获取待验证字段
  const getPendingFields = useCallback((): ExtractedField[] => {
    if (!session?.validation_session) return [];
    
    return session.validation_session.extracted_fields.filter(
      field => field.validation_status === FieldValidationStatus.PENDING
    );
  }, [session]);

  // 获取已验证字段
  const getValidatedFields = useCallback((): ExtractedField[] => {
    if (!session?.validation_session) return [];
    
    return session.validation_session.extracted_fields.filter(
      field => [
        FieldValidationStatus.VALIDATED,
        FieldValidationStatus.MODIFIED
      ].includes(field.validation_status)
    );
  }, [session]);

  // 启动状态轮询
  const startPolling = useCallback(() => {
    if (pollTimerRef.current) {
      clearInterval(pollTimerRef.current);
    }

    pollTimerRef.current = setInterval(async () => {
      const currentSession = sessionRef.current;
      if (!currentSession || [
        InteractionSessionStatus.COMPLETED,
        InteractionSessionStatus.CANCELLED,
        InteractionSessionStatus.ERROR
      ].includes(currentSession.status)) {
        return;
      }

      try {
        const updatedSession = await apiCall<HumanInteractionSession>(
          `/sessions/${currentSession.session_id}`
        );
        updateSession(updatedSession);
      } catch (err) {
        console.error('轮询会话状态失败:', err);
      }
    }, pollInterval);
  }, [apiCall, updateSession, pollInterval]);

  // 停止轮询
  const stopPolling = useCallback(() => {
    if (pollTimerRef.current) {
      clearInterval(pollTimerRef.current);
      pollTimerRef.current = null;
    }
  }, []);

  // 监听会话状态变化，管理轮询
  useEffect(() => {
    if (session && [
      InteractionSessionStatus.VALIDATING,
      InteractionSessionStatus.WAITING_FOR_VALIDATION
    ].includes(session.status)) {
      startPolling();
    } else {
      stopPolling();
    }

    return stopPolling;
  }, [session?.status, startPolling, stopPolling]);

  // 清理定时器
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, [stopPolling]);

  return {
    // 状态
    session,
    isLoading,
    error,
    
    // 操作方法
    createSession,
    validateField,
    completeSession,
    pauseSession,
    resumeSession,
    cancelSession,
    
    // 工具方法
    getValidationProgress,
    getFieldByName,
    getPendingFields,
    getValidatedFields
  };
};

export default useHumanInteraction;