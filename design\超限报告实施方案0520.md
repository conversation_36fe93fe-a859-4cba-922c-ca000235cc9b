
### **轨道交通智能报告助手 - 产品开发实施方案**

**版本信息：** V1.0 (内部开发版)
**日期：** 2024年5月20日
**开发团队：** [桂林]
**目标受众：** 产品用户、AI代码生成工具 (Cursor, Windsurf) 及开发者本人

---

#### **1. 项目概述与开发目标**

* **项目名称：** 轨道交通智能报告助手
* **项目愿景：** 成为轨道交通设计师不可或缺的智能报告编写伙伴，通过AI技术显著提升报告编制效率和质量。
* **MVP目标 (2024年7月31日)：** 实现"典型设计报告"的智能生成核心流程。具体包括：
  * 支持Word/PDF文档上传、文本粘贴作为数据源。
  * AI智能抽取报告结构、关键数据点、表格内容。
  * 提供可视化界面，支持人工对抽取结果进行结构调整、数据修改和图片关联。
  * 基于Word模板，自动填充内容并保持排版。
  * 基础质量检查：错别字检查、规范引用版本过期提示。
  * 通过Docker Compose在Ubuntu服务器上稳定部署。
* **技术栈约束：** Python 3.12, Flask AppBuilder, PostgreSQL, Docker Compose, Qwen32B (RESTful API), PaddleOCR, MinerU等。
* **开发模式：** 单人团队，AI辅助编程（Cursor, Windsurf）。

#### **2. 系统架构**

本系统采用微服务（或模块化）架构思想，通过Docker容器化部署，实现各组件的解耦和独立运行。

##### **2.1 整体架构图**

```mermaid
graph TD
    subgraph User Interaction
        A[Web Frontend] --> B[Flask AppBuilder UI/API Gateway]
    end

    subgraph Backend Services
        B -- HTTP/REST --> C[Flask AppBuilder Application <Core Logic>]
        C -- Async Task Queue --> D[Celery Worker<s>]
    end

    subgraph AI & Processing Layer
        C -- HTTP/REST --> E[Qwen32B LLM Service RESTful API]
        D -- Local Call --> F[Document Processing & MCP Tools]
    end

    subgraph Data & Storage
        C -- SQLAlchemy ORM --> G[PostgreSQL Database]
        F -- File I/O --> H[Local File Storage-Mounted Volume]
    end

    subgraph External
        I[User Uploaded Documents] --> H
        J[Generated Reports] --> H
        K[Docker Compose] -- Orchestrates --> B & C & D & G & H
    end

    E -- Model Inference --> L[Pre-deployed Qwen32B Model]

    classDef default fill:#DDEBF7,stroke:#333,stroke-width:2px;
    classDef llm fill:#E0F7FA,stroke:#00BCD4;
    classDef db fill:#F0F4C3,stroke:#C0CA33;
    classDef storage fill:#F3E5F5,stroke:#9C27B0;
    class E llm;
    class G db;
    class H storage;
```

##### **2.2 模块职责与技术选型**

* **Web Frontend (Web UI):**
  * **职责：** 提供用户友好的交互界面，展示报告生成进度、编辑抽取结果、预览报告、报告修改等。
  * **技术：** Flask AppBuilder 内置的UI组件和基本的前端框架（Jinja2模板、HTML/CSS/JS）。遵循"左侧功能列表，右侧功能交互/输出"的布局。
* **Flask AppBuilder Application (Core Logic):**
  * **职责：** 后端核心业务逻辑，包括用户管理、项目管理、文档上传/管理、抽取结果存储与管理、报告生成调度、API路由。
  * **技术：** Python 3.12, Flask AppBuilder, SQLAlchemy ORM。
* **Celery Worker(s):**
  * **职责：** 处理耗时长的异步任务，如文档解析、AI抽取、报告生成、质量检查。避免阻塞主应用进程，提升用户体验，处理过程中需要向用户展示进度。
  * **技术：** Python 3.12, Celery (Broker: Redis, Backend: Redis)。
* **Qwen32B LLM Service:**
  * **职责：** 提供大模型推理服务，实现文档语义理解、关键信息抽取、文本生成等核心AI功能。
  * **技术：** Qwen32B (通过 RESTful API 调用)。**目前Qwen32B已独立部署在其他高配服务器，本系统仅作为客户端调用。**
* **Document Processing :**
  * **职责：** 负责文档的物理解析（文本、图片、表格提取）、OCR识别等。
  * **技术：** Python 3.12。
    * **Word (.docx) 解析/操作:** `python-docx`
    * **PDF (.pdf) 解析:** `MinerU` (用于文本、图片、布局提取，支持更多高级功能，如坐标定位) 。**这是一个重点攻克和优化点。**
    * **OCR:** `PaddleOCR` (对中文支持优异，可私有化部署)。
    * **表格生成:** 表格处理通过大模型原生能力完成，通过大模型对文本数据进行处理，按照要求生成json数据后，转换为表格对象，以便插入到word模板中合适位置。
* **PostgreSQL Database:**
  * **职责：** 存储所有结构化数据，包括项目元数据、用户数据、抽取结果（JSONB）、模板数据、任务状态等。
  * **技术：** PostgreSQL 独立部署。
* **Local File Storage:**
  * **职责：** 存储用户上传的原始文档、图片以及最终生成的报告文件。
  * **技术：** 服务器本地文件系统，通过Docker Volume进行持久化。

#### **3. 功能结构**

##### **3.1 核心功能模块**


    A[项目管理] --> A1[创建项目]
    A --> A2[选择项目]
    A --> A3[删除项目]
    A --> A4[项目模板管理]（模板增、删、改、查、下载）

    B[数据识别] --> B1[选择项目]
    B --> B2[选择模板]
    B --> B3[数据管理]（数据增、删、改、查、下载）

    C[报告成果] --> C1[选择项目]
    C --> C2[选择模板]
    C --> C3[报告管理]（报告增、删、改、查、下载）

    D[智能交互主界面] --> D0[状态切换]
    D1 --> D1a[检索状态]
    D1 --> D1b[模板分析]
    D1 --> D1c[信息提取]
    D1 --> D1d[报告生成]
    D --> D1[检索状态]
    D1 --> D1a[报告下载]
    D1 --> D1b[项目查询]
    D1 --> D1c[模板查询]
    D1 --> D1d[数据查询]
    D --> D2[模板分析]
    D2 --> D2a[智能分析报告模板]
    D2 --> D2b[模板交互式编辑]
    D2 --> D2c[模板保存]
    D --> D3[信息抽取]
    D3 --> D3a[智能信息分析]
    D3 --> D3b[抽取结果交互式编辑]
    D3 --> D3c[信息保存]
    D3 --> D3d[表格信息处理]
    D3 --> D3e[图片信息处理]
    D --> D4[报告生成]
    D4 --> D4a[信息完整度检查]
    D4 --> D4b[基于模板的信息智能填充]
    D4 --> D4c[智能排版]
    D4 --> D4d[报告保存]

    E[用户管理] --> E1[用户认证-登录/登出]
    E --> E2[权限管理-Flask AppBuilder]


##### **3.2 MVP阶段功能范围**

* **项目管理：** 创建、查看、编辑、删除项目。
* **数据源管理：** Word/PDF上传、文本粘贴、图片上传（用户手动标识）。
* **AI智能抽取：**
  * **DSR：** 识别《超限报告》的章节、标题层级（例如：1.工程概况、1.1工程概况、1.1.1效果图）。
  * **KIE：** 抽取报告中常见的关键数据点（如项目名称、建筑高度、设防烈度、建筑面积、结构类型、主要超限项、计算软件、材料强度等）。
  * **表格数据抽取：** 根据设计计算软件结果，根据既定逻辑生成表格。
  * **图片OCR：** 仅限识别图片中的文字（例如图号和图注）。
* **人工干预与编辑：**
  * **可视化结构编辑器：** 支持拖拽、增删改章节标题。XML导出/导入。
  * **关键数据点编辑器：** 列表/表单编辑，支持XML导出/导入。
  * **图片位置关联器：** 用户手动将上传图片与模板占位符关联。
* **报告模板管理：** 上传Word模板，系统识别 `{{占位符}}`。
* **报告智能生成：** 基于用户选择的Word模板，填充人工确认后的结构化数据和AI生成内容。继承Word模板样式，自动插入图片。
* **用户管理：** 基础的用户认证。

#### **4. 用户流程图与界面交互**

延续《产品设计说明书》中的用户旅程图，并融入"左侧功能列表，右侧功能交互/输出"的界面布局。

* **平台登录：** 
界面布局描述：用户名和密码登陆框
用户动作：利用账户和密码登录平台
特殊说明：用户相关数据都具有用户标签
数据说明：基于flask appbuilder权限管控体系实现

* **系统主界面：** 
界面布局描述：
左侧约1/10，从顶到底部为竖条状界面菜单栏，分别标注主界面、项目管理、模板管理、数据管理、结果管理、用户管理
右侧约9/10，为主要工作区，上部分3/5区域为结果交互区，用于交互式编辑，中间1/5区域为状态显示区，通过下拉列表分别选择项目，模板，数据，右侧显示当前大模型交互状态，状态包括：检索状态、模板分析、信息抽取、报告生成四个状态，下方1/5区为功能沟通区，此处用户输入不同的命令，完成大部分该产品功能的实现，上传需要处理的文件
用户动作：
1.点击左侧界面菜单，可以分别切换界面
2.结果交互区，系统反馈当前状态，处理结果，响应用户问答，且可以在该区域进行相关结果的编辑，编辑对象包括：文档模板识别结果，文档数据识别结果，文档生成结果
3.状态显示区，通过下拉列表可以选择项目、模板、数据，切换状态，已显示当前情况；
4.功能沟通区，分别在四中不同的状态中，与系统进行通讯，可以实现的功能如下：
4.1 检索状态
前置条件：
通过自然语言，检索系统中目前已有的项目信息，模板信息，数据信息，报告信息，查询后在结果交互区进行展示，所有结果不可编辑
通过自然语言，切换到其他状态，如果满足前置条件，则可以进行切换
4.2 模板分析
前置条件：具有被选择的项目，上传了docx或者doc文档   或者   选择了具体模板
通过自然语言，启动分析功能对上传的文档结构和数据构成进行分析，形成结果展示在结果交互区，编辑完成后，通过自然语言让系统进行保存
如果没有上传文档数据，但选择了具体模板的，则将模板导入结果交互区进行编辑，编辑完成后可选择是否保存，或者另存为
4.3 信息抽取
前置条件：选择了具体模板，上传了docx、doc、jpg、txt等可能存在信息的数据文件
通过自然语言，启动数据抽取功能，对上传了的数据进行相关的处理，按照模板要求，形成生成报告所需的各类数据，抽取完成后，形成结果展示在结果交互区进行编辑，完成后可选择是否保存，或者另存为
4.4 报告生成
前置条件：选择了具体模板，选择了具体数据
通过自然语言，启动报告生成功能，将数据填充到具体的模板中，形成新的报告，模板文件以docx文档存储，以减少工作量，生成报告后，将报告展示在结果交互区，可以自动进行排版，生成用户需要的结果文件，生成报告的过程中，除了常规的文字数据外，还有表格和图片数据，表格，图片可以根据名称自动排版到模板合适位置

* **项目管理：** 
界面布局描述：
左侧界面菜单栏中，项目管理高亮，右侧按照列表显示项目，可对项目进行增删改查，项目具有项目名称、创建人、创建时间、项目描述、关联模板数、关联数据数量、最后生成报告时间信息
用户动作：
1.点击表头对项目进行排序；
2.点击编辑可打开项目详情对话框，对项目进行编辑，包括项目名称、项目描述
3.点击具体项目相关模板数量，可跳转到模板管理，自动选择本项目
4.点击具体项目相关数据数量，可跳转到数据管理，自动选择本项目

* **模板管理：** 
界面布局描述：
左侧界面菜单栏中，模板管理高亮，右侧按照列表显示所有模板，可对模板进行增删改查和下载。模板列表包含模板名称、所属项目、创建人、创建时间、模板描述、占位符数量、被引用次数等信息。
用户动作：
1. 点击表头可对模板进行排序；
2. 点击新增模板按钮，弹出上传Word模板对话框，填写模板名称、描述并上传文件；
3. 点击模板名称可查看模板详情，包括占位符预览、模板结构树、被引用项目列表等；
4. 点击编辑按钮可修改模板名称、描述，或重新上传模板文件；
5. 点击删除按钮可删除模板（如被项目引用需二次确认）；
6. 点击下载按钮可下载模板原文件；
7. 点击占位符数量可弹出占位符详情列表，支持复制占位符名。

* **数据管理：** 
界面布局描述：
左侧界面菜单栏中，数据管理高亮，右侧以列表形式展示所有数据源，支持增删改查和下载。数据列表包含数据名称、所属项目、关联模板、上传人、上传时间、来源数据（Word/PDF/图片/文本）、抽取结果预览等信息。
用户动作：
1. 点击数据名称可查看数据详情，包括原文件预览、抽取结果、抽取日志等；
2. 点击编辑按钮可修改数据名称、描述，或重新上传数据文件；
3. 点击删除按钮可删除数据（如已被报告引用需二次确认）；
4. 点击抽取结果预览可展开查看结构化数据、表格、图片等内容；
5. 支持批量选择数据进行删除或下载操作。

* **报告管理：** 
界面布局描述：
左侧界面菜单栏中，结果管理（报告管理）高亮，右侧以列表形式展示所有已生成报告，支持增删改查和下载。报告列表包含报告名称、所属项目、关联模板、关联数据、生成时间、生成状态、报告版本、报告预览等信息。
用户动作：
1. 点击生成报告按钮，返回主界面，选择项目、模板、数据，填写报告名称、描述，启动报告生成流程；
2. 点击报告名称可查看报告详情，包括报告预览、生成日志、填充数据明细等；
3. 点击下载按钮可下载报告Word文件；
4. 点击删除按钮可删除报告（需二次确认）；
5. 点击报告预览可在线浏览报告内容，支持分页、跳转、放大缩小等操作；

* **用户管理：** 
界面布局描述：
左侧界面菜单栏中，用户管理高亮，右侧以列表形式展示所有用户信息，支持用户的增删改查和权限分配。用户列表包含用户名、姓名、角色、所属部门、创建时间、最后登录时间、状态（启用/禁用）等信息。
用户动作：
1. 点击新增用户按钮，弹出新建用户对话框，填写用户名、姓名、密码、角色、部门等信息；
2. 点击用户名可查看用户详情，包括基本信息、历史操作记录、所属项目等；
3. 点击编辑按钮可修改用户信息、重置密码、调整角色权限；
4. 点击删除按钮可删除用户（需二次确认）；
5. 点击状态切换按钮可启用/禁用用户账号；
6. 支持批量选择用户进行删除或状态切换操作。

#### **5. 模块设计与API接口**

##### **5.1 后端API接口 (Flask AppBuilder)**

所有API接口都应遵循RESTful原则，使用JSON进行数据交换，并强制使用Python类型提示。

* **项目管理 (Project Management)**
  * `GET /api/projects` - 获取项目列表
    * 查询参数: `page`, `page_size`, `search_term`, `sort_field`, `sort_direction`
    * 响应: `{"count": int, "result": [{"id": int, "name": string, "description": string, "creator": string, "created_on": datetime, "template_count": int, "data_source_count": int, "last_report_generated": datetime}, ...]}`
    * 权限: `can_list`
  * `POST /api/projects` - 创建新项目
    * 请求体: `{"name": string, "description": string}`
    * 响应: `{"id": int, "name": string, "description": string, ...}`
    * 权限: `can_add`
  * `GET /api/projects/{id}` - 获取项目详情
    * 响应: `{"id": int, "name": string, "description": string, "templates": [...], "data_sources": [...], "reports": [...]}`
    * 权限: `can_show`
  * `PUT /api/projects/{id}` - 更新项目信息
    * 请求体: `{"name": string, "description": string}`
    * 响应: `{"id": int, "name": string, "description": string, ...}`
    * 权限: `can_edit`
  * `DELETE /api/projects/{id}` - 删除项目
    * 响应: `{"message": string, "success": boolean}`
    * 权限: `can_delete`

* **数据源管理 (Document/Data Source Management)**
  * `GET /api/data_sources` - 获取数据源列表
    * 查询参数: `page`, `page_size`, `project_id`, `type`, `search_term`
    * 响应: `{"count": int, "result": [{"id": int, "name": string, "project_id": int, "type": string, "uploaded_by": string, "uploaded_on": datetime, "extraction_status": string}, ...]}`
    * 权限: `can_list`
  * `POST /api/data_sources` - 上传新数据源
    * 请求体: `multipart/form-data` 包含 `file`, `name`, `description`, `project_id`, `type`
    * 响应: `{"id": int, "name": string, "project_id": int, ...}`
    * 权限: `can_add`
  * `GET /api/data_sources/{id}` - 获取数据源详情
    * 响应: `{"id": int, "name": string, "project_id": int, "file_path": string, "extraction_results": {...}, ...}`
    * 权限: `can_show`
  * `DELETE /api/data_sources/{id}` - 删除数据源
    * 响应: `{"message": string, "success": boolean}`
    * 权限: `can_delete`
  * `POST /api/data_sources/{id}/extract` - 启动数据抽取任务
    * 请求体: `{"template_id": int, "extraction_type": string}`
    * 响应: `{"task_id": string, "status": string}`
    * 权限: `can_extract`
  * `GET /api/data_sources/{id}/extract_results` - 获取抽取结果
    * 响应: `{"data_points": {...}, "structure": [...], "tables": [...], "images": [...]}`
    * 权限: `can_show`
  * `PUT /api/data_sources/{id}/extract_results` - 更新抽取结果
    * 请求体: `{"data_points": {...}, "structure": [...], "tables": [...], "images": [...]}`
    * 响应: `{"message": string, "success": boolean}`
    * 权限: `can_edit`

* **抽取结果与人工干预 (Extracted Data & Manual Intervention)**
  * `GET /api/extraction_tasks` - 获取抽取任务列表
    * 查询参数: `page`, `page_size`, `status`, `project_id`
    * 响应: `{"count": int, "result": [{"id": string, "data_source_id": int, "status": string, "created_on": datetime, "completed_on": datetime}, ...]}`
    * 权限: `can_list`
  * `GET /api/extraction_tasks/{id}` - 获取抽取任务详情
    * 响应: `{"id": string, "data_source_id": int, "status": string, "progress": float, "log": string, "results": {...}}`
    * 权限: `can_show`
  * `POST /api/extraction_tasks/{id}/cancel` - 取消抽取任务
    * 响应: `{"message": string, "success": boolean}`
    * 权限: `can_cancel`
  * `GET /api/extracted_data/{id}` - 获取结构化抽取数据
    * 响应: `{"data_points": {...}, "structure": [...], "tables": [...], "images": [...]}`
    * 权限: `can_show`
  * `PUT /api/extracted_data/{id}` - 更新结构化抽取数据
    * 请求体: `{"data_points": {...}, "structure": [...], "tables": [...], "images": [...]}`
    * 响应: `{"message": string, "success": boolean}`
    * 权限: `can_edit`

* **模板管理 (Template Management)**
  * `GET /api/templates` - 获取模板列表
    * 查询参数: `page`, `page_size`, `project_id`, `search_term`
    * 响应: `{"count": int, "result": [{"id": int, "name": string, "project_id": int, "created_by": string, "created_on": datetime, "placeholder_count": int, "reference_count": int}, ...]}`
    * 权限: `can_list`
  * `POST /api/templates` - 上传新模板
    * 请求体: `multipart/form-data` 包含 `file`, `name`, `description`, `project_id`
    * 响应: `{"id": int, "name": string, "project_id": int, ...}`
    * 权限: `can_add`
  * `GET /api/templates/{id}` - 获取模板详情
    * 响应: `{"id": int, "name": string, "project_id": int, "file_path": string, "placeholders": [...], "structure": {...}}`
    * 权限: `can_show`
  * `PUT /api/templates/{id}` - 更新模板信息
    * 请求体: `multipart/form-data` 可包含 `file`, `name`, `description`
    * 响应: `{"id": int, "name": string, "project_id": int, ...}`
    * 权限: `can_edit`
  * `DELETE /api/templates/{id}` - 删除模板
    * 响应: `{"message": string, "success": boolean}`
    * 权限: `can_delete`
  * `GET /api/templates/{id}/placeholders` - 获取模板占位符列表
    * 响应: `{"placeholders": [{"name": string, "type": string, "location": {...}}, ...]}`
    * 权限: `can_show`

* **报告生成 (Report Generation)**
  * `POST /api/reports` - 创建新报告任务
    * 请求体: `{"name": string, "description": string, "project_id": int, "template_id": int, "data_source_id": int}`
    * 响应: `{"id": int, "task_id": string, "status": string}`
    * 权限: `can_add`
  * `GET /api/reports` - 获取报告列表
    * 查询参数: `page`, `page_size`, `project_id`, `status`, `search_term`
    * 响应: `{"count": int, "result": [{"id": int, "name": string, "project_id": int, "template_id": int, "data_source_id": int, "status": string, "created_on": datetime}, ...]}`
    * 权限: `can_list`
  * `GET /api/reports/{id}` - 获取报告详情
    * 响应: `{"id": int, "name": string, "project_id": int, "template_id": int, "data_source_id": int, "file_path": string, "generation_log": string, "status": string, ...}`
    * 权限: `can_show`
  * `DELETE /api/reports/{id}` - 删除报告
    * 响应: `{"message": string, "success": boolean}`
    * 权限: `can_delete`
  * `GET /api/reports/{id}/download` - 下载报告文件
    * 响应: `application/vnd.openxmlformats-officedocument.wordprocessingml.document` (Word文档)
    * 权限: `can_download`
  * `GET /api/report_tasks/{id}` - 获取报告生成任务状态
    * 响应: `{"id": string, "report_id": int, "status": string, "progress": float, "log": string}`
    * 权限: `can_show`

##### **5.2 后端-AI服务接口 (Qwen32B RESTful API)**

这些是后端应用或Celery Worker调用的Qwen32B的API。

* **文档结构识别 (DSR)**
  * `POST /llm/extract_document_structure`: 从原始文本中提取文档层级结构和占位符位置。
    * `Request Body`: 
    ```json
    {
      "document_text": "string",
      "max_depth": 3,
      "extract_placeholders": true
    }
    ```
    * `Response`: 
    ```json
    {
      "structure": [
        {
          "title": "string", 
          "level": int, 
          "content_type": "text|table|image", 
          "key": "string",
          "children": [...]
        }, 
        ...
      ],
      "placeholders": [
        {
          "name": "string",
          "location": {
            "section": "string",
            "context": "string"
          }
        },
        ...
      ]
    }
    ```

* **关键信息抽取 (KIE)**
  * `POST /llm/extract_key_information`: 从给定文本中抽取预定义的关键数据点。
    * `Request Body`: 
    ```json
    {
      "context_text": "string", 
      "target_data_points": ["项目名称", "建筑高度", ...],
      "domain_context": "structural_engineering"
    }
    ```
    * `Response`: 
    ```json
    {
      "projectName": "string", 
      "buildingHeight": "float",
      "confidence_scores": {
        "projectName": 0.98,
        "buildingHeight": 0.92
      },
      ...
    }
    ```

* **表格数据抽取 (Table Extraction - LLM Assisted)**
  * `POST /llm/extract_table_data`: (辅助功能) 给定表格的OCR文本，LLM辅助结构化。
    * `Request Body`: 
    ```json
    {
      "table_raw_text": "string", 
      "table_context": "string",
      "expected_columns": ["column1", "column2", ...]
    }
    ```
    * `Response`: 
    ```json
    {
      "table_data": [
        {"column1": "value1", "column2": "value2", ...},
        ...
      ], 
      "identified_headers": ["column1", "column2", ...],
      "confidence": 0.95
    }
    ```

* **文本生成/润色 (Content Generation)**
  * `POST /llm/generate_content`: 根据结构化数据和上下文生成或润色报告段落。
    * `Request Body`: 
    ```json
    {
      "prompt": "string", 
      "context_data": {...}, 
      "output_format": "text|markdown",
      "tone": "formal|technical|summary",
      "max_length": 500
    }
    ```
    * `Response`: 
    ```json
    {
      "generated_text": "string",
      "token_count": 324
    }
    ```

* **通用文本理解 (General Text Understanding)**
  * `POST /llm/understand_text`: 辅助错别字检查或语义理解。
    * `Request Body`: 
    ```json
    {
      "text": "string", 
      "task": "spell_check|semantic_analysis|term_extraction",
      "domain": "structural_engineering"
    }
    ```
    * `Response`: 
    ```json
    {
      "corrections": [
        {"original": "string", "corrected": "string", "position": [start, end]},
        ...
      ], 
      "suggestions": [
        {"text": "string", "confidence": 0.92},
        ...
      ],
      "extracted_terms": [
        {"term": "string", "category": "string", "definition": "string"},
        ...
      ]
    }
    ```

#### **6. 数据库设计 (PostgreSQL)**

所有敏感文本字段考虑使用PostgreSQL的文本搜索功能（FTS）。

##### **6.1 核心数据表**

* **用户表 (ab_user)**
  * Flask AppBuilder自带的用户表，包含用户认证和权限信息
  * 关键字段: `id`, `first_name`, `last_name`, `username`, `password`, `active`, `email`, `last_login`, `login_count`, `fail_login_count`, `created_on`, `changed_on`, `created_by_fk`, `changed_by_fk`

* **项目表 (projects)**
  * 存储项目基本信息
  * 字段:
    * `id`: SERIAL PRIMARY KEY
    * `name`: VARCHAR(255) NOT NULL
    * `description`: TEXT
    * `created_on`: TIMESTAMP DEFAULT NOW()
    * `updated_on`: TIMESTAMP DEFAULT NOW()
    * `created_by_id`: INTEGER REFERENCES ab_user(id)
    * `updated_by_id`: INTEGER REFERENCES ab_user(id)

* **模板表 (templates)**
  * 存储报告模板信息
  * 字段:
    * `id`: SERIAL PRIMARY KEY
    * `name`: VARCHAR(255) NOT NULL
    * `description`: TEXT
    * `file_path`: VARCHAR(512) NOT NULL
    * `file_size`: INTEGER
    * `file_type`: VARCHAR(50)
    * `project_id`: INTEGER REFERENCES projects(id)
    * `created_on`: TIMESTAMP DEFAULT NOW()
    * `created_by_id`: INTEGER REFERENCES ab_user(id)
    * `updated_on`: TIMESTAMP DEFAULT NOW()
    * `updated_by_id`: INTEGER REFERENCES ab_user(id)

* **模板占位符表 (template_placeholders)**
  * 存储模板中的占位符信息
  * 字段:
    * `id`: SERIAL PRIMARY KEY
    * `template_id`: INTEGER REFERENCES templates(id)
    * `name`: VARCHAR(255) NOT NULL
    * `type`: VARCHAR(50) DEFAULT 'text'
    * `location_data`: JSONB
    * `default_value`: TEXT

* **数据源表 (data_sources)**
  * 存储上传的文档数据源
  * 字段:
    * `id`: SERIAL PRIMARY KEY
    * `name`: VARCHAR(255) NOT NULL
    * `description`: TEXT
    * `file_path`: VARCHAR(512)
    * `file_size`: INTEGER
    * `file_type`: VARCHAR(50)
    * `content_type`: VARCHAR(50)
    * `project_id`: INTEGER REFERENCES projects(id)
    * `created_on`: TIMESTAMP DEFAULT NOW()
    * `created_by_id`: INTEGER REFERENCES ab_user(id)
    * `updated_on`: TIMESTAMP DEFAULT NOW()
    * `updated_by_id`: INTEGER REFERENCES ab_user(id)

* **抽取任务表 (extraction_tasks)**
  * 存储数据抽取任务信息
  * 字段:
    * `id`: VARCHAR(36) PRIMARY KEY
    * `data_source_id`: INTEGER REFERENCES data_sources(id)
    * `template_id`: INTEGER REFERENCES templates(id) NULL
    * `status`: VARCHAR(50) DEFAULT 'pending'
    * `progress`: FLOAT DEFAULT 0.0
    * `log`: TEXT
    * `created_on`: TIMESTAMP DEFAULT NOW()
    * `completed_on`: TIMESTAMP NULL
    * `created_by_id`: INTEGER REFERENCES ab_user(id)

* **抽取结果表 (extraction_results)**
  * 存储数据抽取的结果
  * 字段:
    * `id`: SERIAL PRIMARY KEY
    * `extraction_task_id`: VARCHAR(36) REFERENCES extraction_tasks(id)
    * `data_source_id`: INTEGER REFERENCES data_sources(id)
    * `structure_data`: JSONB
    * `key_data_points`: JSONB
    * `table_data`: JSONB
    * `image_data`: JSONB
    * `created_on`: TIMESTAMP DEFAULT NOW()
    * `updated_on`: TIMESTAMP DEFAULT NOW()
    * `created_by_id`: INTEGER REFERENCES ab_user(id)
    * `updated_by_id`: INTEGER REFERENCES ab_user(id)

* **报告表 (reports)**
  * 存储生成的报告信息
  * 字段:
    * `id`: SERIAL PRIMARY KEY
    * `name`: VARCHAR(255) NOT NULL
    * `description`: TEXT
    * `file_path`: VARCHAR(512)
    * `file_size`: INTEGER
    * `project_id`: INTEGER REFERENCES projects(id)
    * `template_id`: INTEGER REFERENCES templates(id)
    * `data_source_id`: INTEGER REFERENCES data_sources(id)
    * `extraction_result_id`: INTEGER REFERENCES extraction_results(id)
    * `status`: VARCHAR(50) DEFAULT 'draft'
    * `version`: VARCHAR(50) DEFAULT 'v1.0'
    * `created_on`: TIMESTAMP DEFAULT NOW()
    * `updated_on`: TIMESTAMP DEFAULT NOW()
    * `created_by_id`: INTEGER REFERENCES ab_user(id)
    * `updated_by_id`: INTEGER REFERENCES ab_user(id)

* **报告生成任务表 (report_tasks)**
  * 存储报告生成任务信息
  * 字段:
    * `id`: VARCHAR(36) PRIMARY KEY
    * `report_id`: INTEGER REFERENCES reports(id)
    * `status`: VARCHAR(50) DEFAULT 'pending'
    * `progress`: FLOAT DEFAULT 0.0
    * `log`: TEXT
    * `created_on`: TIMESTAMP DEFAULT NOW()
    * `completed_on`: TIMESTAMP NULL
    * `created_by_id`: INTEGER REFERENCES ab_user(id)

##### **6.2 数据库关系图**

```
+------------+     +---------------+     +--------------------+
|  projects  |<----|  templates    |<----|template_placeholders|
+------------+     +---------------+     +--------------------+
      ^                   ^
      |                   |
      |                   |
+------------+     +---------------+     +-------------------+
|data_sources|---->|extraction_tasks|--->|extraction_results |
+------------+     +---------------+     +-------------------+
      ^                                        ^
      |                                        |
      |                                        |
+------------+     +---------------+           |
|  reports   |---->| report_tasks  |-----------+
+------------+     +---------------+
```

##### **6.3 索引设计**

* 项目表 (projects)
  * 创建索引: `CREATE INDEX idx_projects_name ON projects(name);`
  * 创建索引: `CREATE INDEX idx_projects_created_by ON projects(created_by_id);`

* 模板表 (templates)
  * 创建索引: `CREATE INDEX idx_templates_name ON templates(name);`
  * 创建索引: `CREATE INDEX idx_templates_project_id ON templates(project_id);`

* 数据源表 (data_sources)
  * 创建索引: `CREATE INDEX idx_data_sources_name ON data_sources(name);`
  * 创建索引: `CREATE INDEX idx_data_sources_project_id ON data_sources(project_id);`
  * 创建索引: `CREATE INDEX idx_data_sources_file_type ON data_sources(file_type);`

* 抽取任务表 (extraction_tasks)
  * 创建索引: `CREATE INDEX idx_extraction_tasks_data_source_id ON extraction_tasks(data_source_id);`
  * 创建索引: `CREATE INDEX idx_extraction_tasks_status ON extraction_tasks(status);`

* 抽取结果表 (extraction_results)
  * 创建索引: `CREATE INDEX idx_extraction_results_data_source_id ON extraction_results(data_source_id);`
  * 创建全文搜索索引: `CREATE INDEX idx_extraction_results_structure_data ON extraction_results USING GIN (structure_data);`
  * 创建全文搜索索引: `CREATE INDEX idx_extraction_results_key_data_points ON extraction_results USING GIN (key_data_points);`

* 报告表 (reports)
  * 创建索引: `CREATE INDEX idx_reports_name ON reports(name);`
  * 创建索引: `CREATE INDEX idx_reports_project_id ON reports(project_id);`
  * 创建索引: `CREATE INDEX idx_reports_template_id ON reports(template_id);`
  * 创建索引: `CREATE INDEX idx_reports_data_source_id ON reports(data_source_id);`
  * 创建索引: `CREATE INDEX idx_reports_status ON reports(status);`

#### **7. 关键数据点定义**

结合两份《超限报告》样本，定义核心关键数据点，并考虑其存储类型和用途。这将直接指导AI抽取模型和报告填充逻辑。

| **数据点名称 (英文键名)**                               | **定义**                                      | **示例值 (来自报告)**                                                  | **存储类型 (在 `key_data_points` JSONB中)** | **利用方式**       |
| :------------------------------------------------------------ | :-------------------------------------------------- | :--------------------------------------------------------------------------- | :-------------------------------------------------- | :----------------------- |
| `projectName` (项目名称)                                    | 工程的正式名称，用于报告标题和正文引用。            | 白云(棠溪)站综合交通枢纽一体化工程西地块场站综合体项目A2 楼栋（北塔）        | string                                              | 报告标题、封面、正文填充 |
| `constructionUnit` (建设单位)                               | 负责项目建设的单位全称。                            | 广州市云胜房地产开发有限公司 / 广州地铁集团有限公司                          | string                                              | 报告封面、正文填充       |
| `designUnit` (设计单位)                                     | 负责项目设计工作的单位全称。                        | 广州地铁设计院股份有限公司                                                   | string                                              | 报告封面、正文填充       |
| `designer` (设计人/计算人)                                  | 报告的设计人或计算人姓名。                          | 刘齐霞                                                                       | string                                              | 报告落款                 |
| `checker` (校核人)                                          | 报告的校核人姓名。                                  | 李颖平                                                                       | string                                              | 报告落款                 |
| `projectManager` (专业负责人)                               | 报告的专业负责人姓名。                              | 李红波                                                                       | string                                              | 报告落款                 |
| `reviewer` (审核人)                                         | 报告的审核人姓名。                                  | 李红波                                                                       | string                                              | 报告落款                 |
| `approver` (审定人)                                         | 报告的审定人姓名。                                  | 伍永胜                                                                       | string                                              | 报告落款                 |
| `reportDate` (报告日期)                                     | 报告编制的日期。                                    | 2022 年 10 月                                                                | string (或date)                                     | 报告封面、日期更新       |
| `reportVersion` (版本)                                      | 报告的版本信息。                                    | V0.9 / 2016 年版                                                             | string                                              | 报告封面                 |
| `totalBuildingArea` (总建筑面积)                            | 地上总建筑面积，单位m²。                           | 69047m²                                                                     | float                                               | 工程概况描述             |
| `mainStructureHeight` (主结构高度)                          | 主体结构从地下室顶板或室外地面算起的总高度，单位m。 | 145.3m                                                                       | float                                               | 工程概况描述、超限判别   |
| `mainStructureStories` (主结构层数)                         | 主体结构地上总层数。                                | 31                                                                           | int                                                 | 工程概况描述、超限判别   |
| `undergroundStories` (地下层数)                             | 地下室层数。                                        | 4                                                                            | int                                                 | 工程概况描述             |
| `seismicFortificationIntensity` (抗震设防烈度)              | 建筑抗震设防烈度，单位度。                          | 7 度( 0.1g)                                                                  | string                                              | 设计条件、超限判别       |
| `siteClass` (场地类别)                                      | 建筑场地的地震分类。                                | II 类 / III 类                                                               | string                                              | 设计条件、超限判别       |
| `characteristicPeriod` (特征周期)                           | 场地特征周期，单位s。                               | 0.35s / 0.45s                                                                | float                                               | 设计条件                 |
| `structureType` (结构类型)                                  | 建筑物主要结构体系类型。                            | 框架-核心筒结构 / 钢筋混凝土剪力墙结构                                       | string                                              | 工程概况、超限判别       |
| `primaryOverlimitItems` (主要超限项)                        | 报告中识别出的主要超限类型列表。                    | 超 A 级高度建筑, 扭转不规则, 楼板不连续, 局部不规则                          | array of string                                     | 超限情况总结             |
| `calculationSoftware` (计算软件)                            | 报告中使用的结构计算软件列表。                      | YJK, MIDAS Building, SAUSAGE                                                 | array of string                                     | 技术对策                 |
| `materialStrength.beamSlab` (材料强度-梁板)                 | 梁板混凝土强度等级。                                | C30 / C35                                                                    | string                                              | 设计条件、计算参数       |
| `materialStrength.coreWallColumn` (材料强度-核心筒剪力墙柱) | 核心筒剪力墙和柱混凝土强度等级范围。                | C60~C35 / C60                                                                | string                                              | 设计条件、计算参数       |
| `materialStrength.frameColumn` (材料强度-外框柱)            | 外框柱混凝土强度等级范围。                          | C80~C50 / C60                                                                | string                                              | 设计条件、计算参数       |
| `materialStrength.beamSection` (材料强度-梁截面)            | 报告中列举的梁截面尺寸示例。                        | 200x400、200x500、...                                                        | string (可进一步解析为列表)                         | 计算参数                 |
| `liveLoad.office` (活载-办公)                               | 办公区域的活荷载取值，单位kN/m²。                  | 2.2                                                                          | float                                               | 荷载取值                 |
| `referenceNorm.GB50011-2010` (规范引用-抗震规范)            | 抗震设计规范的完整编号及版本。                      | GB50011-2010（2016年版）                                                     | string                                              | 设计依据、质量检查       |
| `table_data.楼面屋面荷载取值表` (表格数据-楼面屋面荷载)     | 报告中特定表格的结构化数据。                        | （见下方表格数据结构）                                                       | array of objects                                    | 报告中表格填充           |
| `image_ref.总平面图` (图片引用-总平面图)                    | 报告中引用图片的编号及图注。                        | 图1.1-1 白云(棠溪)站综合交通枢纽一体化工程建设项目西地块场站综合体项目总平面 | object {file_id: string, caption: string}           | 报告图片插入             |

#### **8. 部署方案 (Docker Compose)**

部署环境：Ubuntu Server 22.04 LTS, 16GB RAM, 4 Cores, 60GB Storage。
使用Docker Compose进行容器编排，确保所有服务独立运行且易于管理。

##### **8.1 容器组件**

1. **Web应用服务 (web)**
   * 基于Python 3.12和Flask AppBuilder的Web服务
   * 暴露端口: 8080
   * 连接PostgreSQL、Redis和文件存储

2. **Celery Worker服务 (worker)**
   * 处理后台任务的Celery Worker
   * 连接Redis作为Broker和Backend
   * 连接PostgreSQL和文件存储

3. **Redis服务 (redis)**
   * 用于Celery消息队列和缓存
   * 持久化配置: RDB + AOF

4. **PostgreSQL服务 (postgres)**
   * 数据库服务
   * 持久化卷: `/var/lib/postgresql/data`

5. **NGINX服务 (nginx)**
   * 前端反向代理
   * 静态文件服务
   * SSL终止

##### **8.2 Docker Compose配置**

**`docker-compose.yml` (示例):**

```yaml
version: '3.8'

services:
  web:
    build: 
      context: .
      dockerfile: Dockerfile
    restart: always
    ports:
      - "8080:8080"
    volumes:
      - ./app:/app
      - ./data/files:/app/files
    depends_on:
      - postgres
      - redis
    environment:
      - FLASK_APP=app.app:app
      - FLASK_ENV=production
      - DATABASE_URI=********************************************/report_assistant
      - REDIS_URI=redis://redis:6379/0
      - LLM_API_ENDPOINT=http://llm-service:8000
    command: gunicorn -w 4 -b 0.0.0.0:8080 app.app:app

  worker:
    build: 
      context: .
      dockerfile: Dockerfile
    restart: always
    volumes:
      - ./app:/app
      - ./data/files:/app/files
    depends_on:
      - postgres
      - redis
    environment:
      - DATABASE_URI=********************************************/report_assistant
      - REDIS_URI=redis://redis:6379/0
      - LLM_API_ENDPOINT=http://llm-service:8000
    command: celery -A app.celery_app:app worker -l info

  postgres:
    image: postgres:14
    restart: always
    environment:
      - POSTGRES_PASSWORD=password
      - POSTGRES_USER=postgres
      - POSTGRES_DB=report_assistant
    volumes:
      - postgres-data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7
    restart: always
    volumes:
      - redis-data:/data
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes

  nginx:
    image: nginx:latest
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
      - ./app/static:/usr/share/nginx/html/static
    depends_on:
      - web

volumes:
  postgres-data:
  redis-data:
```

##### **8.3 Dockerfile**

```dockerfile
FROM python:3.12-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY app/ /app/

# 创建文件目录
RUN mkdir -p /app/files

# 设置环境变量
ENV PYTHONPATH="/app:${PYTHONPATH}"

# 暴露端口
EXPOSE 8080

# 入口命令在docker-compose中指定
```

**`requirements.txt` (示例):**

```
Flask==2.3.3
flask-appbuilder==4.3.6
SQLAlchemy==2.0.23
psycopg2-binary==2.9.9
celery==5.3.4
redis==5.0.1
gunicorn==21.2.0
python-docx==1.0.1
requests==2.31.0
pandas==2.1.1
numpy==1.26.0
paddleocr==2.7.0
Pillow==10.1.0
```

#### **9. AI辅助开发指导**

##### **9.1 前端代码生成**

Flask AppBuilder提供了基于模型的自动视图生成功能，结合AI辅助，可以显著提高开发效率：

1. **视图组件生成**
   * 使用AI生成Flask AppBuilder的视图类、表单、过滤器
   * 示例提示模板:
     ```
     为PostgreSQL数据表 {table_name} 创建一个完整的Flask AppBuilder ModelView类，
     包含以下字段: {fields}。
     添加适当的权限装饰器、列表和显示列、编辑表单、添加表单、过滤器等。
     ```

2. **自定义界面生成**
   * 使用AI生成复杂布局的HTML/Jinja2模板
   * 示例提示模板:
     ```
     为{feature_name}功能创建一个Flask Jinja2模板，遵循左侧菜单布局，
     右侧内容区包含: {elements_description}。
     使用Bootstrap 4的组件和样式，确保响应式设计。
     ```

3. **前端交互组件**
   * 使用AI生成JavaScript交互功能
   * 示例提示模板:
     ```
     创建一个JavaScript函数，实现{interaction_description}功能。
     使用Fetch API与后端API {endpoint} 交互，处理成功和错误情况。
     ```

##### **9.2 后端代码生成**

1. **模型类生成**
   * 使用AI生成SQLAlchemy模型类
   * 示例提示模板:
     ```
     根据以下数据库表设计创建SQLAlchemy模型类:
     表名: {table_name}
     字段: {field_definitions}
     关系: {relationships}
     包含必要的导入、类型声明、关系定义和Flask-AppBuilder集成。
     ```

2. **API端点生成**
   * 使用AI生成RESTful API视图
   * 示例提示模板:
     ```
     为{resource_name}资源创建一个Flask AppBuilder REST API视图类，
     支持以下操作: {operations}
     包含适当的权限控制、输入验证和错误处理。
     ```

3. **业务逻辑生成**
   * 使用AI生成复杂业务逻辑函数
   * 示例提示模板:
     ```
     创建一个Python函数，实现{business_logic_description}。
     函数签名为: {function_signature}
     包含详细的错误处理、日志记录和返回合适的结果。
     ```

4. **Celery任务生成**
   * 使用AI生成异步任务代码
   * 示例提示模板:
     ```
     创建一个Celery任务函数，处理{task_description}。
     实现进度报告、错误处理和任务重试机制。
     ```

##### **9.3 LLM提示工程**

为确保与Qwen32B有效交互，需要精心设计提示模板：

1. **文档结构识别提示**
   * 示例:
     ```
     我需要你从以下轨道交通超限报告文本中提取文档结构。
     识别章节标题、层级关系和内容类型(文本/表格/图片)。
     输出应是JSON格式，包含每个章节的标题、级别、内容类型。
     文本内容: {document_text}
     ```

2. **关键信息抽取提示**
   * 示例:
     ```
     从以下轨道交通超限报告文本中抽取关键工程参数:
     - 项目名称
     - 建筑高度
     - 结构类型
     - 抗震设防烈度
     ...
     文本内容: {context_text}
     以JSON格式输出，对每个抽取结果提供信心分数。
     ```

3. **表格数据处理提示**
   * 示例:
     ```
     将以下OCR识别的表格文本转换为结构化表格数据:
     表格文本: {table_raw_text}
     表格上下文: {table_context}
     期望的列名: {expected_columns}
     输出应是JSON格式的对象数组，每个对象表示一行数据。
     ```

4. **AI写作与润色提示**
   * 示例:
     ```
     根据以下超限报告数据点，生成"{section_name}"章节的正式技术内容:
     项目名称: {project_name}
     建筑高度: {building_height}
     ...
     内容应符合超限报告的技术规范和专业术语，长度约{length}字。
     ```

##### **9.4 AI辅助测试**

1. **单元测试生成**
   * 使用AI生成单元测试用例
   * 示例提示模板:
     ```
     为以下Python函数创建pytest单元测试:
     {function_code}
     包含正常情况、边界条件和异常情况的测试用例。
     ```

2. **模拟数据生成**
   * 使用AI生成测试数据
   * 示例提示模板:
     ```
     生成10条模拟的超限报告关键数据点，包括项目名称、高度、结构类型等，
     数据应具有真实性和多样性。以JSON格式输出。
     ```

#### **10. 测试与CI/CD方案**

##### **10.1 测试策略**

1. **单元测试**
   * 框架: pytest
   * 范围: 模型、业务逻辑、工具函数
   * 覆盖率目标: 80%
   * 运行方式: 自动化测试脚本，CI/CD集成

2. **集成测试**
   * 框架: pytest + requests
   * 范围: API接口、数据库交互、Celery任务
   * 策略: 使用测试数据库、模拟服务
   * 关键测试点: 数据流、状态转换、边界条件

3. **端到端测试**
   * 框架: Selenium/Playwright
   * 范围: 用户界面、完整工作流
   * 测试场景:
     * 项目创建和管理
     * 模板上传和解析
     * 数据源上传和抽取
     * 报告生成和下载

4. **性能测试**
   * 工具: Locust
   * 场景: API负载测试、并发任务处理
   * 关注指标: 响应时间、吞吐量、错误率

##### **10.2 测试自动化**

1. **测试执行**
   * 每次代码提交自动运行单元测试
   * 每日自动运行集成测试
   * 版本发布前运行端到端测试

2. **测试报告**
   * 使用pytest-html生成HTML报告
   * 集成到CI/CD平台显示测试结果
   * 发送邮件通知测试失败

##### **10.3 CI/CD流程**

使用GitHub Actions实现自动化CI/CD流程:

1. **持续集成 (CI)**
   * 触发条件: 代码推送到主分支或PR
   * 流程步骤:
     * 代码检出
     * 安装依赖
     * 运行代码质量检查 (flake8, mypy)
     * 运行单元测试
     * 生成测试覆盖率报告

2. **持续部署 (CD)**
   * 触发条件: 主分支合并或手动触发
   * 流程步骤:
     * 构建Docker镜像
     * 推送镜像到容器仓库
     * 部署到测试环境
     * 运行集成测试
     * 部署到生产环境 (手动批准)

3. **GitHub Actions工作流示例**

```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: test_db
        ports:
          - 5432:5432
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5

    steps:
    - uses: actions/checkout@v3
    - name: Set up Python 3.12
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 pytest pytest-cov
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
    - name: Lint with flake8
      run: |
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
    - name: Test with pytest
      run: |
        pytest --cov=app --cov-report=xml
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
    - uses: actions/checkout@v3
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    - name: Login to DockerHub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}
    - name: Build and push
      uses: docker/build-push-action@v4
      with:
        context: .
        push: true
        tags: username/report-assistant:latest

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
    - name: Deploy to production
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.DEPLOY_HOST }}
        username: ${{ secrets.DEPLOY_USER }}
        key: ${{ secrets.DEPLOY_KEY }}
        script: |
          cd /path/to/app
          docker-compose pull
          docker-compose up -d
```

#### **11. 运维与安全措施**

##### **11.1 系统监控**

1. **基础设施监控**
   * 工具: Prometheus + Grafana
   * 监控指标:
     * CPU、内存、磁盘使用率
     * 网络流量和延迟
     * 容器健康状态
   * 告警机制: 阈值触发、邮件/短信通知

2. **应用性能监控**
   * 工具: Prometheus + Grafana + Flask Exporter
   * 监控指标:
     * API响应时间
     * 请求成功/失败率
     * 并发用户数
     * 数据库查询性能
   * 可视化: 实时仪表盘、趋势图表

3. **日志管理**
   * 工具: ELK Stack (Elasticsearch, Logstash, Kibana)
   * 功能:
     * 集中式日志收集
     * 实时日志搜索和分析
     * 异常检测和告警
     * 日志保留策略: 7天实时索引，90天归档

##### **11.2 安全措施**

1. **身份认证与授权**
   * 基于Flask-AppBuilder的角色权限控制
   * JWT令牌认证
   * 密码策略: 强度要求、定期更换
   * 会话管理: 超时控制、单点登录

2. **数据安全**
   * 敏感数据加密存储
   * 传输加密: HTTPS/TLS
   * 数据脱敏: 日志和错误消息中的敏感信息
   * 文件访问控制: 基于用户权限的文件隔离

3. **API安全**
   * 请求速率限制: Redis实现
   * API密钥认证
   * 输入验证和消毒
   * CORS设置: 限制来源域

4. **容器安全**
   * 镜像安全扫描
   * 最小权限原则: 非root用户运行
   * 容器隔离: 网络分段
   * 定期更新基础镜像和依赖

5. **安全审计**
   * 用户操作日志
   * 系统变更记录
   * 安全事件监控
   * 定期安全评估

##### **11.3 备份与恢复**

1. **数据库备份**
   * 策略: 每日全量备份，每小时增量备份
   * 备份存储: 本地存储 + 远程对象存储
   * 自动化: 定时任务，备份验证
   * 保留策略: 7天全量，30天增量

2. **文件系统备份**
   * 策略: 每日增量备份
   * 范围: 用户上传文件、生成报告
   * 存储: 本地备份 + 云存储同步

3. **恢复计划**
   * 灾难恢复程序文档
   * 恢复时间目标 (RTO): 4小时
   * 恢复点目标 (RPO): 1小时
   * 定期恢复演练

#### **12. 项目计划与里程碑**

##### **12.1 开发阶段划分**

基于AI辅助开发的特点，项目开发划分为以下阶段：

1. **第一阶段: 基础框架搭建 (3周)**
   * 目标: 建立项目基础架构和核心功能框架
   * 任务:
     * 环境搭建与配置
     * 数据库设计与初始化
     * 用户认证系统实现
     * 项目管理模块开发
     * 基础UI框架搭建
   * AI辅助重点: 数据库模型、API设计、基础UI生成

2. **第二阶段: 文档处理能力开发 (4周)**
   * 目标: 实现文档上传、解析和数据抽取的核心功能
   * 任务:
     * 文档上传与管理功能
     * Word/PDF解析集成
     * LLM API集成
     * 模板解析与占位符识别
     * 关键数据抽取实现
   * AI辅助重点: 文档处理代码、LLM提示设计、异步任务处理

3. **第三阶段: 交互界面与用户体验 (3周)**
   * 目标: 完成用户交互界面和编辑功能
   * 任务:
     * 抽取结果可视化编辑器
     * 模板管理界面
     * 数据源管理界面
     * 报告预览功能
     * 结果导出功能
   * AI辅助重点: 前端交互组件、数据可视化、用户体验优化

4. **第四阶段: 报告生成与导出 (2周)**
   * 目标: 实现报告自动生成与导出功能
   * 任务:
     * 报告生成逻辑实现
     * Word模板填充功能
     * 表格和图片处理
     * 报告下载与版本管理
   * AI辅助重点: 文档生成代码、排版处理、错误处理

5. **第五阶段: 测试、优化与部署 (3周)**
   * 目标: 完成系统测试、性能优化和部署准备
   * 任务:
     * 单元测试与集成测试
     * 性能优化
     * 安全加固
     * 部署脚本准备
     * 用户文档编写
   * AI辅助重点: 测试用例生成、性能分析、部署脚本

##### **12.2 里程碑计划**

| **里程碑**                | **计划日期**   | **交付物**                                                   |
|--------------------------|---------------|-------------------------------------------------------------|
| **项目启动**              | 2024-06-01    | 项目计划、需求文档、技术方案                                  |
| **基础框架完成**          | 2024-06-21    | 数据库结构、基础API、用户认证、项目管理功能                    |
| **文档处理能力实现**      | 2024-07-19    | 文档解析、LLM集成、数据抽取功能                               |
| **用户界面完成**          | 2024-08-09    | 完整交互界面、编辑功能、用户体验优化                          |
| **报告生成功能完成**      | 2024-08-23    | 报告生成、导出功能、质量检查                                  |
| **MVP版本发布**           | 2024-09-13    | 完整系统、部署文档、用户手册                                  |

##### **12.3 风险与缓解措施**

| **风险**                         | **影响**     | **可能性** | **缓解措施**                                               |
|---------------------------------|-------------|------------|-----------------------------------------------------------|
| LLM API响应不稳定                | 高          | 中         | 实现重试机制，本地缓存，降级方案                            |
| 文档结构复杂导致解析困难          | 中          | 高         | 分阶段解析，人工干预机制，支持多种文档格式                   |
| 性能瓶颈影响用户体验              | 中          | 中         | 异步处理，分页加载，优化数据库查询                           |
| 数据安全与隐私问题                | 高          | 低         | 权限控制，数据加密，安全审计                                |
| AI生成内容质量不稳定              | 中          | 中         | 人工审核机制，多轮优化，专业术语库                           |

##### **12.4 资源需求**

1. **开发环境**
   * 开发服务器: 8GB RAM, 4 Core CPU, 100GB SSD
   * 开发工具: VSCode, Cursor, PyCharm
   * 版本控制: GitHub

2. **测试环境**
   * 测试服务器: 16GB RAM, 4 Core CPU, 200GB SSD
   * 测试工具: Pytest, Locust, Selenium

3. **生产环境**
   * 生产服务器: 16GB RAM, 4 Core CPU, 60GB SSD
   * 外部依赖: Qwen32B LLM API服务器
   * 网络: 100Mbps带宽，低延迟

4. **开发团队**
   * 单人开发团队
   * AI辅助工具: Cursor, Windsurf
   * 外部支持: LLM技术咨询
