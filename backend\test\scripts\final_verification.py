#!/usr/bin/env python3
"""
最终验证脚本 - 确认数据库连接问题已完全解决
"""

import os
import requests
import json

# 设置环境变量
os.environ['DATABASE_URL'] = 'postgresql://report_user:report_pass@localhost:5432/report_gen'

def test_database_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    
    try:
        from models.core import engine
        from sqlalchemy import text
        
        with engine.connect() as conn:
            result = conn.execute(text('SELECT version()'))
            version = result.fetchone()[0]
            print(f"✅ 数据库连接成功: PostgreSQL {version[:30]}...")
            
            # 测试中文支持
            result = conn.execute(text("SELECT '测试中文数据库连接' as test"))
            chinese_test = result.fetchone()[0]
            print(f"✅ 中文支持正常: {chinese_test}")
            
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False


def test_model_consistency():
    """测试模型与expect_procedures.md的一致性"""
    print("\n🔍 测试模型一致性...")
    
    try:
        from models.template import TemplateField
        from sqlalchemy import text
        from models.core import engine
        
        # 检查TemplateField模型的字段
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'template_fields'
                ORDER BY ordinal_position
            """))
            db_fields = [row[0] for row in result.fetchall()]
        
        # expect_procedures.md要求的字段
        required_fields = ['description', 'example', 'format', 'default', 'length', 'source', 'must']
        
        print("📋 字段一致性检查:")
        all_present = True
        for field in required_fields:
            if field in db_fields:
                print(f"  ✅ {field}: 存在")
            else:
                print(f"  ❌ {field}: 缺失")
                all_present = False
        
        if all_present:
            print("✅ 模型完全符合expect_procedures.md要求")
        else:
            print("❌ 模型不符合expect_procedures.md要求")
            
        return all_present
        
    except Exception as e:
        print(f"❌ 模型一致性检查失败: {e}")
        return False


def test_api_endpoints():
    """测试API接口"""
    print("\n🔍 测试API接口...")
    
    base_url = "http://localhost:8000"
    endpoints = [
        "/health",
        "/api/templates/",
        "/api/data/sources",
        "/api/reports/"
    ]
    
    all_success = True
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"  ✅ {endpoint}: 状态码 {response.status_code}")
            else:
                print(f"  ❌ {endpoint}: 状态码 {response.status_code}")
                all_success = False
        except Exception as e:
            print(f"  ❌ {endpoint}: 请求失败 - {e}")
            all_success = False
    
    return all_success


def test_crud_operations():
    """测试基础CRUD操作"""
    print("\n🔍 测试CRUD操作...")
    
    try:
        from models.core import SessionLocal
        from models.template import Template, TemplateField
        import uuid
        
        db = SessionLocal()
        
        # 创建测试模板
        test_template = Template(
            name="测试模板",
            description="这是一个测试模板",
            category="测试分类",
            file_path="/test/template.docx",
            placeholder_count=2
        )
        
        db.add(test_template)
        db.commit()
        db.refresh(test_template)
        print("✅ 模板创建成功")
        
        # 创建测试字段
        test_field = TemplateField(
            template_id=test_template.id,
            field_name="测试字段",
            description="这是一个测试字段描述",
            example="测试样例",
            format="普通文本",
            default="默认值",
            length=100,
            source="上传内容",
            must=True
        )
        
        db.add(test_field)
        db.commit()
        print("✅ 字段创建成功")
        
        # 查询测试
        template = db.query(Template).filter(Template.name == "测试模板").first()
        if template:
            print(f"✅ 模板查询成功: {template.name}")
            
            # 查询关联字段
            fields = db.query(TemplateField).filter(TemplateField.template_id == template.id).all()
            if fields:
                field = fields[0]
                print(f"✅ 字段查询成功: {field.field_name} (must={field.must})")
        
        # 清理测试数据
        db.delete(test_field)
        db.delete(test_template)
        db.commit()
        print("✅ 测试数据清理完成")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ CRUD操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主验证函数"""
    print("🚀 开始最终验证测试\n")
    
    tests = [
        ("数据库连接", test_database_connection),
        ("模型一致性", test_model_consistency),
        ("API接口", test_api_endpoints),
        ("CRUD操作", test_crud_operations)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"{'='*50}")
        result = test_func()
        results.append((test_name, result))
    
    print(f"\n{'='*50}")
    print("📊 最终验证结果:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    print(f"\n{'='*50}")
    if all_passed:
        print("🎉 所有测试通过！数据库连接问题已完全解决！")
        print("✅ 项目已准备好进行下一步开发")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    
    return all_passed


if __name__ == "__main__":
    main()
