# 报告智能生成工具

> 基于AI技术的文档处理解决方案，支持自动信息抽取和智能报告生成

## 🎯 项目概述

报告智能生成工具是一个现代化的文档处理系统，利用通义千问大语言模型实现智能信息抽取和报告自动生成。系统提供完整的Web界面，支持从文档上传到报告生成的端到端自动化工作流。

### ✨ 核心功能

- **📄 文档上传与解析** - 支持docx/txt格式，自动解析文档内容
- **🤖 智能信息抽取** - 基于AI模型的字段信息自动抽取
- **📊 报告自动生成** - 结构化报告自动生成和格式化
- **🎨 现代化Web界面** - 基于React的用户友好界面
- **⚡ 端到端工作流** - 一键完成从上传到报告的全流程

## 🏗️ 技术架构

### 后端技术栈
- **FastAPI** - 现代Python Web框架
- **PostgreSQL** - 关系型数据库
- **通义千问** - qwq-32b-preview大语言模型
- **SQLAlchemy** - Python ORM框架
- **uv** - 现代Python包管理器

### 前端技术栈
- **React 19** - 现代前端框架
- **TypeScript** - 类型安全的JavaScript
- **Ant Design 5** - 企业级UI组件库
- **Zustand** - 轻量级状态管理
- **Vite** - 快速构建工具

## 🚀 快速开始

### 环境要求
- Python 3.11+
- Node.js 18+
- PostgreSQL 14+
- uv包管理器

### 1. 克隆项目
```bash
git clone <repository-url>
cd report-gen
```

### 2. 后端设置
```bash
cd backend

# 安装依赖
uv sync

# 配置环境变量
# 编辑 .env 文件，设置数据库连接和API密钥
cp .env.example .env

# 启动后端服务
uv run python app.py
```

### 3. 前端设置
```bash
cd frontend

# 安装依赖
npm install

# 启动前端服务
npm run dev
```

### 4. 访问应用
- **前端界面**: http://localhost:5173
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health

## 📁 项目结构

```
report-gen/
├── README.md                   # 项目说明
├── backend/                    # 后端代码
│   ├── app.py                  # FastAPI应用入口
│   ├── config.py               # 配置管理
│   ├── database.py             # 数据库连接
│   ├── DEVELOPMENT.md          # 开发文档
│   ├── api/                    # API路由
│   ├── agents/                 # 智能体模块
│   ├── models/                 # 数据模型
│   ├── services/               # 服务层
│   ├── test/                   # 测试文件
│   │   ├── README.md           # 测试说明
│   │   ├── run_tests.py        # 测试运行脚本
│   │   ├── documents/          # 测试文档
│   │   └── scripts/            # 测试脚本
│   └── uploads/                # 上传文件目录
└── frontend/                   # 前端代码
    ├── src/
    │   ├── App.tsx             # 主应用组件
    │   ├── components/         # React组件
    │   ├── services/           # API服务
    │   └── stores/             # 状态管理
    ├── package.json            # 前端依赖
    └── vite.config.ts          # Vite配置
```

## 🧪 测试

### 运行测试套件
```bash
cd backend

# 运行所有测试
uv run python test/run_tests.py

# 运行特定测试
uv run python test/scripts/test_workflow.py
```

### 测试覆盖
- ✅ 数据库连接测试
- ✅ LLM服务测试
- ✅ 文档解析测试
- ✅ 文件上传测试
- ✅ 端到端工作流测试

## 📖 使用指南

### 1. 上传文档
- 支持拖拽上传或点击选择
- 支持.docx和.txt格式
- 文件大小限制50MB

### 2. 配置字段
- 定义需要抽取的字段信息
- 提供字段描述和示例
- 支持预设模板快速配置

### 3. 查看结果
- 实时查看处理进度
- 查看抽取结果和置信度
- 下载生成的报告

## 🔧 配置说明

### 环境变量配置
```bash
# .env文件
APP_NAME=报告智能生成工具
DATABASE_URL=postgresql://username:password@localhost:5432/report_gen
DASHSCOPE_API_KEY=your_api_key_here
LLM_MODEL=qwq-32b-preview
UPLOAD_DIR=./uploads
CORS_ORIGINS=http://localhost:3000,http://localhost:5173
```

## 📝 API文档

### 核心端点
- `POST /api/upload/document` - 上传文档
- `POST /api/workflow/process-document` - 端到端处理
- `GET /api/workflow/demo-fields` - 获取演示字段
- `GET /health` - 健康检查

完整API文档请访问: http://localhost:8000/docs

## 📚 文档

- [开发文档](backend/DEVELOPMENT.md) - 详细的开发指南
- [测试文档](backend/test/README.md) - 测试说明和指南
- [API文档](http://localhost:8000/docs) - 在线API文档

---

**项目状态**: MVP完成，核心功能可用
**最后更新**: 2024年7月4日

## 主要功能

- 文档智能解析
- 数据自动抽取
- 报告模板管理
- 智能报告生成
- 质量自动检查

## 技术栈

- 后端：Python + Flask AppBuilder
- 前端：Bootstrap 4 + jQuery
- 数据库：PostgreSQL
- AI服务：LLM API + OCR

## 开发环境要求

- Python 3.12+
- Docker & Docker Compose
- PostgreSQL 14+
- Node.js 18+

## 快速开始

1. 克隆仓库
```bash
git clone https://gitee.com/guilin8410/report-gen.git
cd report-gen
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 启动服务
```bash
docker-compose up -d
python app.py
```

## 项目结构

```
report-gen/
├── app/                # 应用主目录
│   ├── models/        # 数据模型
│   ├── views/         # 视图
│   └── api/           # API接口
├── config/            # 配置文件
├── docs/             # 文档
├── tests/            # 测试
└── docker/           # Docker配置
```

## 开发规范

- 遵循PEP 8编码规范
- 使用类型注解
- 编写单元测试
- 提交前进行代码格式化

## 版本历史

- v0.1.0 (开发中) - 基础框架搭建

## 许可证

MIT License 