# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an **intelligent report generation system** that leverages AI for document processing and automated report generation. The system consists of a FastAPI backend with multi-agent architecture using LangGraph, and a React frontend with modern UI components.

**Core functionality**: Upload documents → Extract structured data using AI agents → Generate formatted reports

## Architecture

### Backend Structure
- **Multi-agent system** using LangGraph for complex document processing workflows
- **Agent types**: Data extraction, template identification, template parsing, report generation
- **Tools system**: Modular tools for document examination, key extraction, search, etc.
- **LLM Integration**: Uses <PERSON>wen models via DashScope API for AI processing
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Background tasks**: Celery with Redis for async processing

### Frontend Structure  
- **React 19** with TypeScript and Vite
- **Ant Design 5** for UI components
- **Zustand** for state management
- **React Query** for server state management
- **Layout system**: Sidebar navigation with main workspace area

### Key Directories
- `backend/agents/` - LangGraph agents and workflow definitions
- `backend/tools/` - Modular tool implementations  
- `backend/models/` - SQLAlchemy database models
- `backend/routers/` - FastAPI route handlers
- `backend/services/` - Business logic services
- `frontend/src/components/` - React components organized by feature
- `frontend/src/stores/` - Zustand state stores

## Development Commands

### Backend Development
```bash
cd backend
# Install dependencies
uv sync

# Run development server
uv run python app.py
# Alternative: uv run uvicorn app:app --reload

# Run tests
uv run python test/run_tests.py
# Or specific test script: uv run python test/scripts/test_workflow.py

# Code formatting and linting
uv run black .
uv run isort .
uv run flake8 .
uv run mypy .
```

### Frontend Development  
```bash
cd frontend
# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build

# Lint code
npm run lint
```

### Docker Development
```bash
# Start all services (PostgreSQL, Redis, backend, frontend, Celery)
docker-compose up -d

# View logs
docker-compose logs -f backend
docker-compose logs -f frontend

# Stop services
docker-compose down
```

## Configuration

### Environment Setup
1. Copy `.env.example` to `.env` 
2. Configure required API keys:
   - `DASHSCOPE_API_KEY` - For Qwen AI models
   - `DATABASE_URL` - PostgreSQL connection
   - `REDIS_URL` - Redis connection

### Database Setup
- PostgreSQL database with migrations in `scripts/`
- Models defined in `backend/models/`
- Database initialization via `scripts/init-db.sql`

## Testing Strategy

### Backend Tests
- **Integration tests**: Full workflow testing in `test/scripts/test_workflow.py`
- **Service tests**: Individual service testing (LLM, document, upload)
- **Agent tests**: LangGraph agent and tool testing
- **API tests**: FastAPI endpoint testing

### Test Execution
- Run all tests: `uv run python test/run_tests.py`
- Test files follow pattern `test_*.py`
- Async test support enabled via pytest-asyncio

## API Endpoints

### Core Endpoints
- `POST /api/upload/document` - Document upload
- `POST /api/workflow/process-document` - End-to-end document processing
- `GET /api/workflow/demo-fields` - Get demo field configuration
- `GET /health` - Health check

### Development URLs
- Frontend: http://localhost:3000 (Docker) or http://localhost:5173 (npm)
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs
- pgAdmin: http://localhost:5050 (Docker only)

## LangGraph Workflow

The system uses a sophisticated multi-agent workflow:
1. **Intention identification** - Determine processing intent
2. **Data extraction** - Extract structured information
3. **Template identification** - Find appropriate template
4. **Template parsing** - Parse template structure  
5. **Report generation** - Generate final formatted report

Agent state and workflow defined in `backend/agents/langgraph_state.py` and `backend/agents/report_generation_graph.py`.

## Code Patterns

### Backend Patterns
- **Dependency injection** via FastAPI Depends()
- **Pydantic models** for request/response validation (in `schemas/`)
- **SQLAlchemy models** with proper relationships (in `models/`)
- **Service layer** pattern for business logic
- **Tool-based architecture** for agent capabilities

### Frontend Patterns
- **Feature-based** component organization
- **Zustand stores** for global state
- **Ant Design** components with consistent styling
- **TypeScript** strict mode enabled
- **API service layer** in `services/api.ts`

## Important Notes

- The system is designed for Chinese document processing (会议纪要, etc.)
- Uses Qwen models specifically optimized for Chinese language
- File uploads support `.docx` and `.txt` formats
- Maximum file size: 50MB (configurable)
- Async processing for long-running document analysis tasks
- Comprehensive error handling and logging throughout the system