
### **轨道交通智能报告助手 - 产品开发实施方案**

**版本信息：** V1.0 (内部开发版)
**日期：** 2024年5月20日
**开发团队：** [桂林]
**目标受众：** 产品用户、AI代码生成工具 (Cursor, Windsurf) 及开发者本人

---

#### **1. 项目概述与开发目标**

* **项目名称：** 轨道交通智能报告助手
* **项目愿景：** 成为轨道交通设计师不可或缺的智能报告编写伙伴，通过AI技术显著提升报告编制效率和质量。
* **MVP目标 (2024年7月31日)：** 实现"典型设计报告"的智能生成核心流程。具体包括：
  * 支持Word/PDF文档上传、文本粘贴作为数据源。
  * AI智能抽取报告结构、关键数据点、表格内容。
  * 提供可视化界面，支持人工对抽取结果进行结构调整、数据修改和图片关联。
  * 基于Word模板，自动填充内容并保持排版。
  * 基础质量检查：错别字检查、规范引用版本过期提示。
  * 通过Docker Compose在Ubuntu服务器上稳定部署。
* **技术栈约束：** Python 3.12, Flask AppBuilder, PostgreSQL, Docker Compose, Qwen32B (RESTful API), PaddleOCR, MinerU等。
* **开发模式：** 单人团队，AI辅助编程（Cursor, Windsurf）。

#### **2. 系统架构**

本系统采用微服务（或模块化）架构思想，通过Docker容器化部署，实现各组件的解耦和独立运行。

##### **2.1 整体架构图**

```mermaid
graph TD
    subgraph User Interaction
        A[Web Frontend] --> B[Flask AppBuilder UI/API Gateway]
    end

    subgraph Backend Services
        B -- HTTP/REST --> C[Flask AppBuilder Application <Core Logic>]
        C -- Async Task Queue --> D[Celery Worker<s>]
    end

    subgraph AI & Processing Layer
        C -- HTTP/REST --> E[Qwen32B LLM Service RESTful API]
        D -- Local Call --> F[Document Processing & MCP Tools]
    end

    subgraph Data & Storage
        C -- SQLAlchemy ORM --> G[PostgreSQL Database]
        F -- File I/O --> H[Local File Storage-Mounted Volume]
    end

    subgraph External
        I[User Uploaded Documents] --> H
        J[Generated Reports] --> H
        K[Docker Compose] -- Orchestrates --> B & C & D & G & H
    end

    E -- Model Inference --> L[Pre-deployed Qwen32B Model]

    classDef default fill:#DDEBF7,stroke:#333,stroke-width:2px;
    classDef llm fill:#E0F7FA,stroke:#00BCD4;
    classDef db fill:#F0F4C3,stroke:#C0CA33;
    classDef storage fill:#F3E5F5,stroke:#9C27B0;
    class E llm;
    class G db;
    class H storage;
```

##### **2.2 模块职责与技术选型**

* **Web Frontend (Web UI):**
  * **职责：** 提供用户友好的交互界面，展示报告生成进度、编辑抽取结果、预览报告、报告修改等。
  * **技术：** Flask AppBuilder 内置的UI组件和基本的前端框架（Jinja2模板、HTML/CSS/JS）。遵循"左侧功能列表，右侧功能交互/输出"的布局。
* **Flask AppBuilder Application (Core Logic):**
  * **职责：** 后端核心业务逻辑，包括用户管理、项目管理、文档上传/管理、抽取结果存储与管理、报告生成调度、API路由。
  * **技术：** Python 3.12, Flask AppBuilder, SQLAlchemy ORM。
* **Celery Worker(s):**
  * **职责：** 处理耗时长的异步任务，如文档解析、AI抽取、报告生成、质量检查。避免阻塞主应用进程，提升用户体验，处理过程中需要向用户展示进度。
  * **技术：** Python 3.12, Celery (Broker: Redis, Backend: Redis)。
* **Qwen32B LLM Service:**
  * **职责：** 提供大模型推理服务，实现文档语义理解、关键信息抽取、文本生成等核心AI功能。
  * **技术：** Qwen32B (通过 RESTful API 调用)。**目前Qwen32B已独立部署在其他高配服务器，本系统仅作为客户端调用。**
* **Document Processing :**
  * **职责：** 负责文档的物理解析（文本、图片、表格提取）、OCR识别等。
  * **技术：** Python 3.12。
    * **Word (.docx) 解析/操作:** `python-docx`
    * **PDF (.pdf) 解析:** `MinerU` (用于文本、图片、布局提取，支持更多高级功能，如坐标定位) 。**这是一个重点攻克和优化点。**
    * **OCR:** `PaddleOCR` (对中文支持优异，可私有化部署)。
    * **表格生成:** 表格处理通过大模型原生能力完成，通过大模型对文本数据进行处理，按照要求生成json数据后，转换为表格对象，以便插入到word模板中合适位置。
* **PostgreSQL Database:**
  * **职责：** 存储所有结构化数据，包括项目元数据、用户数据、抽取结果（JSONB）、模板数据、任务状态等。
  * **技术：** PostgreSQL 独立部署。
* **Local File Storage:**
  * **职责：** 存储用户上传的原始文档、图片以及最终生成的报告文件。
  * **技术：** 服务器本地文件系统，通过Docker Volume进行持久化。

#### **3. 功能结构**

##### **3.1 核心功能模块**

```mermaid
graph TD
    A[项目管理] --> A1[创建项目]
    A --> A2[选择项目]
    A --> A3[删除项目]
    A --> A4[项目模板管理]（模板增、删、改、查、下载）

    B[数据识别] --> B1[选择项目]
    B --> B2[选择模板]
    B --> B3[数据管理]（数据增、删、改、查、下载）

    C[报告成果] --> C1[选择项目]
    C --> C2[选择模板]
    C --> C3[报告管理]（报告增、删、改、查、下载）

    D[智能交互主界面] --> D0[状态切换]
    D1 --> D1a[检索状态]
    D1 --> D1b[模板分析]
    D1 --> D1c[信息提取]
    D1 --> D1d[报告生成]
    D --> D1[检索状态]
    D1 --> D1a[报告下载]
    D1 --> D1b[项目查询]
    D1 --> D1c[模板查询]
    D1 --> D1d[数据查询]
    D --> D2[模板分析]
    D2 --> D2a[智能分析报告模板]
    D2 --> D2b[模板交互式编辑]
    D2 --> D2c[模板保存]
    D --> D3[信息抽取]
    D3 --> D3a[智能信息分析]
    D3 --> D3b[抽取结果交互式编辑]
    D3 --> D3c[信息保存]
    D3 --> D3d[表格信息处理]
    D3 --> D3e[图片信息处理]
    D --> D4[报告生成]
    D4 --> D4a[信息完整度检查]
    D4 --> D4b[基于模板的信息智能填充]
    D4 --> D4c[智能排版]
    D4 --> D4d[报告保存]

    E[用户管理] --> E1[用户认证-登录/登出]
    E --> E2[权限管理-Flask AppBuilder]
```

##### **3.2 MVP阶段功能范围**

* **项目管理：** 创建、查看、编辑、删除项目。
* **数据源管理：** Word/PDF上传、文本粘贴、图片上传（用户手动标识）。
* **AI智能抽取：**
  * **DSR：** 识别《超限报告》的章节、标题层级（例如：1.工程概况、1.1工程概况、1.1.1效果图）。
  * **KIE：** 抽取报告中常见的关键数据点（如项目名称、建筑高度、设防烈度、建筑面积、结构类型、主要超限项、计算软件、材料强度等）。
  * **表格数据抽取：** 根据设计计算软件结果，根据既定逻辑生成表格。
  * **图片OCR：** 仅限识别图片中的文字（例如图号和图注）。
* **人工干预与编辑：**
  * **可视化结构编辑器：** 支持拖拽、增删改章节标题。XML导出/导入。
  * **关键数据点编辑器：** 列表/表单编辑，支持XML导出/导入。
  * **图片位置关联器：** 用户手动将上传图片与模板占位符关联。
* **报告模板管理：** 上传Word模板，系统识别 `{{占位符}}`。
* **报告智能生成：** 基于用户选择的Word模板，填充人工确认后的结构化数据和AI生成内容。继承Word模板样式，自动插入图片。
* **用户管理：** 基础的用户认证。

#### **4. 用户流程图与界面交互**

延续《产品设计说明书》中的用户旅程图，并融入"左侧功能列表，右侧功能交互/输出"的界面布局。

* **平台登录：** 
界面布局描述：用户名和密码登陆框
用户动作：利用账户和密码登录平台
特殊说明：用户相关数据都具有用户标签
数据说明：基于flask appbuilder权限管控体系实现

* **系统主界面：** 
界面布局描述：
左侧约1/10，从顶到底部为竖条状界面菜单栏，分别标注主界面、项目管理、模板管理、数据管理、结果管理、用户管理
右侧约9/10，为主要工作区，上部分3/5区域为结果交互区，用于交互式编辑，中间1/5区域为状态显示区，通过下拉列表分别选择项目，模板，数据，右侧显示当前大模型交互状态，状态包括：检索状态、模板分析、信息抽取、报告生成四个状态，下方1/5区为功能沟通区，此处用户输入不同的命令，完成大部分该产品功能的实现，上传需要处理的文件
用户动作：
1.点击左侧界面菜单，可以分别切换界面
2.结果交互区，系统反馈当前状态，处理结果，响应用户问答，且可以在该区域进行相关结果的编辑，编辑对象包括：文档模板识别结果，文档数据识别结果，文档生成结果
3.状态显示区，通过下拉列表可以选择项目、模板、数据，切换状态，已显示当前情况；
4.功能沟通区，分别在四中不同的状态中，与系统进行通讯，可以实现的功能如下：
4.1 检索状态
前置条件：
通过自然语言，检索系统中目前已有的项目信息，模板信息，数据信息，报告信息，查询后在结果交互区进行展示，所有结果不可编辑
通过自然语言，切换到其他状态，如果满足前置条件，则可以进行切换
4.2 模板分析
前置条件：具有被选择的项目，上传了docx或者doc文档   或者   选择了具体模板
通过自然语言，启动分析功能对上传的文档结构和数据构成进行分析，形成结果展示在结果交互区，编辑完成后，通过自然语言让系统进行保存
如果没有上传文档数据，但选择了具体模板的，则将模板导入结果交互区进行编辑，编辑完成后可选择是否保存，或者另存为
4.3 信息抽取
前置条件：选择了具体模板，上传了docx、doc、jpg、txt等可能存在信息的数据文件
通过自然语言，启动数据抽取功能，对上传了的数据进行相关的处理，按照模板要求，形成生成报告所需的各类数据，抽取完成后，形成结果展示在结果交互区进行编辑，完成后可选择是否保存，或者另存为
4.4 报告生成
前置条件：选择了具体模板，选择了具体数据
通过自然语言，启动报告生成功能，将数据填充到具体的模板中，形成新的报告，模板文件以docx文档存储，以减少工作量，生成报告后，将报告展示在结果交互区，可以自动进行排版，生成用户需要的结果文件，生成报告的过程中，除了常规的文字数据外，还有表格和图片数据，表格，图片可以根据名称自动排版到模板合适位置

* **项目管理：** 
界面布局描述：
左侧界面菜单栏中，项目管理高亮，右侧按照列表显示项目，可对项目进行增删改查，项目具有项目名称、创建人、创建时间、项目描述、关联模板数、关联数据数量、最后生成报告时间信息
用户动作：
1.点击表头对项目进行排序；
2.点击编辑可打开项目详情对话框，对项目进行编辑，包括项目名称、项目描述
3.点击具体项目相关模板数量，可跳转到模板管理，自动选择本项目
4.点击具体项目相关数据数量，可跳转到数据管理，自动选择本项目

* **模板管理：** 
界面布局描述：
左侧界面菜单栏中，模板管理高亮，右侧按照列表显示所有模板，可对模板进行增删改查和下载。模板列表包含模板名称、所属项目、创建人、创建时间、模板描述、占位符数量、被引用次数等信息。
用户动作：
1. 点击表头可对模板进行排序；
2. 点击新增模板按钮，弹出上传Word模板对话框，填写模板名称、描述并上传文件；
3. 点击模板名称可查看模板详情，包括占位符预览、模板结构树、被引用项目列表等；
4. 点击编辑按钮可修改模板名称、描述，或重新上传模板文件；
5. 点击删除按钮可删除模板（如被项目引用需二次确认）；
6. 点击下载按钮可下载模板原文件；
7. 点击占位符数量可弹出占位符详情列表，支持复制占位符名。

* **数据管理：** 
界面布局描述：
左侧界面菜单栏中，数据管理高亮，右侧以列表形式展示所有数据源，支持增删改查和下载。数据列表包含数据名称、所属项目、关联模板、上传人、上传时间、来源数据（Word/PDF/图片/文本）、抽取结果预览等信息。
用户动作：
1. 点击数据名称可查看数据详情，包括原文件预览、抽取结果、抽取日志等；
2. 点击编辑按钮可修改数据名称、描述，或重新上传数据文件；
3. 点击删除按钮可删除数据（如已被报告引用需二次确认）；
4. 点击抽取结果预览可展开查看结构化数据、表格、图片等内容；
5. 支持批量选择数据进行删除或下载操作。

* **报告管理：** 
界面布局描述：
左侧界面菜单栏中，结果管理（报告管理）高亮，右侧以列表形式展示所有已生成报告，支持增删改查和下载。报告列表包含报告名称、所属项目、关联模板、关联数据、生成时间、生成状态、报告版本、报告预览等信息。
用户动作：
1. 点击生成报告按钮，返回主界面，选择项目、模板、数据，填写报告名称、描述，启动报告生成流程；
2. 点击报告名称可查看报告详情，包括报告预览、生成日志、填充数据明细等；
3. 点击下载按钮可下载报告Word文件；
4. 点击删除按钮可删除报告（需二次确认）；
5. 点击报告预览可在线浏览报告内容，支持分页、跳转、放大缩小等操作；

* **用户管理：** 
界面布局描述：
左侧界面菜单栏中，用户管理高亮，右侧以列表形式展示所有用户信息，支持用户的增删改查和权限分配。用户列表包含用户名、姓名、角色、所属部门、创建时间、最后登录时间、状态（启用/禁用）等信息。
用户动作：
1. 点击新增用户按钮，弹出新建用户对话框，填写用户名、姓名、密码、角色、部门等信息；
2. 点击用户名可查看用户详情，包括基本信息、历史操作记录、所属项目等；
3. 点击编辑按钮可修改用户信息、重置密码、调整角色权限；
4. 点击删除按钮可删除用户（需二次确认）；
5. 点击状态切换按钮可启用/禁用用户账号；
6. 支持批量选择用户进行删除或状态切换操作。

#### **5. 模块设计与API接口**

##### **5.1 后端API接口 (Flask AppBuilder)**

所有API接口都应遵循RESTful原则，使用JSON进行数据交换，并强制使用Python类型提示。

* **项目管理 (Project Management)**


* **数据源管理 (Document/Data Source Management)**

* **抽取结果与人工干预 (Extracted Data & Manual Intervention)**



* **模板管理 (Template Management)**


* **报告生成 (Report Generation)**



##### **5.2 后端-AI服务接口 (Qwen32B RESTful API)**

这些是后端应用或Celery Worker调用的Qwen32B的API。


#### **6. 数据库设计 (PostgreSQL)**

所有敏感文本字段考虑使用PostgreSQL的文本搜索功能（FTS）。

*

#### **7. 关键数据点定义**

结合两份《超限报告》样本，定义核心关键数据点，并考虑其存储类型和用途。这将直接指导AI抽取模型和报告填充逻辑。

| **数据点名称 (英文键名)**                               | **定义**                                      | **示例值 (来自报告)**                                                  | **存储类型 (在 `key_data_points` JSONB中)** | **利用方式**       |
| :------------------------------------------------------------ | :-------------------------------------------------- | :--------------------------------------------------------------------------- | :-------------------------------------------------- | :----------------------- |
| `projectName` (项目名称)                                    | 工程的正式名称，用于报告标题和正文引用。            | 白云(棠溪)站综合交通枢纽一体化工程西地块场站综合体项目A2 楼栋（北塔）        | string                                              | 报告标题、封面、正文填充 |
| `constructionUnit` (建设单位)                               | 负责项目建设的单位全称。                            | 广州市云胜房地产开发有限公司 / 广州地铁集团有限公司                          | string                                              | 报告封面、正文填充       |
| `designUnit` (设计单位)                                     | 负责项目设计工作的单位全称。                        | 广州地铁设计院股份有限公司                                                   | string                                              | 报告封面、正文填充       |
| `designer` (设计人/计算人)                                  | 报告的设计人或计算人姓名。                          | 刘齐霞                                                                       | string                                              | 报告落款                 |
| `checker` (校核人)                                          | 报告的校核人姓名。                                  | 李颖平                                                                       | string                                              | 报告落款                 |
| `projectManager` (专业负责人)                               | 报告的专业负责人姓名。                              | 李红波                                                                       | string                                              | 报告落款                 |
| `reviewer` (审核人)                                         | 报告的审核人姓名。                                  | 李红波                                                                       | string                                              | 报告落款                 |
| `approver` (审定人)                                         | 报告的审定人姓名。                                  | 伍永胜                                                                       | string                                              | 报告落款                 |
| `reportDate` (报告日期)                                     | 报告编制的日期。                                    | 2022 年 10 月                                                                | string (或date)                                     | 报告封面、日期更新       |
| `reportVersion` (版本)                                      | 报告的版本信息。                                    | V0.9 / 2016 年版                                                             | string                                              | 报告封面                 |
| `totalBuildingArea` (总建筑面积)                            | 地上总建筑面积，单位m²。                           | 69047m²                                                                     | float                                               | 工程概况描述             |
| `mainStructureHeight` (主结构高度)                          | 主体结构从地下室顶板或室外地面算起的总高度，单位m。 | 145.3m                                                                       | float                                               | 工程概况描述、超限判别   |
| `mainStructureStories` (主结构层数)                         | 主体结构地上总层数。                                | 31                                                                           | int                                                 | 工程概况描述、超限判别   |
| `undergroundStories` (地下层数)                             | 地下室层数。                                        | 4                                                                            | int                                                 | 工程概况描述             |
| `seismicFortificationIntensity` (抗震设防烈度)              | 建筑抗震设防烈度，单位度。                          | 7 度( 0.1g)                                                                  | string                                              | 设计条件、超限判别       |
| `siteClass` (场地类别)                                      | 建筑场地的地震分类。                                | II 类 / III 类                                                               | string                                              | 设计条件、超限判别       |
| `characteristicPeriod` (特征周期)                           | 场地特征周期，单位s。                               | 0.35s / 0.45s                                                                | float                                               | 设计条件                 |
| `structureType` (结构类型)                                  | 建筑物主要结构体系类型。                            | 框架-核心筒结构 / 钢筋混凝土剪力墙结构                                       | string                                              | 工程概况、超限判别       |
| `primaryOverlimitItems` (主要超限项)                        | 报告中识别出的主要超限类型列表。                    | 超 A 级高度建筑, 扭转不规则, 楼板不连续, 局部不规则                          | array of string                                     | 超限情况总结             |
| `calculationSoftware` (计算软件)                            | 报告中使用的结构计算软件列表。                      | YJK, MIDAS Building, SAUSAGE                                                 | array of string                                     | 技术对策                 |
| `materialStrength.beamSlab` (材料强度-梁板)                 | 梁板混凝土强度等级。                                | C30 / C35                                                                    | string                                              | 设计条件、计算参数       |
| `materialStrength.coreWallColumn` (材料强度-核心筒剪力墙柱) | 核心筒剪力墙和柱混凝土强度等级范围。                | C60~C35 / C60                                                                | string                                              | 设计条件、计算参数       |
| `materialStrength.frameColumn` (材料强度-外框柱)            | 外框柱混凝土强度等级范围。                          | C80~C50 / C60                                                                | string                                              | 设计条件、计算参数       |
| `materialStrength.beamSection` (材料强度-梁截面)            | 报告中列举的梁截面尺寸示例。                        | 200x400、200x500、...                                                        | string (可进一步解析为列表)                         | 计算参数                 |
| `liveLoad.office` (活载-办公)                               | 办公区域的活荷载取值，单位kN/m²。                  | 2.2                                                                          | float                                               | 荷载取值                 |
| `referenceNorm.GB50011-2010` (规范引用-抗震规范)            | 抗震设计规范的完整编号及版本。                      | GB50011-2010（2016年版）                                                     | string                                              | 设计依据、质量检查       |
| `table_data.楼面屋面荷载取值表` (表格数据-楼面屋面荷载)     | 报告中特定表格的结构化数据。                        | （见下方表格数据结构）                                                       | array of objects                                    | 报告中表格填充           |
| `image_ref.总平面图` (图片引用-总平面图)                    | 报告中引用图片的编号及图注。                        | 图1.1-1 白云(棠溪)站综合交通枢纽一体化工程建设项目西地块场站综合体项目总平面 | object {file_id: string, caption: string}           | 报告图片插入             |

#### **8. 部署方案 (Docker Compose)**

部署环境：Ubuntu Server 22.04 LTS, 16GB RAM, 4 Cores, 60GB Storage。
使用Docker Compose进行容器编排，确保所有服务独立运行且易于管理。


**`requirements.txt` (示例):**




#### **9. AI辅助开发指导**

