"""
人机交互API接口
提供字段验证和工作流协调的后端服务
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Dict, List, Any, Optional
from pydantic import BaseModel
from datetime import datetime
import uuid
import asyncio

from services.llm_service import llm_service, ExtractionRequest
from services.template_validation_service import template_validation_service
from utils.template_manager import template_manager

router = APIRouter(prefix="/api/human-interaction", tags=["人机交互"])


# 请求模型
class CreateSessionRequest(BaseModel):
    """创建验证会话请求"""
    document_path: str
    template_name: str
    user_preferences: Optional[Dict[str, Any]] = None


class ValidateFieldRequest(BaseModel):
    """字段验证请求"""
    session_id: str
    field_name: str
    validation_status: str  # validated, rejected, modified, skipped
    user_confirmed_value: Optional[str] = None
    rejection_reason: Optional[str] = None


class CompleteSessionRequest(BaseModel):
    """完成会话请求"""
    session_id: str
    validated_data: Dict[str, str]


# 响应模型
class ExtractedFieldResponse(BaseModel):
    """抽取字段响应"""
    field_name: str
    field_description: str
    field_example: str
    extracted_value: str
    confidence: float
    extraction_method: str
    original_context: str
    is_required: bool
    validation_status: str
    user_confirmed_value: Optional[str] = None
    user_rejection_reason: Optional[str] = None
    suggested_improvements: List[str] = []


class ValidationSessionResponse(BaseModel):
    """验证会话响应"""
    session_id: str
    document_name: str
    total_fields: int
    validated_fields: int
    rejected_fields: int
    pending_fields: int
    overall_progress: float
    current_stage: str
    extracted_fields: List[ExtractedFieldResponse]
    validation_start_time: datetime
    estimated_completion_time: Optional[datetime] = None


class WorkflowStageResponse(BaseModel):
    """工作流阶段响应"""
    stage: str
    title: str
    description: str
    status: str
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    progress: Optional[float] = None
    error_message: Optional[str] = None
    metrics: Optional[Dict[str, Any]] = None


class WorkflowProgressResponse(BaseModel):
    """工作流进度响应"""
    workflow_id: str
    document_name: str
    current_stage: str
    overall_progress: float
    total_duration: float
    estimated_remaining: float
    stages: List[WorkflowStageResponse]
    is_paused: bool = False
    pause_reason: Optional[str] = None


class HumanInteractionSessionResponse(BaseModel):
    """人机交互会话响应"""
    session_id: str
    workflow_id: str
    document_name: str
    template_name: str
    status: str
    validation_session: Optional[ValidationSessionResponse] = None
    workflow_progress: WorkflowProgressResponse
    created_at: datetime
    updated_at: datetime
    user_preferences: Optional[Dict[str, Any]] = None
    statistics: Optional[Dict[str, Any]] = None


class CreateSessionResponse(BaseModel):
    """创建会话响应"""
    session_id: str
    workflow_id: str
    status: str
    validation_session: ValidationSessionResponse
    workflow_progress: WorkflowProgressResponse


class ValidateFieldResponse(BaseModel):
    """验证字段响应"""
    success: bool
    updated_session: HumanInteractionSessionResponse
    next_field: Optional[str] = None
    session_complete: bool = False


class CompleteSessionResponse(BaseModel):
    """完成会话响应"""
    success: bool
    generated_report: str
    report_id: str
    final_statistics: Dict[str, Any]


# 内存会话存储（生产环境应使用数据库）
active_sessions: Dict[str, Dict[str, Any]] = {}


def create_workflow_stages(document_name: str) -> List[WorkflowStageResponse]:
    """创建工作流阶段"""
    stages = [
        {
            "stage": "document_upload",
            "title": "文档上传",
            "description": "上传并解析文档内容",
            "status": "completed",
            "start_time": datetime.now(),
            "end_time": datetime.now(),
            "duration": 2.0,
            "progress": 100.0
        },
        {
            "stage": "document_analysis",
            "title": "文档分析",
            "description": "分析文档结构和内容类型",
            "status": "completed",
            "start_time": datetime.now(),
            "end_time": datetime.now(),
            "duration": 3.5,
            "progress": 100.0
        },
        {
            "stage": "field_extraction",
            "title": "字段抽取",
            "description": "使用AI模型抽取关键字段信息",
            "status": "completed",
            "start_time": datetime.now(),
            "end_time": datetime.now(),
            "duration": 8.2,
            "progress": 100.0
        },
        {
            "stage": "human_validation",
            "title": "人工验证",
            "description": "人工确认和修正抽取结果",
            "status": "in_progress",
            "start_time": datetime.now(),
            "progress": 0.0
        },
        {
            "stage": "report_generation",
            "title": "报告生成",
            "description": "根据验证结果生成最终报告",
            "status": "pending"
        },
        {
            "stage": "completed",
            "title": "完成",
            "description": "处理流程完成",
            "status": "pending"
        }
    ]
    
    return [WorkflowStageResponse(**stage) for stage in stages]


async def extract_fields_from_document(document_path: str, template_name: str) -> List[ExtractedFieldResponse]:
    """从文档中抽取字段"""
    try:
        # 读取文档内容
        with open(document_path, 'r', encoding='utf-8') as f:
            document_content = f.read()
        
        # 生成抽取请求
        extraction_requests = await template_manager.generate_extraction_requests(
            template_name, document_content
        )
        
        # 执行字段抽取
        extraction_results = await llm_service.extract_multiple_fields(extraction_requests)
        
        # 转换为响应格式
        extracted_fields = []
        for result, request in zip(extraction_results, extraction_requests):
            field = ExtractedFieldResponse(
                field_name=result.field_name,
                field_description=request.field_description,
                field_example=request.field_example,
                extracted_value=result.field_value,
                confidence=result.confidence,
                extraction_method=result.extraction_method,
                original_context=request.source_text[:500] + "..." if len(request.source_text) > 500 else request.source_text,
                is_required=request.must,
                validation_status="pending"
            )
            extracted_fields.append(field)
        
        return extracted_fields
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"字段抽取失败: {str(e)}")


@router.post("/sessions", response_model=CreateSessionResponse)
async def create_validation_session(request: CreateSessionRequest, background_tasks: BackgroundTasks):
    """
    创建人机交互验证会话
    """
    try:
        # 生成会话ID
        session_id = str(uuid.uuid4())
        workflow_id = str(uuid.uuid4())
        
        # 验证模板是否存在
        templates = template_manager.list_templates()
        template_exists = any(t["template_name"] == request.template_name for t in templates)
        if not template_exists:
            raise HTTPException(status_code=404, detail=f"模板 '{request.template_name}' 不存在")
        
        # 验证文档是否存在
        import os
        if not os.path.exists(request.document_path):
            raise HTTPException(status_code=404, detail=f"文档文件不存在: {request.document_path}")
        
        # 创建工作流阶段
        workflow_stages = create_workflow_stages(request.document_path.split('/')[-1])
        
        # 在后台执行字段抽取
        extracted_fields = await extract_fields_from_document(
            request.document_path, request.template_name
        )
        
        # 创建验证会话
        validation_session = ValidationSessionResponse(
            session_id=session_id,
            document_name=request.document_path.split('/')[-1],
            total_fields=len(extracted_fields),
            validated_fields=0,
            rejected_fields=0,
            pending_fields=len(extracted_fields),
            overall_progress=0.0,
            current_stage="field_validation",
            extracted_fields=extracted_fields,
            validation_start_time=datetime.now()
        )
        
        # 创建工作流进度
        workflow_progress = WorkflowProgressResponse(
            workflow_id=workflow_id,
            document_name=request.document_path.split('/')[-1],
            current_stage="human_validation",
            overall_progress=75.0,  # 前面的阶段已完成
            total_duration=15.0,  # 假设已用时间
            estimated_remaining=300.0,  # 预计剩余5分钟
            stages=workflow_stages
        )
        
        # 创建完整会话
        session_data = {
            "session_id": session_id,
            "workflow_id": workflow_id,
            "document_name": request.document_path.split('/')[-1],
            "template_name": request.template_name,
            "status": "waiting_for_validation",
            "validation_session": validation_session,
            "workflow_progress": workflow_progress,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "user_preferences": request.user_preferences or {}
        }
        
        # 存储会话
        active_sessions[session_id] = session_data
        
        return CreateSessionResponse(
            session_id=session_id,
            workflow_id=workflow_id,
            status="waiting_for_validation",
            validation_session=validation_session,
            workflow_progress=workflow_progress
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建验证会话失败: {str(e)}")


@router.get("/sessions/{session_id}", response_model=HumanInteractionSessionResponse)
async def get_validation_session(session_id: str):
    """
    获取验证会话信息
    """
    if session_id not in active_sessions:
        raise HTTPException(status_code=404, detail="验证会话不存在")
    
    session_data = active_sessions[session_id]
    return HumanInteractionSessionResponse(**session_data)


@router.post("/validate-field", response_model=ValidateFieldResponse)
async def validate_extracted_field(request: ValidateFieldRequest):
    """
    验证抽取的字段
    """
    if request.session_id not in active_sessions:
        raise HTTPException(status_code=404, detail="验证会话不存在")
    
    try:
        session_data = active_sessions[request.session_id]
        validation_session = session_data["validation_session"]
        
        # 查找要验证的字段
        field_found = False
        for field in validation_session.extracted_fields:
            if field.field_name == request.field_name:
                # 更新字段状态
                field.validation_status = request.validation_status
                if request.user_confirmed_value:
                    field.user_confirmed_value = request.user_confirmed_value
                if request.rejection_reason:
                    field.user_rejection_reason = request.rejection_reason
                field_found = True
                break
        
        if not field_found:
            raise HTTPException(status_code=404, detail=f"字段 '{request.field_name}' 不存在")
        
        # 更新统计
        validation_session.validated_fields = len([
            f for f in validation_session.extracted_fields 
            if f.validation_status in ["validated", "modified"]
        ])
        validation_session.rejected_fields = len([
            f for f in validation_session.extracted_fields 
            if f.validation_status == "rejected"
        ])
        validation_session.pending_fields = len([
            f for f in validation_session.extracted_fields 
            if f.validation_status == "pending"
        ])
        validation_session.overall_progress = (
            (validation_session.validated_fields + validation_session.rejected_fields) / 
            validation_session.total_fields * 100
        )
        
        # 更新工作流进度
        workflow_progress = session_data["workflow_progress"]
        human_validation_stage = next(
            (stage for stage in workflow_progress.stages if stage.stage == "human_validation"), 
            None
        )
        if human_validation_stage:
            human_validation_stage.progress = validation_session.overall_progress
        
        # 更新整体进度
        workflow_progress.overall_progress = 75.0 + (validation_session.overall_progress * 0.2)
        
        # 检查是否所有字段都已处理
        session_complete = validation_session.pending_fields == 0
        if session_complete:
            session_data["status"] = "validation_complete"
            # 更新人工验证阶段为完成
            if human_validation_stage:
                human_validation_stage.status = "completed"
                human_validation_stage.end_time = datetime.now()
                human_validation_stage.progress = 100.0
        
        # 更新会话时间戳
        session_data["updated_at"] = datetime.now()
        
        # 查找下一个待验证字段
        next_field = None
        for field in validation_session.extracted_fields:
            if field.validation_status == "pending":
                next_field = field.field_name
                break
        
        return ValidateFieldResponse(
            success=True,
            updated_session=HumanInteractionSessionResponse(**session_data),
            next_field=next_field,
            session_complete=session_complete
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"字段验证失败: {str(e)}")


@router.post("/complete-session", response_model=CompleteSessionResponse)
async def complete_validation_session(request: CompleteSessionRequest):
    """
    完成验证会话，生成报告
    """
    if request.session_id not in active_sessions:
        raise HTTPException(status_code=404, detail="验证会话不存在")
    
    try:
        session_data = active_sessions[request.session_id]
        validation_session = session_data["validation_session"]
        
        # 生成报告内容
        report_lines = ["# 智能报告生成结果\n"]
        report_lines.append(f"**文档名称**: {session_data['document_name']}")
        report_lines.append(f"**模板**: {session_data['template_name']}")
        report_lines.append(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("\n## 抽取结果\n")
        
        for field_name, value in request.validated_data.items():
            report_lines.append(f"**{field_name}**: {value}")
        
        report_lines.append("\n## 验证统计\n")
        report_lines.append(f"- 总字段数: {validation_session.total_fields}")
        report_lines.append(f"- 成功验证: {validation_session.validated_fields}")
        report_lines.append(f"- 已拒绝: {validation_session.rejected_fields}")
        report_lines.append(f"- 验证率: {validation_session.overall_progress:.1f}%")
        
        report_lines.append("\n---\n*本报告由AI智能生成，已通过人工验证确认*")
        
        generated_report = "\n".join(report_lines)
        report_id = str(uuid.uuid4())
        
        # 计算最终统计
        validation_duration = (datetime.now() - validation_session.validation_start_time).total_seconds()
        final_statistics = {
            "total_validation_time": validation_duration,
            "fields_validated_per_minute": (validation_session.validated_fields / max(validation_duration / 60, 1)),
            "accuracy_rate": validation_session.validated_fields / validation_session.total_fields
        }
        
        # 更新会话状态
        session_data["status"] = "completed"
        session_data["updated_at"] = datetime.now()
        session_data["statistics"] = final_statistics
        
        # 更新工作流为完成状态
        workflow_progress = session_data["workflow_progress"]
        workflow_progress.overall_progress = 100.0
        workflow_progress.current_stage = "completed"
        
        # 更新报告生成阶段
        report_stage = next(
            (stage for stage in workflow_progress.stages if stage.stage == "report_generation"), 
            None
        )
        if report_stage:
            report_stage.status = "completed"
            report_stage.start_time = datetime.now()
            report_stage.end_time = datetime.now()
            report_stage.duration = 2.0
            report_stage.progress = 100.0
        
        # 更新完成阶段
        completed_stage = next(
            (stage for stage in workflow_progress.stages if stage.stage == "completed"), 
            None
        )
        if completed_stage:
            completed_stage.status = "completed"
            completed_stage.start_time = datetime.now()
            completed_stage.end_time = datetime.now()
            completed_stage.progress = 100.0
        
        return CompleteSessionResponse(
            success=True,
            generated_report=generated_report,
            report_id=report_id,
            final_statistics=final_statistics
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"完成验证会话失败: {str(e)}")


@router.post("/sessions/{session_id}/pause")
async def pause_validation_session(session_id: str):
    """
    暂停验证会话
    """
    if session_id not in active_sessions:
        raise HTTPException(status_code=404, detail="验证会话不存在")
    
    session_data = active_sessions[session_id]
    session_data["status"] = "paused"
    session_data["workflow_progress"]["is_paused"] = True
    session_data["workflow_progress"]["pause_reason"] = "用户手动暂停"
    session_data["updated_at"] = datetime.now()
    
    return {"success": True, "message": "验证会话已暂停"}


@router.post("/sessions/{session_id}/resume")
async def resume_validation_session(session_id: str):
    """
    恢复验证会话
    """
    if session_id not in active_sessions:
        raise HTTPException(status_code=404, detail="验证会话不存在")
    
    session_data = active_sessions[session_id]
    session_data["status"] = "validating"
    session_data["workflow_progress"]["is_paused"] = False
    session_data["workflow_progress"]["pause_reason"] = None
    session_data["updated_at"] = datetime.now()
    
    return {"success": True, "message": "验证会话已恢复"}


@router.delete("/sessions/{session_id}")
async def cancel_validation_session(session_id: str):
    """
    取消验证会话
    """
    if session_id not in active_sessions:
        raise HTTPException(status_code=404, detail="验证会话不存在")
    
    # 更新会话状态为取消
    session_data = active_sessions[session_id]
    session_data["status"] = "cancelled"
    session_data["updated_at"] = datetime.now()
    
    # 可以选择是否从内存中删除
    # del active_sessions[session_id]
    
    return {"success": True, "message": "验证会话已取消"}


@router.get("/sessions")
async def list_validation_sessions():
    """
    列出所有验证会话
    """
    sessions = []
    for session_id, session_data in active_sessions.items():
        sessions.append({
            "session_id": session_id,
            "document_name": session_data["document_name"],
            "template_name": session_data["template_name"],
            "status": session_data["status"],
            "created_at": session_data["created_at"],
            "progress": session_data.get("validation_session", {}).get("overall_progress", 0)
        })
    
    return {"sessions": sessions}


@router.get("/statistics")
async def get_interaction_statistics():
    """
    获取人机交互统计信息
    """
    total_sessions = len(active_sessions)
    completed_sessions = len([s for s in active_sessions.values() if s["status"] == "completed"])
    active_sessions_count = len([s for s in active_sessions.values() if s["status"] in ["validating", "waiting_for_validation"]])
    
    return {
        "total_sessions": total_sessions,
        "completed_sessions": completed_sessions,
        "active_sessions": active_sessions_count,
        "completion_rate": completed_sessions / max(total_sessions, 1) * 100
    }