"""
模板验证API接口
提供模板与关键字段定义的验证服务
"""

from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
import tempfile
import os
from pathlib import Path

from services.template_validation_service import (
    template_validation_service, 
    TemplateValidationResult,
    ValidationIssue,
    ValidationLevel
)

router = APIRouter(prefix="/api/template-validation", tags=["模板验证"])


class ValidationRequest(BaseModel):
    """验证请求模型"""
    template_path: str
    key_definitions_path: str


class ValidationIssueResponse(BaseModel):
    """验证问题响应模型"""
    level: str
    type: str
    message: str
    location: Optional[str] = None
    suggestion: Optional[str] = None
    field_name: Optional[str] = None


class PlaceholderInfoResponse(BaseModel):
    """占位符信息响应模型"""
    placeholder_text: str
    field_name: str
    pattern_type: str
    location: str
    context: str
    line_number: Optional[int] = None


class FieldDefinitionResponse(BaseModel):
    """字段定义响应模型"""
    field_name: str
    description: str
    example: str
    format: str
    default: str
    length: Optional[int]
    source: str
    must: bool


class ValidationResultResponse(BaseModel):
    """验证结果响应模型"""
    is_valid: bool
    issues: List[ValidationIssueResponse]
    placeholders: List[PlaceholderInfoResponse]
    field_definitions: List[FieldDefinitionResponse]
    
    total_placeholders: int
    matched_placeholders: int
    unmatched_placeholders: int
    missing_definitions: int
    unused_definitions: int
    
    consistency_score: float
    completeness_score: float
    overall_score: float
    
    suggestions: List[str]
    summary: Dict[str, Any]


@router.post("/validate-files", response_model=ValidationResultResponse)
async def validate_template_files(request: ValidationRequest):
    """
    验证指定路径的模板文件和关键字段定义文件
    """
    try:
        # 验证文件是否存在
        if not os.path.exists(request.template_path):
            raise HTTPException(status_code=404, detail=f"模板文件不存在: {request.template_path}")
        
        if not os.path.exists(request.key_definitions_path):
            raise HTTPException(status_code=404, detail=f"关键字段定义文件不存在: {request.key_definitions_path}")
        
        # 执行验证
        validation_result = await template_validation_service.validate_template_consistency(
            request.template_path, request.key_definitions_path
        )
        
        # 获取改进建议和摘要
        suggestions = await template_validation_service.suggest_template_improvements(validation_result)
        summary = template_validation_service.get_validation_summary(validation_result)
        
        # 转换为响应格式
        return _convert_validation_result_to_response(validation_result, suggestions, summary)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"模板验证失败: {str(e)}")


@router.post("/validate-upload", response_model=ValidationResultResponse)
async def validate_uploaded_files(
    template_file: UploadFile = File(..., description="模板文件"),
    key_definitions_file: UploadFile = File(..., description="关键字段定义文件")
):
    """
    验证上传的模板文件和关键字段定义文件
    """
    template_temp_path = None
    key_definitions_temp_path = None
    
    try:
        # 验证文件格式
        template_ext = Path(template_file.filename).suffix.lower()
        if template_ext not in ['.docx', '.txt', '.md']:
            raise HTTPException(status_code=400, detail="不支持的模板文件格式，请上传 .docx, .txt 或 .md 文件")
        
        key_ext = Path(key_definitions_file.filename).suffix.lower()
        if key_ext not in ['.md', '.txt']:
            raise HTTPException(status_code=400, detail="不支持的关键字段定义文件格式，请上传 .md 或 .txt 文件")
        
        # 保存临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=template_ext) as temp_template:
            template_temp_path = temp_template.name
            content = await template_file.read()
            temp_template.write(content)
        
        with tempfile.NamedTemporaryFile(delete=False, suffix=key_ext) as temp_key_def:
            key_definitions_temp_path = temp_key_def.name
            content = await key_definitions_file.read()
            temp_key_def.write(content)
        
        # 执行验证
        validation_result = await template_validation_service.validate_template_consistency(
            template_temp_path, key_definitions_temp_path
        )
        
        # 获取改进建议和摘要
        suggestions = await template_validation_service.suggest_template_improvements(validation_result)
        summary = template_validation_service.get_validation_summary(validation_result)
        
        # 转换为响应格式
        return _convert_validation_result_to_response(validation_result, suggestions, summary)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件验证失败: {str(e)}")
    
    finally:
        # 清理临时文件
        if template_temp_path and os.path.exists(template_temp_path):
            os.unlink(template_temp_path)
        if key_definitions_temp_path and os.path.exists(key_definitions_temp_path):
            os.unlink(key_definitions_temp_path)


@router.post("/extract-placeholders")
async def extract_template_placeholders(
    template_file: UploadFile = File(..., description="模板文件")
):
    """
    从模板文件中提取占位符信息
    """
    temp_path = None
    
    try:
        # 验证文件格式
        file_ext = Path(template_file.filename).suffix.lower()
        if file_ext not in ['.docx', '.txt', '.md']:
            raise HTTPException(status_code=400, detail="不支持的文件格式")
        
        # 保存临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_file:
            temp_path = temp_file.name
            content = await template_file.read()
            temp_file.write(content)
        
        # 提取占位符
        placeholders = await template_validation_service._extract_template_placeholders(temp_path)
        
        # 统计占位符信息
        pattern_stats = {}
        for placeholder in placeholders:
            pattern_type = placeholder.pattern_type
            pattern_stats[pattern_type] = pattern_stats.get(pattern_type, 0) + 1
        
        # 转换为响应格式
        placeholder_responses = [
            PlaceholderInfoResponse(
                placeholder_text=p.placeholder_text,
                field_name=p.field_name,
                pattern_type=p.pattern_type,
                location=p.location,
                context=p.context,
                line_number=p.line_number
            ) for p in placeholders
        ]
        
        return {
            "success": True,
            "data": {
                "placeholders": placeholder_responses,
                "statistics": {
                    "total_placeholders": len(placeholders),
                    "unique_fields": len(set(p.field_name for p in placeholders)),
                    "pattern_distribution": pattern_stats
                }
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"占位符提取失败: {str(e)}")
    
    finally:
        if temp_path and os.path.exists(temp_path):
            os.unlink(temp_path)


@router.post("/parse-key-definitions")
async def parse_key_definitions_file(
    key_definitions_file: UploadFile = File(..., description="关键字段定义文件")
):
    """
    解析关键字段定义文件
    """
    temp_path = None
    
    try:
        # 验证文件格式
        file_ext = Path(key_definitions_file.filename).suffix.lower()
        if file_ext not in ['.md', '.txt']:
            raise HTTPException(status_code=400, detail="不支持的文件格式")
        
        # 保存临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_file:
            temp_path = temp_file.name
            content = await key_definitions_file.read()
            temp_file.write(content)
        
        # 解析字段定义
        field_definitions = await template_validation_service._parse_key_definitions(temp_path)
        
        # 统计字段信息
        format_stats = {}
        source_stats = {}
        required_count = 0
        
        for field_def in field_definitions:
            # 格式统计
            format_type = field_def.format
            format_stats[format_type] = format_stats.get(format_type, 0) + 1
            
            # 来源统计
            source_type = field_def.source
            source_stats[source_type] = source_stats.get(source_type, 0) + 1
            
            # 必填字段统计
            if field_def.must:
                required_count += 1
        
        # 转换为响应格式
        field_responses = [
            FieldDefinitionResponse(
                field_name=fd.field_name,
                description=fd.description,
                example=fd.example,
                format=fd.format,
                default=fd.default,
                length=fd.length,
                source=fd.source,
                must=fd.must
            ) for fd in field_definitions
        ]
        
        return {
            "success": True,
            "data": {
                "field_definitions": field_responses,
                "statistics": {
                    "total_fields": len(field_definitions),
                    "required_fields": required_count,
                    "optional_fields": len(field_definitions) - required_count,
                    "format_distribution": format_stats,
                    "source_distribution": source_stats
                }
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"字段定义解析失败: {str(e)}")
    
    finally:
        if temp_path and os.path.exists(temp_path):
            os.unlink(temp_path)


@router.post("/generate-template-skeleton")
async def generate_template_skeleton(
    key_definitions_file: UploadFile = File(..., description="关键字段定义文件"),
    placeholder_format: str = Form(default="double_brace", description="占位符格式")
):
    """
    根据关键字段定义生成模板骨架
    """
    temp_path = None
    
    try:
        # 验证占位符格式
        format_patterns = {
            "double_brace": "{{{}}}",
            "single_brace": "{{}}",
            "square_bracket": "[{}]",
            "angle_bracket": "<{}>"
        }
        
        if placeholder_format not in format_patterns:
            raise HTTPException(status_code=400, detail="不支持的占位符格式")
        
        pattern = format_patterns[placeholder_format]
        
        # 保存临时文件
        file_ext = Path(key_definitions_file.filename).suffix.lower()
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_file:
            temp_path = temp_file.name
            content = await key_definitions_file.read()
            temp_file.write(content)
        
        # 解析字段定义
        field_definitions = await template_validation_service._parse_key_definitions(temp_path)
        
        # 生成模板骨架
        template_skeleton = _generate_template_skeleton_content(field_definitions, pattern)
        
        return {
            "success": True,
            "data": {
                "template_skeleton": template_skeleton,
                "placeholder_format": placeholder_format,
                "total_fields": len(field_definitions),
                "required_fields": len([fd for fd in field_definitions if fd.must])
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"模板骨架生成失败: {str(e)}")
    
    finally:
        if temp_path and os.path.exists(temp_path):
            os.unlink(temp_path)


@router.get("/validation-rules")
async def get_validation_rules():
    """
    获取模板验证规则说明
    """
    return {
        "success": True,
        "data": {
            "placeholder_formats": {
                "double_brace": {
                    "pattern": "{{field_name}}",
                    "description": "双花括号格式（推荐）",
                    "example": "{{meeting_title}}"
                },
                "single_brace": {
                    "pattern": "{field_name}",
                    "description": "单花括号格式",
                    "example": "{meeting_title}"
                },
                "square_bracket": {
                    "pattern": "[field_name]",
                    "description": "方括号格式",
                    "example": "[meeting_title]"
                },
                "angle_bracket": {
                    "pattern": "<field_name>",
                    "description": "尖括号格式",
                    "example": "<meeting_title>"
                }
            },
            "validation_levels": {
                "error": "错误级别，必须修复才能通过验证",
                "warning": "警告级别，建议修复以提高质量",
                "info": "信息级别，可选修复"
            },
            "field_definition_attributes": {
                "field_name": "字段名称（必须）",
                "description": "字段描述，用于生成提取提示词",
                "example": "字段示例，帮助理解字段内容",
                "format": "字段格式类型",
                "default": "默认值",
                "length": "字段长度限制",
                "source": "信息来源",
                "must": "是否为必填字段"
            },
            "supported_formats": [
                "普通文本", "表格行", "段落", "人名", "单独行", "行单元格", "日期时间", "百分比"
            ],
            "validation_scores": {
                "consistency_score": "占位符与字段定义的匹配程度",
                "completeness_score": "字段定义的使用完整性",
                "overall_score": "综合质量评分"
            }
        }
    }


def _convert_validation_result_to_response(validation_result: TemplateValidationResult,
                                         suggestions: List[str],
                                         summary: Dict[str, Any]) -> ValidationResultResponse:
    """转换验证结果为响应格式"""
    
    # 转换问题列表
    issues = [
        ValidationIssueResponse(
            level=issue.level.value,
            type=issue.type,
            message=issue.message,
            location=issue.location,
            suggestion=issue.suggestion,
            field_name=issue.field_name
        ) for issue in validation_result.issues
    ]
    
    # 转换占位符列表
    placeholders = [
        PlaceholderInfoResponse(
            placeholder_text=p.placeholder_text,
            field_name=p.field_name,
            pattern_type=p.pattern_type,
            location=p.location,
            context=p.context,
            line_number=p.line_number
        ) for p in validation_result.placeholders
    ]
    
    # 转换字段定义列表
    field_definitions = [
        FieldDefinitionResponse(
            field_name=fd.field_name,
            description=fd.description,
            example=fd.example,
            format=fd.format,
            default=fd.default,
            length=fd.length,
            source=fd.source,
            must=fd.must
        ) for fd in validation_result.field_definitions
    ]
    
    return ValidationResultResponse(
        is_valid=validation_result.is_valid,
        issues=issues,
        placeholders=placeholders,
        field_definitions=field_definitions,
        total_placeholders=validation_result.total_placeholders,
        matched_placeholders=validation_result.matched_placeholders,
        unmatched_placeholders=validation_result.unmatched_placeholders,
        missing_definitions=validation_result.missing_definitions,
        unused_definitions=validation_result.unused_definitions,
        consistency_score=validation_result.consistency_score,
        completeness_score=validation_result.completeness_score,
        overall_score=validation_result.overall_score,
        suggestions=suggestions,
        summary=summary
    )


def _generate_template_skeleton_content(field_definitions: List, pattern: str) -> str:
    """生成模板骨架内容"""
    skeleton_parts = []
    
    # 添加标题
    skeleton_parts.append("# 报告模板\n")
    
    # 按格式分组字段
    format_groups = {}
    for field_def in field_definitions:
        format_type = field_def.format
        if format_type not in format_groups:
            format_groups[format_type] = []
        format_groups[format_type].append(field_def)
    
    # 生成基本信息部分
    basic_fields = format_groups.get("普通文本", []) + format_groups.get("人名", []) + format_groups.get("日期时间", [])
    if basic_fields:
        skeleton_parts.append("\n## 基本信息\n")
        for field_def in basic_fields[:6]:  # 最多显示6个基本字段
            placeholder = pattern.format(field_def.field_name)
            required_mark = "（必填）" if field_def.must else ""
            skeleton_parts.append(f"**{field_def.description}{required_mark}**: {placeholder}\n")
    
    # 生成表格部分
    table_fields = format_groups.get("表格行", []) + format_groups.get("行单元格", [])
    if table_fields:
        skeleton_parts.append("\n## 详细信息\n")
        skeleton_parts.append("| 项目 | 内容 |\n")
        skeleton_parts.append("|------|------|\n")
        for field_def in table_fields:
            placeholder = pattern.format(field_def.field_name)
            skeleton_parts.append(f"| {field_def.description} | {placeholder} |\n")
    
    # 生成段落部分
    paragraph_fields = format_groups.get("段落", [])
    if paragraph_fields:
        skeleton_parts.append("\n## 详细描述\n")
        for field_def in paragraph_fields:
            placeholder = pattern.format(field_def.field_name)
            skeleton_parts.append(f"\n### {field_def.description}\n\n{placeholder}\n")
    
    # 生成其他字段
    other_fields = []
    for format_type, fields in format_groups.items():
        if format_type not in ["普通文本", "人名", "日期时间", "表格行", "行单元格", "段落"]:
            other_fields.extend(fields)
    
    if other_fields:
        skeleton_parts.append("\n## 其他信息\n")
        for field_def in other_fields:
            placeholder = pattern.format(field_def.field_name)
            skeleton_parts.append(f"**{field_def.description}**: {placeholder}\n")
    
    return "".join(skeleton_parts)