/**
 * 对话历史管理页面
 * 提供完整的对话历史管理功能
 */

import React, { useState } from 'react';
import { Card, Row, Col, Typography, Button, Space, message } from 'antd';
import { ArrowLeftOutlined, PlusOutlined } from '@ant-design/icons';
import DialogueHistoryList from '../DialogueHistory/DialogueHistoryList';

const { Title, Text } = Typography;

interface DialogueHistoryPageProps {
  onBack?: () => void;
  onStartNewConversation?: (conversationId?: string) => void;
}

const DialogueHistoryPage: React.FC<DialogueHistoryPageProps> = ({
  onBack,
  onStartNewConversation
}) => {
  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null);

  const handleConversationSelect = (conversationId: string) => {
    setSelectedConversationId(conversationId);
    message.info(`已选择对话: ${conversationId}`);
  };

  const handleNewConversation = () => {
    if (onStartNewConversation) {
      onStartNewConversation();
    } else {
      message.info('创建新对话功能需要在主页面中实现');
    }
  };

  const handleContinueConversation = () => {
    if (selectedConversationId && onStartNewConversation) {
      onStartNewConversation(selectedConversationId);
    } else {
      message.warning('请先选择一个对话');
    }
  };

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column', padding: '16px' }}>
      {/* 页面头部 */}
      <Card size="small" style={{ marginBottom: '16px', flex: '0 0 auto' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            {onBack && (
              <Button
                type="text"
                icon={<ArrowLeftOutlined />}
                onClick={onBack}
              >
                返回
              </Button>
            )}
            <Title level={3} style={{ margin: 0 }}>
              对话历史管理
            </Title>
          </Space>
          
          <Space>
            {selectedConversationId && (
              <Button
                type="primary"
                onClick={handleContinueConversation}
              >
                继续此对话
              </Button>
            )}
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleNewConversation}
            >
              开始新对话
            </Button>
          </Space>
        </div>
      </Card>

      {/* 主要内容区域 */}
      <div style={{ flex: 1, display: 'flex' }}>
        <Row gutter={16} style={{ width: '100%', height: '100%' }}>
          {/* 对话列表 */}
          <Col span={24} style={{ height: '100%' }}>
            <DialogueHistoryList
              userId="default_user"
              onConversationSelect={handleConversationSelect}
              onNewConversation={handleNewConversation}
            />
          </Col>
        </Row>
      </div>

      {/* 底部信息栏 */}
      <Card size="small" style={{ marginTop: '16px', flex: '0 0 auto' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Text type="secondary">
            💡 提示: 点击对话可以选择，使用操作按钮可以查看历史、编辑标题或删除对话
          </Text>
          
          <Space>
            <Text type="secondary">
              {selectedConversationId ? `已选择: ${selectedConversationId}` : '未选择对话'}
            </Text>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default DialogueHistoryPage;
