#!/usr/bin/env python3
"""
简化的LLM服务测试脚本
"""

import asyncio

# 环境变量将从backend/.env文件自动加载

async def test_basic_llm():
    """测试基础LLM调用"""
    print("🔍 测试基础LLM调用...")
    
    try:
        from services.llm_service import llm_service
        
        messages = [
            {
                "role": "system",
                "content": "你是一个有用的AI助手。"
            },
            {
                "role": "user",
                "content": "请简单介绍一下你自己，用中文回答，不超过50字。"
            }
        ]
        
        response = await llm_service.call_llm(messages)
        
        if response.success:
            print(f"✅ LLM调用成功")
            print(f"📝 响应内容: {response.content}")
            print(f"📊 Token使用: {response.usage}")
            print(f"🤖 模型: {response.model}")
            return True
        else:
            print(f"❌ LLM调用失败: {response.error}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_extraction():
    """测试信息抽取"""
    print("\n🔍 测试信息抽取...")
    
    try:
        from services.llm_service import llm_service, ExtractionRequest
        
        # 模拟文档内容
        document_content = """
        会议纪要
        
        会议时间：2024年7月4日 14:00-16:00
        会议地点：数字科技中心会议室A
        主持人：张三
        参会人员：李四、王五、赵六、钱七
        
        会议主题：数字化转型项目进展讨论
        """
        
        # 测试抽取会议时间
        request = ExtractionRequest(
            field_name="会议时间",
            field_description="会议举行的具体时间",
            field_example="2024年7月4日 14:00-16:00",
            field_format="日期时间",
            source_text=document_content
        )
        
        response = await llm_service.extract_field_info(request)
        
        if response.success:
            print(f"✅ 信息抽取成功")
            print(f"📝 抽取结果: {response.content}")
            return True
        else:
            print(f"❌ 信息抽取失败: {response.error}")
            return False
            
    except Exception as e:
        print(f"❌ 信息抽取测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("🚀 开始LLM服务简化测试\n")
    
    # 检查API密钥配置
    from config import settings
    if not settings.dashscope_api_key or settings.dashscope_api_key == "your-dashscope-api-key-here":
        print("⚠️  警告: DashScope API密钥未配置")
        print("请在.env文件中设置DASHSCOPE_API_KEY")
        return False
    
    print(f"✅ API密钥已配置: {settings.dashscope_api_key[:10]}...")
    print(f"🤖 使用模型: {settings.llm_model}")
    
    tests = [
        ("基础LLM调用", test_basic_llm),
        ("信息抽取功能", test_extraction)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"{'='*50}")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 发生异常: {e}")
            results.append((test_name, False))
    
    print(f"\n{'='*50}")
    print("📊 测试结果汇总:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    print(f"\n{'='*50}")
    if all_passed:
        print("🎉 所有LLM服务测试通过！")
        print("✅ LLM集成已准备就绪")
    else:
        print("❌ 部分测试失败，请检查配置和实现")
    
    return all_passed


if __name__ == "__main__":
    asyncio.run(main())
