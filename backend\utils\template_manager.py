"""
模板管理工具
提供模板上传、验证、同步等实用功能
"""

import os
import shutil
import json
from typing import Dict, List, Optional, Tuple
from pathlib import Path
from datetime import datetime

from services.template_validation_service import template_validation_service
from services.llm_service import llm_service, ExtractionRequest


class TemplateManager:
    """模板管理器"""
    
    def __init__(self, templates_dir: str = "templates", keys_dir: str = "key_definitions"):
        self.templates_dir = Path(templates_dir)
        self.keys_dir = Path(keys_dir)
        
        # 确保目录存在
        self.templates_dir.mkdir(exist_ok=True)
        self.keys_dir.mkdir(exist_ok=True)
    
    def upload_template_pair(self, template_file_path: str, key_definitions_file_path: str,
                           template_name: str) -> Dict[str, any]:
        """
        上传模板和关键字段定义文件对
        
        Args:
            template_file_path: 模板文件路径
            key_definitions_file_path: 关键字段定义文件路径
            template_name: 模板名称
            
        Returns:
            Dict: 上传结果和验证信息
        """
        try:
            # 验证文件存在
            if not os.path.exists(template_file_path):
                return {"success": False, "error": f"模板文件不存在: {template_file_path}"}
            
            if not os.path.exists(key_definitions_file_path):
                return {"success": False, "error": f"关键字段定义文件不存在: {key_definitions_file_path}"}
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            template_ext = Path(template_file_path).suffix
            
            template_filename = f"{template_name}_{timestamp}{template_ext}"
            key_filename = f"{template_name}_{timestamp}_key_definitions.md"
            
            # 目标路径
            target_template_path = self.templates_dir / template_filename
            target_key_path = self.keys_dir / key_filename
            
            # 执行预验证
            validation_result = await template_validation_service.validate_template_consistency(
                template_file_path, key_definitions_file_path
            )
            
            # 复制文件
            shutil.copy2(template_file_path, target_template_path)
            shutil.copy2(key_definitions_file_path, target_key_path)
            
            # 创建元数据文件
            metadata = {
                "template_name": template_name,
                "upload_time": datetime.now().isoformat(),
                "template_file": template_filename,
                "key_definitions_file": key_filename,
                "validation_result": {
                    "is_valid": validation_result.is_valid,
                    "overall_score": validation_result.overall_score,
                    "consistency_score": validation_result.consistency_score,
                    "completeness_score": validation_result.completeness_score,
                    "total_placeholders": validation_result.total_placeholders,
                    "matched_placeholders": validation_result.matched_placeholders,
                    "issues_count": len(validation_result.issues)
                }
            }
            
            metadata_path = self.templates_dir / f"{template_name}_{timestamp}_metadata.json"
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            return {
                "success": True,
                "data": {
                    "template_path": str(target_template_path),
                    "key_definitions_path": str(target_key_path),
                    "metadata_path": str(metadata_path),
                    "validation_result": validation_result,
                    "metadata": metadata
                }
            }
            
        except Exception as e:
            return {"success": False, "error": f"上传失败: {str(e)}"}
    
    def list_templates(self) -> List[Dict[str, any]]:
        """列出所有模板"""
        templates = []
        
        try:
            # 查找所有元数据文件
            for metadata_file in self.templates_dir.glob("*_metadata.json"):
                try:
                    with open(metadata_file, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)
                    
                    # 检查文件是否存在
                    template_path = self.templates_dir / metadata["template_file"]
                    key_path = self.keys_dir / metadata["key_definitions_file"]
                    
                    template_info = {
                        "template_name": metadata["template_name"],
                        "upload_time": metadata["upload_time"],
                        "template_file": metadata["template_file"],
                        "key_definitions_file": metadata["key_definitions_file"],
                        "template_exists": template_path.exists(),
                        "key_definitions_exists": key_path.exists(),
                        "validation_score": metadata.get("validation_result", {}).get("overall_score", 0),
                        "is_valid": metadata.get("validation_result", {}).get("is_valid", False),
                        "metadata_path": str(metadata_file)
                    }
                    
                    templates.append(template_info)
                    
                except Exception as e:
                    print(f"读取模板元数据失败 {metadata_file}: {e}")
                    continue
            
            # 按上传时间排序
            templates.sort(key=lambda x: x["upload_time"], reverse=True)
            return templates
            
        except Exception as e:
            print(f"列出模板失败: {e}")
            return []
    
    async def validate_template(self, template_name: str) -> Optional[Dict[str, any]]:
        """验证指定模板"""
        templates = self.list_templates()
        target_template = None
        
        for template in templates:
            if template["template_name"] == template_name:
                target_template = template
                break
        
        if not target_template:
            return {"success": False, "error": f"模板 '{template_name}' 不存在"}
        
        try:
            template_path = self.templates_dir / target_template["template_file"]
            key_path = self.keys_dir / target_template["key_definitions_file"]
            
            validation_result = await template_validation_service.validate_template_consistency(
                str(template_path), str(key_path)
            )
            
            suggestions = await template_validation_service.suggest_template_improvements(validation_result)
            summary = template_validation_service.get_validation_summary(validation_result)
            
            return {
                "success": True,
                "data": {
                    "template_info": target_template,
                    "validation_result": validation_result,
                    "suggestions": suggestions,
                    "summary": summary
                }
            }
            
        except Exception as e:
            return {"success": False, "error": f"验证失败: {str(e)}"}
    
    async def generate_extraction_requests(self, template_name: str, 
                                         document_content: str) -> List[ExtractionRequest]:
        """为指定模板生成抽取请求"""
        try:
            # 获取模板信息
            templates = self.list_templates()
            target_template = None
            
            for template in templates:
                if template["template_name"] == template_name:
                    target_template = template
                    break
            
            if not target_template:
                raise ValueError(f"模板 '{template_name}' 不存在")
            
            # 解析关键字段定义
            key_path = self.keys_dir / target_template["key_definitions_file"]
            field_definitions = await template_validation_service._parse_key_definitions(str(key_path))
            
            # 生成抽取请求
            extraction_requests = []
            for field_def in field_definitions:
                request = ExtractionRequest(
                    field_name=field_def.field_name,
                    field_description=field_def.description,
                    field_example=field_def.example,
                    field_format=field_def.format,
                    source_text=document_content,
                    must=field_def.must,
                    default_value=field_def.default if field_def.default else None
                )
                extraction_requests.append(request)
            
            return extraction_requests
            
        except Exception as e:
            raise Exception(f"生成抽取请求失败: {str(e)}")
    
    async def test_template_extraction(self, template_name: str, 
                                     test_document_path: str) -> Dict[str, any]:
        """测试模板的信息抽取效果"""
        try:
            # 读取测试文档
            with open(test_document_path, 'r', encoding='utf-8') as f:
                document_content = f.read()
            
            # 生成抽取请求
            extraction_requests = await self.generate_extraction_requests(template_name, document_content)
            
            # 执行抽取
            extraction_results = await llm_service.extract_multiple_fields(extraction_requests)
            
            # 统计结果
            total_fields = len(extraction_results)
            valid_fields = len([r for r in extraction_results if r.is_valid])
            high_confidence_fields = len([r for r in extraction_results if r.confidence > 0.7])
            avg_confidence = sum(r.confidence for r in extraction_results) / total_fields if total_fields else 0
            
            return {
                "success": True,
                "data": {
                    "template_name": template_name,
                    "test_document": test_document_path,
                    "extraction_results": [
                        {
                            "field_name": r.field_name,
                            "field_value": r.field_value,
                            "confidence": r.confidence,
                            "is_valid": r.is_valid,
                            "extraction_method": r.extraction_method
                        }
                        for r in extraction_results
                    ],
                    "statistics": {
                        "total_fields": total_fields,
                        "valid_fields": valid_fields,
                        "high_confidence_fields": high_confidence_fields,
                        "success_rate": valid_fields / total_fields if total_fields else 0,
                        "avg_confidence": avg_confidence,
                        "high_confidence_rate": high_confidence_fields / total_fields if total_fields else 0
                    }
                }
            }
            
        except Exception as e:
            return {"success": False, "error": f"测试抽取失败: {str(e)}"}
    
    def delete_template(self, template_name: str) -> Dict[str, any]:
        """删除模板"""
        try:
            templates = self.list_templates()
            target_template = None
            
            for template in templates:
                if template["template_name"] == template_name:
                    target_template = template
                    break
            
            if not target_template:
                return {"success": False, "error": f"模板 '{template_name}' 不存在"}
            
            # 删除文件
            files_deleted = []
            
            # 删除模板文件
            template_path = self.templates_dir / target_template["template_file"]
            if template_path.exists():
                template_path.unlink()
                files_deleted.append(str(template_path))
            
            # 删除关键字段定义文件
            key_path = self.keys_dir / target_template["key_definitions_file"]
            if key_path.exists():
                key_path.unlink()
                files_deleted.append(str(key_path))
            
            # 删除元数据文件
            metadata_path = Path(target_template["metadata_path"])
            if metadata_path.exists():
                metadata_path.unlink()
                files_deleted.append(str(metadata_path))
            
            return {
                "success": True,
                "data": {
                    "template_name": template_name,
                    "files_deleted": files_deleted
                }
            }
            
        except Exception as e:
            return {"success": False, "error": f"删除失败: {str(e)}"}
    
    async def sync_template_with_database(self, template_name: str) -> Dict[str, any]:
        """将模板同步到数据库"""
        try:
            # 获取模板信息
            validation_result = await self.validate_template(template_name)
            if not validation_result["success"]:
                return validation_result
            
            template_info = validation_result["data"]["template_info"]
            validation_data = validation_result["data"]["validation_result"]
            
            # TODO: 实现数据库同步逻辑
            # 这里需要调用数据库服务来创建模板记录
            
            return {
                "success": True,
                "data": {
                    "template_name": template_name,
                    "sync_status": "completed",
                    "database_id": "placeholder_id",  # 实际应该返回数据库中的ID
                    "validation_passed": validation_data.is_valid
                }
            }
            
        except Exception as e:
            return {"success": False, "error": f"数据库同步失败: {str(e)}"}


# 全局模板管理器实例
template_manager = TemplateManager()