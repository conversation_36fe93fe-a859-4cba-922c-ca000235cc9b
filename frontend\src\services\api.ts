/**
 * API服务层
 * 处理与后端的所有HTTP通信
 */

import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:8000',
  timeout: 60000, // 60秒超时，因为LLM处理可能需要较长时间
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.status, response.config.url);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.status, error.response?.data);
    return Promise.reject(error);
  }
);

// 类型定义
export interface TemplateField {
  field_name: string;
  description: string;
  example: string;
  format: string;
}

export interface WorkflowResult {
  workflow_id: string;
  status: string;
  file_info: {
    filename: string;
    document_type: string;
    text_length: number;
    paragraph_count: number;
    table_count: number;
  };
  extracted_data: Record<string, string>;
  generated_report: string;
  confidence_scores: Record<string, number>;
  validation_results: Record<string, any>;
  processing_time: number;
  error_message?: string;
}

export interface UploadResult {
  file_id: string;
  filename: string;
  file_path: string;
  document_type: string;
  text_length: number;
  paragraph_count: number;
  table_count: number;
  metadata: Record<string, any>;
  preview: string;
}

export interface DocumentContent {
  file_id: string;
  filename: string;
  document_type: string;
  raw_text: string;
  paragraphs: string[];
  tables: any[];
  metadata: Record<string, any>;
}

// API方法
export const apiService = {
  // 健康检查
  async healthCheck() {
    const response = await api.get('/health');
    return response.data;
  },

  // 文件上传
  async uploadDocument(file: File): Promise<UploadResult> {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await api.post('/api/upload/document', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    return response.data;
  },

  // 获取文档内容
  async getDocumentContent(fileId: string): Promise<DocumentContent> {
    const response = await api.get(`/api/upload/document/${fileId}`);
    return response.data;
  },

  // 删除文档
  async deleteDocument(fileId: string) {
    const response = await api.delete(`/api/upload/document/${fileId}`);
    return response.data;
  },

  // 获取上传文档列表
  async getUploadedDocuments() {
    const response = await api.get('/api/upload/list');
    return response.data;
  },

  // 端到端工作流 - 上传并处理
  async processDocumentWorkflow(
    file: File,
    templateFields: TemplateField[],
    reportTemplate?: string
  ): Promise<WorkflowResult> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('template_fields', JSON.stringify(templateFields));
    
    if (reportTemplate) {
      formData.append('report_template', reportTemplate);
    }
    
    const response = await api.post('/api/workflow/process-document', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    return response.data;
  },

  // 处理已上传文件的工作流
  async processUploadedFileWorkflow(
    fileId: string,
    templateFields: TemplateField[],
    reportTemplate?: string
  ): Promise<WorkflowResult> {
    const response = await api.post('/api/workflow/process-uploaded-file', {
      file_id: fileId,
      template_fields: templateFields,
      report_template: reportTemplate,
    });
    
    return response.data;
  },

  // 获取工作流状态
  async getWorkflowStatus(workflowId: string) {
    const response = await api.get(`/api/workflow/status/${workflowId}`);
    return response.data;
  },

  // 获取演示字段模板
  async getDemoFields() {
    const response = await api.get('/api/workflow/demo-fields');
    return response.data;
  },

  // 获取模板列表
  async getTemplates() {
    const response = await api.get('/api/templates/');
    return response.data;
  },

  // 获取数据源列表
  async getDataSources() {
    const response = await api.get('/api/data/sources');
    return response.data;
  },

  // 获取报告列表
  async getReports() {
    const response = await api.get('/api/reports/');
    return response.data;
  },
};

export default api;
