"""
模板管理API路由
"""

from typing import List
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlalchemy.orm import Session
from database import get_db
from models.template import Template, TemplateField
from schemas.template import (
    TemplateCreate, TemplateUpdate, TemplateResponse,
    TemplateListResponse, TemplateFieldResponse
)
from schemas.common import MessageResponse, PaginatedResponse

router = APIRouter(prefix="/api/templates", tags=["模板管理"])


@router.get("/", response_model=PaginatedResponse[TemplateListResponse])
async def list_templates(
    page: int = 1,
    size: int = 20,
    db: Session = Depends(get_db)
):
    """获取模板列表"""
    offset = (page - 1) * size

    templates = db.query(Template).filter(Template.is_active == True).offset(offset).limit(size).all()
    total = db.query(Template).filter(Template.is_active == True).count()

    items = [TemplateListResponse.from_orm(template) for template in templates]
    pages = (total + size - 1) // size

    return PaginatedResponse(
        items=items,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/{template_id}", response_model=TemplateResponse)
async def get_template(template_id: UUID, db: Session = Depends(get_db)):
    """获取模板详情"""
    template = db.query(Template).filter(
        Template.id == template_id,
        Template.is_active == True
    ).first()

    if not template:
        raise HTTPException(status_code=404, detail="模板不存在")

    return TemplateResponse.from_orm(template)


@router.post("/upload", response_model=TemplateResponse)
async def upload_template(
    name: str = Form(...),
    description: str = Form(None),
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """上传模板文件"""
    # 验证文件类型
    if not file.filename.endswith(('.doc', '.docx')):
        raise HTTPException(status_code=400, detail="只支持Word文档格式")

    # TODO: 实现文件保存和占位符解析逻辑
    file_path = f"uploads/templates/{file.filename}"

    # 创建模板记录
    template = Template(
        name=name,
        description=description,
        file_path=file_path,
        file_size=file.size,
        mime_type=file.content_type,
        created_by="system"  # TODO: 从认证信息获取
    )

    db.add(template)
    db.commit()
    db.refresh(template)

    return TemplateResponse.from_orm(template)


@router.put("/{template_id}", response_model=TemplateResponse)
async def update_template(
    template_id: UUID,
    template_update: TemplateUpdate,
    db: Session = Depends(get_db)
):
    """更新模板信息"""
    template = db.query(Template).filter(
        Template.id == template_id,
        Template.is_active == True
    ).first()

    if not template:
        raise HTTPException(status_code=404, detail="模板不存在")

    # 更新字段
    for field, value in template_update.dict(exclude_unset=True).items():
        setattr(template, field, value)

    db.commit()
    db.refresh(template)

    return TemplateResponse.from_orm(template)


@router.delete("/{template_id}", response_model=MessageResponse)
async def delete_template(template_id: UUID, db: Session = Depends(get_db)):
    """删除模板(软删除)"""
    template = db.query(Template).filter(
        Template.id == template_id,
        Template.is_active == True
    ).first()

    if not template:
        raise HTTPException(status_code=404, detail="模板不存在")

    template.is_active = False
    db.commit()

    return MessageResponse(message="模板删除成功")


@router.get("/{template_id}/fields", response_model=List[TemplateFieldResponse])
async def get_template_fields(template_id: UUID, db: Session = Depends(get_db)):
    """获取模板字段列表"""
    template = db.query(Template).filter(
        Template.id == template_id,
        Template.is_active == True
    ).first()

    if not template:
        raise HTTPException(status_code=404, detail="模板不存在")

    fields = db.query(TemplateField).filter(
        TemplateField.template_id == template_id,
        TemplateField.is_active == True
    ).order_by(TemplateField.field_order).all()

    return [TemplateFieldResponse.from_orm(field) for field in fields]