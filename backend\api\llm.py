"""
LLM API接口
提供LLM服务的HTTP接口
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Any, Optional
from pydantic import BaseModel

from services.llm_service import llm_service, ExtractionRequest, ExtractionResult, ValidationResult

router = APIRouter(prefix="/api/llm", tags=["LLM服务"])


class ExtractionRequestModel(BaseModel):
    """信息抽取请求模型"""
    field_name: str
    field_description: str
    field_example: str
    field_format: str
    source_text: str
    context: Optional[str] = None
    must: bool = False
    default_value: Optional[str] = None


class BatchExtractionRequest(BaseModel):
    """批量抽取请求模型"""
    requests: List[ExtractionRequestModel]


class ValidationRequest(BaseModel):
    """验证请求模型"""
    field_name: str
    field_value: str
    field_format: str
    original_text: str


class ReportGenerationRequest(BaseModel):
    """报告生成请求模型"""
    template_content: str
    extracted_data: Dict[str, str]


class DocumentAnalysisRequest(BaseModel):
    """文档分析请求模型"""
    document_content: str


@router.post("/extract/single")
async def extract_single_field(request: ExtractionRequestModel):
    """
    单字段信息抽取
    """
    try:
        extraction_request = ExtractionRequest(
            field_name=request.field_name,
            field_description=request.field_description,
            field_example=request.field_example,
            field_format=request.field_format,
            source_text=request.source_text,
            context=request.context,
            must=request.must,
            default_value=request.default_value
        )
        
        response = await llm_service.extract_field_info(extraction_request)
        
        if response.success:
            # 计算置信度
            confidence = llm_service._calculate_extraction_confidence(
                response.content.strip(),
                request.field_example,
                request.field_format
            )
            
            result = ExtractionResult(
                field_name=request.field_name,
                field_value=response.content.strip(),
                confidence=confidence,
                is_valid=confidence > 0.3
            )
            
            return {
                "success": True,
                "data": {
                    "field_name": result.field_name,
                    "field_value": result.field_value,
                    "confidence": result.confidence,
                    "is_valid": result.is_valid,
                    "extraction_method": result.extraction_method
                }
            }
        else:
            return {
                "success": False,
                "error": response.error,
                "data": None
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"信息抽取失败: {str(e)}")


@router.post("/extract/batch")
async def extract_multiple_fields(request: BatchExtractionRequest):
    """
    批量字段信息抽取
    """
    try:
        extraction_requests = [
            ExtractionRequest(
                field_name=req.field_name,
                field_description=req.field_description,
                field_example=req.field_example,
                field_format=req.field_format,
                source_text=req.source_text,
                context=req.context,
                must=req.must,
                default_value=req.default_value
            )
            for req in request.requests
        ]
        
        results = await llm_service.extract_multiple_fields(extraction_requests)
        
        return {
            "success": True,
            "data": {
                "results": [
                    {
                        "field_name": result.field_name,
                        "field_value": result.field_value,
                        "confidence": result.confidence,
                        "is_valid": result.is_valid,
                        "extraction_method": result.extraction_method,
                        "error_message": result.error_message
                    }
                    for result in results
                ],
                "summary": {
                    "total_fields": len(results),
                    "valid_fields": len([r for r in results if r.is_valid]),
                    "avg_confidence": sum(r.confidence for r in results) / len(results) if results else 0
                }
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量信息抽取失败: {str(e)}")


@router.post("/validate")
async def validate_extracted_data(request: ValidationRequest):
    """
    验证抽取数据的准确性
    """
    try:
        response = await llm_service.validate_extracted_data(
            field_name=request.field_name,
            field_value=request.field_value,
            field_format=request.field_format,
            original_text=request.original_text
        )
        
        validation_result = await llm_service.parse_validation_response(response)
        
        return {
            "success": True,
            "data": {
                "is_valid": validation_result.is_valid,
                "confidence": validation_result.confidence,
                "reason": validation_result.reason,
                "suggestions": validation_result.suggestions
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据验证失败: {str(e)}")


@router.post("/generate_report")
async def generate_report_content(request: ReportGenerationRequest):
    """
    生成报告内容
    """
    try:
        response = await llm_service.generate_report_content(
            template_content=request.template_content,
            extracted_data=request.extracted_data
        )
        
        if response.success:
            return {
                "success": True,
                "data": {
                    "report_content": response.content,
                    "model": response.model,
                    "usage": response.usage
                }
            }
        else:
            return {
                "success": False,
                "error": response.error,
                "data": None
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"报告生成失败: {str(e)}")


@router.post("/analyze_document")
async def analyze_document_structure(request: DocumentAnalysisRequest):
    """
    分析文档结构
    """
    try:
        analysis_result = await llm_service.analyze_document_structure(request.document_content)
        
        return {
            "success": True,
            "data": analysis_result
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文档分析失败: {str(e)}")


@router.post("/improve_extraction")
async def improve_extraction_quality(
    original_text: str,
    extraction_results: List[Dict[str, Any]]
):
    """
    改进抽取质量
    """
    try:
        # 转换输入格式
        results = [
            ExtractionResult(
                field_name=result["field_name"],
                field_value=result["field_value"],
                confidence=result["confidence"],
                is_valid=result.get("is_valid", True),
                extraction_method=result.get("extraction_method", "llm_extraction"),
                error_message=result.get("error_message")
            )
            for result in extraction_results
        ]
        
        improved_results = await llm_service.improve_extraction_quality(original_text, results)
        
        return {
            "success": True,
            "data": {
                "improved_results": [
                    {
                        "field_name": result.field_name,
                        "field_value": result.field_value,
                        "confidence": result.confidence,
                        "is_valid": result.is_valid,
                        "extraction_method": result.extraction_method,
                        "error_message": result.error_message
                    }
                    for result in improved_results
                ],
                "improvement_summary": {
                    "original_avg_confidence": sum(r.confidence for r in results) / len(results) if results else 0,
                    "improved_avg_confidence": sum(r.confidence for r in improved_results) / len(improved_results) if improved_results else 0,
                    "improvement_count": len([r for r in improved_results if r.extraction_method == "improved_extraction"])
                }
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"抽取质量改进失败: {str(e)}")


@router.get("/health")
async def llm_health_check():
    """
    LLM服务健康检查
    """
    try:
        # 简单的健康检查调用
        test_messages = [
            {
                "role": "user",
                "content": "你好"
            }
        ]
        
        response = await llm_service.call_llm(test_messages)
        
        return {
            "success": response.success,
            "status": "healthy" if response.success else "unhealthy",
            "model": response.model if response.success else None,
            "error": response.error if not response.success else None
        }
        
    except Exception as e:
        return {
            "success": False,
            "status": "unhealthy",
            "error": str(e)
        }