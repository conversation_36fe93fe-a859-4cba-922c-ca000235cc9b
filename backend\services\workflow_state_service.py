"""
工作流状态服务
提供工作流状态的持久化存储和检索
"""

import json
import os
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path

class WorkflowStateService:
    """工作流状态服务"""
    
    def __init__(self):
        # 状态存储目录
        self.state_dir = Path("workflow_states")
        self.state_dir.mkdir(exist_ok=True)
    
    def save_workflow_state(self, workflow_id: str, state: Dict[str, Any]) -> bool:
        """
        保存工作流状态
        
        Args:
            workflow_id: 工作流ID
            state: 状态数据
            
        Returns:
            bool: 保存是否成功
        """
        try:
            state_file = self.state_dir / f"{workflow_id}.json"
            
            # 添加时间戳
            state_with_timestamp = {
                **state,
                "saved_at": datetime.now().isoformat(),
                "workflow_id": workflow_id
            }
            
            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(state_with_timestamp, f, ensure_ascii=False, indent=2, default=str)
            
            return True
            
        except Exception as e:
            print(f"保存工作流状态失败 {workflow_id}: {e}")
            return False
    
    def get_workflow_state(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """
        获取工作流状态
        
        Args:
            workflow_id: 工作流ID
            
        Returns:
            Optional[Dict[str, Any]]: 状态数据，如果不存在则返回None
        """
        try:
            state_file = self.state_dir / f"{workflow_id}.json"
            
            if not state_file.exists():
                return None
            
            with open(state_file, 'r', encoding='utf-8') as f:
                state = json.load(f)
            
            return state
            
        except Exception as e:
            print(f"获取工作流状态失败 {workflow_id}: {e}")
            return None
    
    def update_workflow_state(self, workflow_id: str, updates: Dict[str, Any]) -> bool:
        """
        更新工作流状态
        
        Args:
            workflow_id: 工作流ID
            updates: 要更新的字段
            
        Returns:
            bool: 更新是否成功
        """
        try:
            current_state = self.get_workflow_state(workflow_id)
            if current_state is None:
                # 如果状态不存在，创建新状态
                current_state = {"workflow_id": workflow_id}
            
            # 更新状态
            current_state.update(updates)
            current_state["updated_at"] = datetime.now().isoformat()
            
            return self.save_workflow_state(workflow_id, current_state)
            
        except Exception as e:
            print(f"更新工作流状态失败 {workflow_id}: {e}")
            return False
    
    def delete_workflow_state(self, workflow_id: str) -> bool:
        """
        删除工作流状态
        
        Args:
            workflow_id: 工作流ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            state_file = self.state_dir / f"{workflow_id}.json"
            
            if state_file.exists():
                state_file.unlink()
            
            return True
            
        except Exception as e:
            print(f"删除工作流状态失败 {workflow_id}: {e}")
            return False
    
    def list_workflow_states(self) -> Dict[str, Dict[str, Any]]:
        """
        列出所有工作流状态
        
        Returns:
            Dict[str, Dict[str, Any]]: 工作流ID到状态的映射
        """
        try:
            states = {}
            
            for state_file in self.state_dir.glob("*.json"):
                workflow_id = state_file.stem
                state = self.get_workflow_state(workflow_id)
                if state:
                    states[workflow_id] = state
            
            return states
            
        except Exception as e:
            print(f"列出工作流状态失败: {e}")
            return {}

# 创建全局实例
workflow_state_service = WorkflowStateService()
