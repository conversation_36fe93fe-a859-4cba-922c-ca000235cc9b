# 测试文件目录

本目录包含报告智能生成工具的所有测试相关文件。

## 📁 目录结构

```
test/
├── README.md           # 本说明文档
├── documents/          # 测试文档
│   ├── test_meeting.txt
│   └── test_project_report.docx
├── scripts/            # 原有测试脚本
│   ├── test_db.py                    # 数据库连接测试
│   ├── test_llm_service.py          # LLM服务测试
│   ├── test_llm_simple.py           # 简单LLM测试
│   ├── test_document_service.py     # 文档服务测试
│   ├── test_agent_integration.py    # 智能体集成测试
│   ├── test_upload.py               # 文件上传测试
│   ├── test_workflow.py             # 端到端工作流测试
│   ├── check_template_fields.py     # 模板字段检查
│   ├── final_verification.py        # 最终验证脚本
│   └── recreate_db.py               # 数据库重建脚本
├── conftest.py                      # pytest配置和共享fixtures
├── run_langgraph_tests.py           # LangGraph专用测试运行脚本
├── test_langgraph_state.py          # LangGraph状态管理测试
├── test_langgraph_nodes.py          # LangGraph节点功能测试
├── test_langgraph_workflow.py       # LangGraph工作流集成测试
└── test_langgraph_api.py            # LangGraph API接口测试
└── data/               # 测试数据（预留）
```

## 🧪 测试脚本说明

### 基础功能测试

1. **test_db.py** - 数据库连接测试
   ```bash
   uv run python test/scripts/test_db.py
   ```

2. **test_llm_service.py** - LLM服务完整测试
   ```bash
   uv run python test/scripts/test_llm_service.py
   ```

3. **test_llm_simple.py** - LLM基础功能测试
   ```bash
   uv run python test/scripts/test_llm_simple.py
   ```

### 服务层测试

4. **test_document_service.py** - 文档解析服务测试
   ```bash
   uv run python test/scripts/test_document_service.py
   ```

5. **test_upload.py** - 文件上传API测试
   ```bash
   uv run python test/scripts/test_upload.py
   ```

### 集成测试

6. **test_agent_integration.py** - 智能体集成测试
   ```bash
   uv run python test/scripts/test_agent_integration.py
   ```

7. **test_workflow.py** - 端到端工作流测试
   ```bash
   uv run python test/scripts/test_workflow.py
   ```

### 维护脚本

8. **recreate_db.py** - 重建数据库
   ```bash
   uv run python test/scripts/recreate_db.py
   ```

9. **check_template_fields.py** - 检查模板字段
   ```bash
   uv run python test/scripts/check_template_fields.py
   ```

10. **final_verification.py** - 最终系统验证
    ```bash
    uv run python test/scripts/final_verification.py
    ```

## 🤖 LangGraph智能体测试

### 新增LangGraph测试套件

11. **test_langgraph_state.py** - LangGraph状态管理测试
    ```bash
    python test/test_langgraph_state.py
    # 或使用pytest
    python -m pytest test/test_langgraph_state.py -v
    ```

12. **test_langgraph_nodes.py** - LangGraph节点功能测试
    ```bash
    python test/test_langgraph_nodes.py
    # 或使用pytest
    python -m pytest test/test_langgraph_nodes.py -v
    ```

13. **test_langgraph_workflow.py** - LangGraph工作流集成测试
    ```bash
    python test/test_langgraph_workflow.py
    # 或使用pytest
    python -m pytest test/test_langgraph_workflow.py -v
    ```

14. **test_langgraph_api.py** - LangGraph API接口测试
    ```bash
    python test/test_langgraph_api.py
    # 或使用pytest
    python -m pytest test/test_langgraph_api.py -v
    ```

### LangGraph测试运行脚本

15. **run_langgraph_tests.py** - 运行所有LangGraph测试
    ```bash
    # 运行完整LangGraph测试套件
    python test/run_langgraph_tests.py

    # 运行快速测试（不依赖外部服务）
    python test/run_langgraph_tests.py --quick
    ```

## 📄 测试文档

### test_meeting.txt
会议纪要测试文档，包含：
- 会议基本信息
- 参会人员
- 会议内容
- 项目进展

### test_project_report.docx
项目报告测试文档，包含：
- 项目概述
- 进展情况
- 问题与风险
- 下一步计划

## 🚀 快速测试流程

### 1. 完整系统测试
```bash
# 1. 重建数据库
uv run python test/scripts/recreate_db.py

# 2. 测试基础功能
uv run python test/scripts/test_db.py
uv run python test/scripts/test_llm_simple.py

# 3. 测试服务层
uv run python test/scripts/test_document_service.py
uv run python test/scripts/test_upload.py

# 4. 测试LangGraph智能体架构
python test/run_langgraph_tests.py

# 5. 测试端到端工作流
uv run python test/scripts/test_workflow.py

# 6. 最终验证
uv run python test/scripts/final_verification.py
```

### 2. 开发调试测试
```bash
# 快速验证核心功能
uv run python test/scripts/test_llm_simple.py
uv run python test/scripts/test_document_service.py
uv run python test/scripts/test_workflow.py
```

## 📝 注意事项

1. **环境要求**：
   - 确保PostgreSQL数据库运行
   - 设置正确的环境变量（DATABASE_URL, DASHSCOPE_API_KEY等）
   - 安装所有依赖包

2. **测试顺序**：
   - 建议按照上述顺序执行测试
   - 如果某个测试失败，检查依赖服务状态

3. **测试数据**：
   - 测试文档位于 `test/documents/` 目录
   - 测试过程中会创建临时文件，测试完成后会自动清理

4. **API测试**：
   - 需要FastAPI服务运行在 http://localhost:8000
   - 可以使用 `uv run python app.py` 启动服务

## 🔧 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查PostgreSQL服务状态
   - 验证DATABASE_URL环境变量

2. **LLM调用失败**
   - 检查DASHSCOPE_API_KEY设置
   - 验证网络连接

3. **文件上传失败**
   - 检查uploads目录权限
   - 验证文件大小限制

4. **工作流测试失败**
   - 确保所有依赖服务正常
   - 检查API服务状态

---

*最后更新：2024年7月4日*
