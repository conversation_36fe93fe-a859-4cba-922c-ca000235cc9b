# 报告智能生成工具V0.1改进计划

## 一、项目概述

### 1.1 改进目标
基于当前应用存在的问题，将系统升级为基于LangGraph的智能体应用，实现human-in-the-loop的报告生成流程，提升智能化程度和用户体验。

### 1.2 核心改进方向
1. **智能体架构升级**：从简单的模板填充升级为基于LangGraph的多智能体协同系统
2. **数据结构优化**：完善模板数据、关键信息定义、提取数据的标准化管理
3. **界面重构**：实现简洁的三栏式布局，增加专门的管理界面
4. **流程智能化**：增加人机交互确认环节，提升生成质量

## 二、详细工作计划

### 阶段一：数据结构与存储设计 (预计3天)

#### 工作项1.1：数据库表结构设计
**描述**：根据新的数据要求设计完整的数据库表结构
**具体任务**：
- 设计模板管理表（templates）
- 设计关键信息定义表（key_definitions）
- 设计提取数据表（extracted_data）
- 设计生成结果表（generated_reports）
- 设计用户会话表（user_sessions）
- 设计历史对话表（conversation_history）

**检查项**：
- [x] 数据库ER图设计完成
- [x] 所有表结构SQL脚本编写完成
- [x] 表关系和约束定义正确
- [x] 支持模板占位符的嵌套结构存储
- [x] 支持7个字段属性的完整存储（description/example/format/default/length/source/must）
- [x] 数据库初始化脚本测试通过

#### 工作项1.2：数据模型类更新
**描述**：更新backend/models中的数据模型类
**具体任务**：
- 更新Template模型，支持占位符解析
- 创建KeyDefinition模型，支持层级结构
- 创建ExtractedData模型，支持树形数据存储
- 创建GeneratedReport模型
- 更新User和Session相关模型

**检查项**：
- [x] 所有模型类定义完成
- [x] 模型关系映射正确
- [x] 支持JSON字段存储复杂数据结构
- [x] 模型验证规则完整
- [x] 数据库迁移脚本生成成功

### 阶段二：智能体架构开发 (预计5天)

#### 工作项2.1：LangGraph基础架构搭建
**描述**：建立基于LangGraph的智能体运行框架
**具体任务**：
- 安装和配置LangGraph依赖
- 创建智能体基础类和接口
- 实现智能体状态管理
- 建立工具调用机制
- 配置DashScope API集成

**检查项**：
- [x] LangGraph环境配置成功
- [x] 智能体基础框架代码完成
- [x] 状态管理机制测试通过
- [x] DashScope API连接测试成功
- [x] 基础工具调用流程验证完成

#### 工作项2.2：意图识别智能体开发
**描述**：开发意图识别智能体，作为系统入口
**具体任务**：
- 创建意图识别智能体类
- 编写意图识别prompt模板
- 实现意图分类逻辑（文本生成/其他）
- 建立智能体调度机制
- 实现错误处理和重试机制

**检查项**：
- [x] 意图识别智能体代码完成
- [ ] prompt模板文件创建（backend/agents/prompts/intention_identification.txt）
- [ ] 意图分类准确率测试通过（>90%）
- [x] 智能体调度逻辑验证完成
- [x] 异常情况处理测试通过

#### 工作项2.3：报告生成智能体开发
**描述**：开发核心的报告生成智能体
**具体任务**：
- 创建报告生成智能体类
- 编写报告生成相关prompt模板
- 实现四个阶段的处理逻辑
- 建立human-in-the-loop交互机制
- 实现状态持久化

**检查项**：
- [x] 报告生成智能体代码完成
- [ ] prompt模板文件创建（backend/agents/prompts/report_generation.txt）
- [x] 四个处理阶段逻辑实现完成
- [x] 人机交互接口定义完成
- [x] 状态持久化机制测试通过

### 阶段三：工具开发 (预计4天)

#### 工作项3.1：智能模板识别工具
**描述**：开发智能模板识别和匹配工具
**具体任务**：
- 实现模板意图分析算法
- 建立模板匹配评分机制
- 实现置信度评估
- 创建用户确认接口
- 编写工具测试用例

**检查项**：
- [ ] 工具类代码完成（backend/agents/tools/template_identification.py）
- [ ] 模板匹配算法测试通过
- [ ] 置信度评估准确性验证完成
- [ ] 用户确认接口联调成功
- [ ] 单元测试覆盖率>80%

#### 工作项3.2：智能模板解析工具
**描述**：开发Word模板占位符解析工具
**具体任务**：
- 实现Word文档占位符扫描
- 建立占位符与字段定义匹配
- 生成结构化提取prompt
- 实现解析结果验证
- 创建解析日志记录

**检查项**：
- [ ] 工具类代码完成（backend/agents/tools/template_parsing.py）
- [ ] Word文档解析功能测试通过
- [ ] 占位符识别准确率>95%
- [ ] prompt生成质量验证完成
- [ ] 解析日志功能测试通过

#### 工作项3.3：智能数据提取与验证工具
**描述**：开发数据提取、验证和确认一体化工具
**具体任务**：
- 实现多文件格式数据提取
- 建立数据验证和完整性检查
- 实现默认值填充逻辑
- 创建人工确认界面数据准备
- 建立数据质量评分机制

**检查项**：
- [ ] 工具类代码完成（backend/agents/tools/data_extraction.py）
- [ ] 多格式文件解析测试通过（docx/txt/pdf）
- [ ] 数据验证规则实现完成
- [ ] 默认值填充逻辑测试通过
- [ ] 数据质量评分算法验证完成

#### 工作项3.4：智能报告生成工具
**描述**：开发基于模板的报告生成工具
**具体任务**：
- 实现Word模板数据映射
- 建立报告内容生成逻辑
- 实现格式保持和样式继承
- 创建质量检查机制
- 建立文件输出管理

**检查项**：
- [ ] 工具类代码完成（backend/agents/tools/report_generation.py）
- [ ] Word模板填充功能测试通过
- [ ] 格式样式保持验证完成
- [ ] 报告质量检查机制测试通过
- [ ] 文件输出和存储功能验证完成

### 阶段四：后端API开发 (预计3天)

#### 工作项4.1：智能体交互API
**描述**：开发智能体交互相关的API接口
**具体任务**：
- 创建对话会话管理API
- 实现智能体调用API
- 建立实时状态查询API
- 创建人工确认响应API
- 实现文件上传处理API

**检查项**：
- [ ] 会话管理API测试通过
- [ ] 智能体调用接口验证完成
- [ ] 实时状态查询功能测试通过
- [ ] 人工确认响应处理验证完成
- [ ] 文件上传API安全性测试通过

#### 工作项4.2：数据管理API
**描述**：开发模板、数据、结果管理API
**具体任务**：
- 创建模板管理CRUD API
- 实现数据管理CRUD API
- 建立结果管理CRUD API
- 创建批量操作API
- 实现数据导入导出API

**检查项**：
- [ ] 模板管理API功能测试通过
- [ ] 数据管理API功能测试通过
- [ ] 结果管理API功能测试通过
- [ ] 批量操作性能测试通过
- [ ] 数据导入导出功能验证完成

### 阶段五：前端界面重构 (预计4天)

#### 工作项5.1：界面布局重构
**描述**：按照新的三栏式布局重构前端界面
**具体任务**：
- 实现导航栏组件（左侧1/7宽度）
- 创建历史对话栏组件（右侧1/7宽度）
- 重构主操作界面（中间5/7宽度）
- 实现可调节的分栏布局
- 优化响应式设计

**检查项**：
- [ ] 三栏式布局实现完成
- [ ] 导航栏功能测试通过
- [ ] 历史对话栏显示正常
- [ ] 主操作界面交互验证完成
- [ ] 分栏调节功能测试通过
- [ ] 响应式适配测试通过

#### 工作项5.2：管理界面开发
**描述**：开发模板、数据、结果管理界面
**具体任务**：
- 创建模板管理界面组件
- 实现数据管理界面组件
- 开发结果管理界面组件
- 建立统一的列表和表格组件
- 实现分页和搜索功能

**检查项**：
- [ ] 模板管理界面功能完整
- [ ] 数据管理界面功能完整
- [ ] 结果管理界面功能完整
- [ ] 列表组件复用性验证完成
- [ ] 分页搜索功能测试通过

#### 工作项5.3：智能体交互界面
**描述**：开发支持智能体交互的界面组件
**具体任务**：
- 创建结果交互区组件
- 实现多类型内容展示组件
- 开发用户选择和确认组件
- 建立文件上传和预览组件
- 实现实时状态更新

**检查项**：
- [ ] 结果交互区组件功能完整
- [ ] 多类型内容展示测试通过
- [ ] 用户选择确认组件验证完成
- [ ] 文件上传预览功能测试通过
- [ ] 实时状态更新机制验证完成

### 阶段六：系统集成与测试 (预计2天)

#### 工作项6.1：端到端集成测试
**描述**：进行完整的系统集成测试
**具体任务**：
- 执行完整的报告生成流程测试
- 验证智能体协同工作效果
- 测试人机交互确认环节
- 验证数据一致性和完整性
- 进行性能和稳定性测试

**检查项**：
- [ ] 端到端流程测试通过
- [ ] 智能体协同效果验证完成
- [ ] 人机交互环节测试通过
- [ ] 数据一致性验证完成
- [ ] 性能指标达到预期

#### 工作项6.2：用户验收测试
**描述**：进行用户验收测试和问题修复
**具体任务**：
- 准备测试数据和测试用例
- 执行用户验收测试
- 收集用户反馈和问题
- 修复发现的问题
- 完善文档和使用说明

**检查项**：
- [ ] 测试用例执行完成
- [ ] 用户验收测试通过
- [ ] 关键问题修复完成
- [ ] 用户反馈处理完成
- [ ] 系统文档更新完成

## 三、风险控制与质量保证

### 3.1 技术风险
- **LangGraph集成风险**：提前进行技术验证，准备备选方案
- **API调用稳定性**：实现重试机制和降级策略
- **数据迁移风险**：制定详细的数据备份和回滚计划

### 3.2 质量保证措施
- 每个工作项完成后必须通过检查项验证
- 关键功能需要编写单元测试和集成测试
- 定期进行代码审查和架构评审
- 建立持续集成和自动化测试流程

### 3.3 进度控制
- 每日进行工作进展同步
- 每个阶段结束进行里程碑评审
- 及时识别和处理进度风险
- 必要时调整计划和资源分配

## 四、交付成果

### 4.1 代码交付
- 完整的后端智能体系统代码
- 重构后的前端界面代码
- 数据库脚本和配置文件
- 单元测试和集成测试代码

### 4.2 文档交付
- 系统架构设计文档
- API接口文档
- 用户操作手册
- 部署和运维文档

### 4.3 功能验证
- 基于会议纪要模板的完整报告生成流程
- Human-in-the-loop的智能体交互体验
- 模板、数据、结果的完整管理功能
- 系统的可扩展性和稳定性验证

---

**计划制定时间**：2025年7月23日
**预计完成时间**：2025年8月5日（约21个工作日）
**计划制定人**：Augment Agent

---

## 五、当前完成状态总结（2025年7月23日更新）

### 5.1 已完成项目
✅ **阶段一：数据结构与存储设计** - **100%完成**
- 数据库表结构设计完成，包含所有必要的表和字段
- 数据模型类全部实现，支持复杂数据结构存储
- 数据库迁移脚本编写完成并测试通过

✅ **阶段二：智能体架构开发** - **80%完成**
- LangGraph基础架构搭建完成
- 智能体基础框架和状态管理机制实现
- 报告生成工作流图构建完成
- 意图识别和报告生成智能体核心逻辑实现

### 5.2 进行中项目
🔄 **工具开发** - **20%完成**
- 智能体工具类框架已建立
- 需要完善具体的工具实现和测试

🔄 **后端API开发** - **30%完成**
- 基础API结构已建立
- 需要完善智能体交互和数据管理API

🔄 **前端界面重构** - **40%完成**
- 基础组件已实现
- 需要实现三栏式布局和管理界面

### 5.3 待开始项目
⏳ **系统集成与测试** - **0%完成**
- 端到端集成测试
- 用户验收测试

### 5.4 下一步工作重点
1. **完善智能体工具实现**：重点开发模板识别、解析、数据提取和报告生成工具
2. **完善prompt模板**：创建intention_identification.txt和report_generation.txt等prompt文件
3. **API接口完善**：实现智能体交互和数据管理相关API
4. **前端界面重构**：实现三栏式布局和管理界面
5. **系统集成测试**：进行端到端测试和性能优化

### 5.5 预计完成时间调整
基于当前进度，预计完成时间调整为：**2025年7月30日**（约7个工作日）
