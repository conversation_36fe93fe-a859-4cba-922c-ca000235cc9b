"""
LLM服务模块
支持通义千问DashScope API和本地模型
"""

import asyncio
import json
import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from openai import AsyncOpenAI
from config import settings

# 配置日志
logger = logging.getLogger(__name__)


class LLMProvider(Enum):
    """LLM提供商枚举"""
    DASHSCOPE = "dashscope"
    OPENAI = "openai"
    LOCAL = "local"


@dataclass
class LLMResponse:
    """LLM响应数据类"""
    content: str
    usage: Dict[str, int]
    model: str
    finish_reason: str
    success: bool = True
    error: Optional[str] = None


@dataclass
class ExtractionRequest:
    """信息抽取请求数据类"""
    field_name: str
    field_description: str
    field_example: str
    field_format: str
    source_text: str
    context: Optional[str] = None
    must: bool = False
    default_value: Optional[str] = None


@dataclass
class ExtractionResult:
    """信息抽取结果数据类"""
    field_name: str
    field_value: str
    confidence: float
    source_location: Optional[str] = None
    extraction_method: str = "llm_extraction"
    is_valid: bool = True
    error_message: Optional[str] = None


@dataclass
class ValidationResult:
    """验证结果数据类"""
    is_valid: bool
    confidence: float
    reason: str
    suggestions: List[str] = None
    
    def __post_init__(self):
        if self.suggestions is None:
            self.suggestions = []


class LLMService:
    """LLM服务类"""

    def __init__(self):
        self.provider = LLMProvider(settings.llm_provider)
        self.model = settings.llm_model
        self.temperature = settings.llm_temperature
        self.max_tokens = settings.llm_max_tokens
        self.timeout = settings.llm_timeout
        self.retry_times = settings.llm_retry_times

        # 初始化DashScope客户端（使用OpenAI兼容接口）
        if self.provider == LLMProvider.DASHSCOPE:
            if not settings.dashscope_api_key or settings.dashscope_api_key == "your-dashscope-api-key-here":
                logger.warning("DashScope API密钥未配置，请在.env文件中设置DASHSCOPE_API_KEY")

            self.client = AsyncOpenAI(
                api_key=settings.dashscope_api_key,
                base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
            )
        else:
            self.client = None
    
    async def call_llm(self, messages: List[Dict[str, str]], **kwargs) -> LLMResponse:
        """
        调用LLM服务
        
        Args:
            messages: 消息列表，格式为[{"role": "user", "content": "..."}]
            **kwargs: 其他参数
            
        Returns:
            LLMResponse: LLM响应结果
        """
        try:
            if self.provider == LLMProvider.DASHSCOPE:
                return await self._call_dashscope(messages, **kwargs)
            elif self.provider == LLMProvider.OPENAI:
                return await self._call_openai(messages, **kwargs)
            elif self.provider == LLMProvider.LOCAL:
                return await self._call_local(messages, **kwargs)
            else:
                raise ValueError(f"不支持的LLM提供商: {self.provider}")
                
        except Exception as e:
            logger.error(f"LLM调用失败: {e}")
            return LLMResponse(
                content="",
                usage={},
                model=self.model,
                finish_reason="error",
                success=False,
                error=str(e)
            )
    
    async def _call_dashscope(self, messages: List[Dict[str, str]], **kwargs) -> LLMResponse:
        """调用DashScope API（使用OpenAI兼容接口）"""
        try:
            if not self.client:
                raise ValueError("DashScope客户端未初始化")

            # 调用参数
            params = {
                "model": kwargs.get("model", self.model),
                "messages": messages,
                "temperature": kwargs.get("temperature", self.temperature),
                "max_tokens": kwargs.get("max_tokens", self.max_tokens),
            }

            # 调用DashScope API
            response = await self.client.chat.completions.create(**params)

            # 解析响应
            choice = response.choices[0]
            usage = response.usage

            return LLMResponse(
                content=choice.message.content,
                usage={
                    "prompt_tokens": usage.prompt_tokens,
                    "completion_tokens": usage.completion_tokens,
                    "total_tokens": usage.total_tokens
                },
                model=response.model,
                finish_reason=choice.finish_reason,
                success=True
            )

        except Exception as e:
            logger.error(f"DashScope调用异常: {e}")
            return LLMResponse(
                content="",
                usage={},
                model=self.model,
                finish_reason="error",
                success=False,
                error=str(e)
            )
    
    async def _call_openai(self, messages: List[Dict[str, str]], **kwargs) -> LLMResponse:
        """调用OpenAI API（预留接口）"""
        # TODO: 实现OpenAI API调用
        raise NotImplementedError("OpenAI API调用尚未实现")
    
    async def _call_local(self, messages: List[Dict[str, str]], **kwargs) -> LLMResponse:
        """调用本地模型（预留接口）"""
        # TODO: 实现本地模型调用
        raise NotImplementedError("本地模型调用尚未实现")
    
    async def extract_field_info(self, request: ExtractionRequest) -> LLMResponse:
        """
        从文档中抽取指定字段信息
        
        Args:
            request: 抽取请求
            
        Returns:
            LLMResponse: 抽取结果
        """
        # 构建提示词
        prompt = self._build_extraction_prompt(request)
        
        messages = [
            {
                "role": "system",
                "content": "你是一个专业的信息抽取助手，能够从文档中准确抽取指定的字段信息。请严格按照要求的格式返回结果。"
            },
            {
                "role": "user", 
                "content": prompt
            }
        ]
        
        return await self.call_llm(messages)
    
    async def extract_multiple_fields(self, requests: List[ExtractionRequest]) -> List[ExtractionResult]:
        """
        批量抽取多个字段信息
        
        Args:
            requests: 抽取请求列表
            
        Returns:
            List[ExtractionResult]: 抽取结果列表
        """
        results = []
        
        for request in requests:
            try:
                response = await self.extract_field_info(request)
                
                if response.success:
                    # 解析抽取结果
                    field_value = response.content.strip()
                    
                    # 计算置信度（基于响应质量）
                    confidence = self._calculate_extraction_confidence(
                        field_value, request.field_example, request.field_format
                    )
                    
                    # 应用默认值规则
                    if field_value == "未找到" or not field_value:
                        if request.default_value:
                            field_value = request.default_value
                            confidence = 0.5  # 默认值的置信度
                        elif request.must:
                            # 必填字段但未找到值
                            confidence = 0.0
                    
                    result = ExtractionResult(
                        field_name=request.field_name,
                        field_value=field_value,
                        confidence=confidence,
                        extraction_method="llm_batch_extraction",
                        is_valid=confidence > 0.3
                    )
                else:
                    # 抽取失败
                    result = ExtractionResult(
                        field_name=request.field_name,
                        field_value=request.default_value or "",
                        confidence=0.0,
                        extraction_method="error_fallback",
                        is_valid=False,
                        error_message=response.error
                    )
                
                results.append(result)
                
            except Exception as e:
                logger.error(f"字段 {request.field_name} 抽取异常: {e}")
                result = ExtractionResult(
                    field_name=request.field_name,
                    field_value=request.default_value or "",
                    confidence=0.0,
                    extraction_method="exception_fallback",
                    is_valid=False,
                    error_message=str(e)
                )
                results.append(result)
        
        return results
    
    def _calculate_extraction_confidence(self, extracted_value: str, 
                                       example_value: str, field_format: str) -> float:
        """
        计算抽取结果的置信度
        
        Args:
            extracted_value: 抽取的值
            example_value: 示例值
            field_format: 字段格式
            
        Returns:
            float: 置信度分数 (0-1)
        """
        if not extracted_value or extracted_value == "未找到":
            return 0.0
        
        confidence = 0.5  # 基础分数
        
        # 基于格式匹配的置信度调整
        if field_format == "人名" and self._is_person_name(extracted_value):
            confidence += 0.3
        elif field_format == "普通文本" and len(extracted_value.strip()) > 0:
            confidence += 0.2
        elif field_format == "日期时间" and self._is_datetime(extracted_value):
            confidence += 0.3
        elif field_format == "表格行" and self._contains_structured_data(extracted_value):
            confidence += 0.2
        elif field_format == "段落" and len(extracted_value.split('\n')) > 1:
            confidence += 0.2
        
        # 基于长度的置信度调整
        if len(extracted_value) > 3:
            confidence += 0.1
        
        # 基于与示例的相似性
        if example_value and extracted_value:
            similarity = self._calculate_similarity(extracted_value, example_value)
            confidence += similarity * 0.2
        
        return min(confidence, 1.0)
    
    def _is_person_name(self, text: str) -> bool:
        """检查是否为人名格式"""
        # 简单的中文人名检测
        return len(text) >= 2 and len(text) <= 4 and all(ord(c) > 127 for c in text)
    
    def _is_datetime(self, text: str) -> bool:
        """检查是否为日期时间格式"""
        datetime_patterns = [
            r'\d{4}年\d{1,2}月\d{1,2}日',
            r'\d{4}-\d{1,2}-\d{1,2}',
            r'\d{1,2}:\d{2}',
            r'\d{4}/\d{1,2}/\d{1,2}'
        ]
        return any(re.search(pattern, text) for pattern in datetime_patterns)
    
    def _contains_structured_data(self, text: str) -> bool:
        """检查是否包含结构化数据"""
        # 检查是否包含常见的结构化数据标识
        structured_indicators = ['、', '，', '：', '；', '|', '\t']
        return any(indicator in text for indicator in structured_indicators)
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """计算两个文本的相似度"""
        if not text1 or not text2:
            return 0.0
        
        # 简单的字符级相似度计算
        set1 = set(text1)
        set2 = set(text2)
        intersection = len(set1 & set2)
        union = len(set1 | set2)
        
        return intersection / union if union > 0 else 0.0
    
    def _build_extraction_prompt(self, request: ExtractionRequest) -> str:
        """构建信息抽取的提示词"""
        prompt = f"""
请从以下文档内容中抽取指定字段的信息：

**字段名称**: {request.field_name}
**字段描述**: {request.field_description}
**字段示例**: {request.field_example}
**字段格式**: {request.field_format}

**文档内容**:
{request.source_text}

**抽取要求**:
1. 仔细阅读文档内容，找到与字段描述相匹配的信息
2. 参考字段示例的格式进行抽取
3. 如果找到相关信息，请直接返回抽取的内容
4. 如果没有找到相关信息，请返回"未找到"
5. 不要添加任何解释或额外的文字

**返回格式**: 直接返回抽取的字段值，不要包含其他内容
"""
        
        if request.context:
            prompt += f"\n**上下文信息**: {request.context}"
        
        return prompt.strip()
    
    async def validate_extracted_data(self, field_name: str, field_value: str, 
                                    field_format: str, original_text: str) -> LLMResponse:
        """
        验证抽取数据的准确性
        
        Args:
            field_name: 字段名称
            field_value: 抽取的字段值
            field_format: 字段格式要求
            original_text: 原始文档内容
            
        Returns:
            LLMResponse: 验证结果
        """
        prompt = f"""
请验证以下信息抽取结果的准确性：

**字段名称**: {field_name}
**抽取结果**: {field_value}
**格式要求**: {field_format}

**原始文档**:
{original_text}

**验证要求**:
1. 检查抽取结果是否与原始文档内容一致
2. 检查抽取结果是否符合格式要求
3. 给出验证结果和置信度评分（0-1之间）

**返回格式**:
{{
    "is_valid": true/false,
    "confidence": 0.95,
    "reason": "验证说明"
}}
"""
        
        messages = [
            {
                "role": "system",
                "content": "你是一个专业的数据验证助手，能够准确验证信息抽取结果的正确性。"
            },
            {
                "role": "user",
                "content": prompt
            }
        ]
        
        return await self.call_llm(messages)
    
    async def parse_validation_response(self, response: LLMResponse) -> ValidationResult:
        """
        解析验证响应
        
        Args:
            response: LLM响应
            
        Returns:
            ValidationResult: 解析后的验证结果
        """
        if not response.success:
            return ValidationResult(
                is_valid=False,
                confidence=0.0,
                reason=f"验证调用失败: {response.error}",
                suggestions=["请重新尝试验证"]
            )
        
        try:
            validation_data = json.loads(response.content)
            return ValidationResult(
                is_valid=validation_data.get("is_valid", False),
                confidence=float(validation_data.get("confidence", 0.0)),
                reason=validation_data.get("reason", "无验证说明"),
                suggestions=validation_data.get("suggestions", [])
            )
        except json.JSONDecodeError:
            # 如果不是JSON格式，尝试文本解析
            content = response.content.lower()
            is_valid = "有效" in content or "正确" in content or "准确" in content
            confidence = 0.7 if is_valid else 0.3
            
            return ValidationResult(
                is_valid=is_valid,
                confidence=confidence,
                reason=response.content,
                suggestions=[]
            )
    
    async def generate_report_content(self, template_content: str, 
                                    extracted_data: Dict[str, str]) -> LLMResponse:
        """
        生成报告内容
        
        Args:
            template_content: 模板内容
            extracted_data: 抽取的数据
            
        Returns:
            LLMResponse: 生成的报告内容
        """
        prompt = f"""
请根据以下模板和数据生成完整的报告内容：

**模板内容**:
{template_content}

**抽取数据**:
{json.dumps(extracted_data, ensure_ascii=False, indent=2)}

**生成要求**:
1. 将数据准确填入模板的相应位置
2. 保持模板的原始格式和结构
3. 对于缺失的数据，使用合理的默认值或留空
4. 确保生成的内容逻辑连贯、语言流畅
5. 返回完整的报告内容

**返回格式**: 直接返回生成的报告内容，不要包含其他说明
"""
        
        messages = [
            {
                "role": "system",
                "content": "你是一个专业的报告生成助手，能够根据模板和数据生成高质量的报告内容。"
            },
            {
                "role": "user",
                "content": prompt
            }
        ]
        
        return await self.call_llm(messages)
    
    async def improve_extraction_quality(self, original_text: str, 
                                       extraction_results: List[ExtractionResult]) -> List[ExtractionResult]:
        """
        改进抽取质量
        
        Args:
            original_text: 原始文本
            extraction_results: 原始抽取结果
            
        Returns:
            List[ExtractionResult]: 改进后的抽取结果
        """
        improved_results = []
        
        for result in extraction_results:
            if result.confidence < 0.6:  # 低置信度需要改进
                try:
                    # 使用改进的提示词重新抽取
                    improved_request = ExtractionRequest(
                        field_name=result.field_name,
                        field_description=f"请仔细从文档中寻找 {result.field_name} 相关的信息",
                        field_example="",
                        field_format="普通文本",
                        source_text=original_text,
                        context=f"之前的抽取结果可能不够准确: {result.field_value}"
                    )
                    
                    response = await self.extract_field_info(improved_request)
                    
                    if response.success and response.content.strip() != "未找到":
                        # 使用改进的结果
                        improved_result = ExtractionResult(
                            field_name=result.field_name,
                            field_value=response.content.strip(),
                            confidence=min(result.confidence + 0.2, 0.9),
                            extraction_method="improved_extraction",
                            is_valid=True
                        )
                        improved_results.append(improved_result)
                    else:
                        improved_results.append(result)
                        
                except Exception as e:
                    logger.error(f"改进抽取失败 {result.field_name}: {e}")
                    improved_results.append(result)
            else:
                improved_results.append(result)
        
        return improved_results
    
    async def analyze_document_structure(self, document_content: str) -> Dict[str, Any]:
        """
        分析文档结构
        
        Args:
            document_content: 文档内容
            
        Returns:
            Dict[str, Any]: 文档结构分析结果
        """
        prompt = f"""
请分析以下文档的结构和内容类型：

**文档内容**:
{document_content[:2000]}...

**分析要求**:
1. 识别文档类型（会议纪要、项目报告、通知等）
2. 识别文档的主要结构部分
3. 找出关键信息位置
4. 评估信息的完整性

**返回格式**:
{{
    "document_type": "文档类型",
    "structure_parts": ["结构部分1", "结构部分2"],
    "key_information": ["关键信息1", "关键信息2"],
    "completeness_score": 0.85
}}
"""
        
        messages = [
            {
                "role": "system",
                "content": "你是一个专业的文档分析师，能够准确分析各种文档的结构和内容。"
            },
            {
                "role": "user",
                "content": prompt
            }
        ]
        
        response = await self.call_llm(messages)
        
        if response.success:
            try:
                return json.loads(response.content)
            except json.JSONDecodeError:
                logger.warning("文档结构分析结果解析失败，使用默认结果")
                return {
                    "document_type": "未知类型",
                    "structure_parts": ["内容主体"],
                    "key_information": ["需要人工确认"],
                    "completeness_score": 0.5
                }
        else:
            return {
                "document_type": "分析失败",
                "structure_parts": [],
                "key_information": [],
                "completeness_score": 0.0
            }


# 全局LLM服务实例
llm_service = LLMService()
