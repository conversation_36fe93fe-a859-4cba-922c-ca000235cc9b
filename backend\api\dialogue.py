"""
对话管理API
提供对话创建、检索、更新和人工干预的REST接口
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime

from fastapi import APIRouter, HTTPException, Query, Body
from pydantic import BaseModel, Field

from services.dialogue_service import dialogue_service
from agents.dialogue_agent import base_dialogue_agent
from models.dialogue import MessageRole, ConversationStatus, InteractionType

# 配置日志
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/dialogue", tags=["对话管理"])


# Pydantic模型定义
class CreateConversationRequest(BaseModel):
    """创建对话请求"""
    user_id: Optional[str] = None
    title: Optional[str] = None
    interaction_type: str = Field(default=InteractionType.CHAT.value)
    context_data: Optional[Dict[str, Any]] = None


class SendMessageRequest(BaseModel):
    """发送消息请求"""
    message: str = Field(..., description="消息内容")
    require_human_approval: bool = Field(default=False, description="是否需要人工审批")


class ProcessDocumentRequest(BaseModel):
    """处理文档请求"""
    file_path: str = Field(..., description="文档文件路径")
    extraction_fields: List[Dict[str, str]] = Field(..., description="提取字段定义")


class HumanFeedbackRequest(BaseModel):
    """人工反馈请求"""
    feedback_content: str = Field(..., description="反馈内容")
    feedback_type: str = Field(default="approval", description="反馈类型")
    message_id: Optional[str] = None


class ConversationResponse(BaseModel):
    """对话响应"""
    id: str
    thread_id: str
    title: str
    user_id: Optional[str]
    status: str
    interaction_type: str
    message_count: int
    last_activity: datetime
    created_at: datetime
    metadata: Dict[str, Any]
    context_data: Dict[str, Any]


class MessageResponse(BaseModel):
    """消息响应"""
    id: str
    role: str
    content: str
    content_type: str
    sequence_number: int
    metadata: Dict[str, Any]
    created_at: datetime
    is_edited: bool


@router.post("/conversations", response_model=ConversationResponse)
async def create_conversation(request: CreateConversationRequest):
    """
    创建新的对话会话
    """
    try:
        # Create a new conversation using the chat interface
        result = await base_dialogue_agent.chat(
            user_message="开始新对话",
            user_id=request.user_id,
            context_data=request.context_data
        )

        # For now, return a mock conversation response
        # In a real implementation, you'd want to store conversation metadata
        conversation_data = {
            "id": result.get("thread_id", ""),
            "thread_id": result.get("thread_id", ""),
            "title": request.title or "新对话",
            "user_id": request.user_id,
            "status": "active",
            "interaction_type": request.interaction_type,
            "message_count": 1,
            "last_activity": datetime.now(),
            "created_at": datetime.now(),
            "metadata": {},
            "context_data": request.context_data or {}
        }
        
        return ConversationResponse(**conversation_data)
        
    except Exception as e:
        logger.error(f"创建对话失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建对话失败: {str(e)}")


@router.get("/conversations", response_model=List[ConversationResponse])
async def list_conversations(
    user_id: Optional[str] = Query(None, description="用户ID过滤"),
    status: Optional[str] = Query(None, description="状态过滤"),
    interaction_type: Optional[str] = Query(None, description="交互类型过滤"),
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="分页偏移量")
):
    """
    获取对话列表
    """
    try:
        conversations = dialogue_service.list_conversations(
            user_id=user_id,
            status=status,
            interaction_type=interaction_type,
            limit=limit,
            offset=offset
        )
        
        return [
            ConversationResponse(
                id=str(conv.id),
                thread_id=conv.thread_id,  # type: ignore
                title=conv.title,  # type: ignore
                user_id=conv.user_id,  # type: ignore
                status=conv.status,  # type: ignore
                interaction_type=conv.interaction_type,  # type: ignore
                message_count=conv.message_count,  # type: ignore
                last_activity=conv.last_activity,  # type: ignore
                created_at=conv.created_at,  # type: ignore
                metadata=conv.metadata or {},  # type: ignore
                context_data=conv.context_data or {}  # type: ignore
            )
            for conv in conversations
        ]
        
    except Exception as e:
        logger.error(f"获取对话列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取对话列表失败: {str(e)}")


@router.get("/conversations/{conversation_id}", response_model=ConversationResponse)
async def get_conversation(conversation_id: str):
    """
    获取特定对话详情
    """
    try:
        conversation = dialogue_service.get_conversation(conversation_id)
        
        if not conversation:
            raise HTTPException(status_code=404, detail="对话不存在")
        
        return ConversationResponse(
            id=str(conversation.id),
            thread_id=conversation.thread_id,  # type: ignore
            title=conversation.title,  # type: ignore
            user_id=conversation.user_id,  # type: ignore
            status=conversation.status,  # type: ignore
            interaction_type=conversation.interaction_type,  # type: ignore
            message_count=conversation.message_count,  # type: ignore
            last_activity=conversation.last_activity,  # type: ignore
            created_at=conversation.created_at,  # type: ignore
            metadata=conversation.metadata or {},  # type: ignore
            context_data=conversation.context_data or {}  # type: ignore
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取对话详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取对话详情失败: {str(e)}")


@router.get("/conversations/{conversation_id}/messages", response_model=List[MessageResponse])
async def get_conversation_messages(
    conversation_id: str,
    limit: Optional[int] = Query(None, ge=1, le=200, description="返回数量限制"),
    before_sequence: Optional[int] = Query(None, description="获取序号小于此值的消息"),
    after_sequence: Optional[int] = Query(None, description="获取序号大于此值的消息")
):
    """
    获取对话消息历史
    """
    try:
        # 验证对话是否存在
        conversation = dialogue_service.get_conversation(conversation_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="对话不存在")
        
        messages = dialogue_service.get_messages(
            conversation_id=conversation_id,
            limit=limit,
            before_sequence=before_sequence,
            after_sequence=after_sequence
        )
        
        return [
            MessageResponse(
                id=str(msg.id),
                role=msg.role,  # type: ignore
                content=msg.content,  # type: ignore
                content_type=msg.content_type,  # type: ignore
                sequence_number=msg.sequence_number,  # type: ignore
                metadata=msg.metadata or {},  # type: ignore
                created_at=msg.created_at,  # type: ignore
                is_edited=msg.is_edited  # type: ignore
            )
            for msg in messages
        ]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取消息历史失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取消息历史失败: {str(e)}")


@router.post("/conversations/{conversation_id}/messages")
async def send_message(conversation_id: str, request: SendMessageRequest):
    """
    向对话发送消息
    """
    try:
        # 使用base_dialogue_agent处理消息
        result = await base_dialogue_agent.chat(
            user_message=request.message,
            thread_id=conversation_id,
            require_human_approval=request.require_human_approval
        )
        
        if result["success"]:
            return {
                "success": True,
                "response": result["response"],
                "thread_id": result["thread_id"],
                "status": result["status"],
                "requires_human_approval": result.get("requires_human_approval", False),
                "agent_status": base_dialogue_agent.get_agent_status(result["thread_id"])
            }
        else:
            raise HTTPException(status_code=500, detail=result.get("error", "处理消息失败"))
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"发送消息失败: {e}")
        raise HTTPException(status_code=500, detail=f"发送消息失败: {str(e)}")


@router.post("/conversations/{conversation_id}/process-document")
async def process_document(conversation_id: str, request: ProcessDocumentRequest):
    """
    在对话中处理文档
    """
    try:
        # 验证对话是否存在
        conversation = dialogue_service.get_conversation_by_thread_id(conversation_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="对话不存在")

        # 处理文档 - 使用base_dialogue_agent
        result = await base_dialogue_agent.chat(
            user_message=f"请处理文档: {request.file_path}",
            thread_id=conversation_id,
            context_data={"file_path": request.file_path, "extraction_fields": request.extraction_fields}
        )

        return {
            "success": True,
            "result": result,
            "agent_status": base_dialogue_agent.get_agent_status(conversation_id)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理文档失败: {e}")
        raise HTTPException(status_code=500, detail=f"处理文档失败: {str(e)}")


@router.post("/conversations/{conversation_id}/human-feedback")
async def submit_human_feedback(conversation_id: str, request: HumanFeedbackRequest):
    """
    提交人工反馈
    """
    try:
        # 验证对话是否存在
        conversation = dialogue_service.get_conversation(conversation_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="对话不存在")
        
        # 处理人工反馈
        result = await base_dialogue_agent.provide_human_feedback(
            thread_id=conversation_id,
            feedback_content=request.feedback_content,
            approved=(request.feedback_type == "approval"),
            feedback_metadata={"message_id": request.message_id}
        )

        if not result["success"]:
            raise HTTPException(status_code=500, detail=result.get("error", "处理人工反馈失败"))

        return {
            "success": True,
            "message": "人工反馈已处理",
            "agent_status": base_dialogue_agent.get_agent_status(conversation_id)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"提交人工反馈失败: {e}")
        raise HTTPException(status_code=500, detail=f"提交人工反馈失败: {str(e)}")


@router.post("/conversations/{conversation_id}/request-intervention")
async def request_human_intervention(
    conversation_id: str,
    message: str = Body(..., description="干预请求消息"),
    intervention_type: str = Body(default="approval", description="干预类型"),
    context_data: Optional[Dict[str, Any]] = Body(default=None, description="上下文数据")
):
    """
    请求人工干预
    """
    try:
        # 验证对话是否存在
        conversation = dialogue_service.get_conversation(conversation_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="对话不存在")
        
        # 请求人工干预 - 使用base_dialogue_agent
        intervention_context = context_data or {}
        intervention_context["intervention_type"] = intervention_type

        result = await base_dialogue_agent.chat(
            user_message=message,
            thread_id=conversation_id,
            require_human_approval=True,
            context_data=intervention_context
        )

        if not result.get("success", False):
            raise HTTPException(status_code=500, detail="请求人工干预失败")

        return {
            "success": True,
            "message": "人工干预请求已提交",
            "agent_status": base_dialogue_agent.get_agent_status(conversation_id)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"请求人工干预失败: {e}")
        raise HTTPException(status_code=500, detail=f"请求人工干预失败: {str(e)}")


@router.put("/conversations/{conversation_id}")
async def update_conversation(
    conversation_id: str,
    title: Optional[str] = Body(None, description="新标题"),
    status: Optional[str] = Body(None, description="新状态"),
    metadata: Optional[Dict[str, Any]] = Body(None, description="元数据更新"),
    context_data: Optional[Dict[str, Any]] = Body(None, description="上下文数据更新")
):
    """
    更新对话信息
    """
    try:
        conversation = dialogue_service.update_conversation(
            conversation_id=conversation_id,
            title=title,
            status=status,
            metadata=metadata,
            context_data=context_data
        )
        
        if not conversation:
            raise HTTPException(status_code=404, detail="对话不存在")
        
        return {
            "success": True,
            "message": "对话已更新",
            "conversation": ConversationResponse(
                id=str(conversation.id),
                thread_id=conversation.thread_id,  # type: ignore
                title=conversation.title,  # type: ignore
                user_id=conversation.user_id,  # type: ignore
                status=conversation.status,  # type: ignore
                interaction_type=conversation.interaction_type,  # type: ignore
                message_count=conversation.message_count,  # type: ignore
                last_activity=conversation.last_activity,  # type: ignore
                created_at=conversation.created_at,  # type: ignore
                metadata=conversation.metadata or {},  # type: ignore
                context_data=conversation.context_data or {}  # type: ignore
            )
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新对话失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新对话失败: {str(e)}")


@router.get("/agent/status")
async def get_agent_status(thread_id: Optional[str] = Query(None, description="线程ID")):
    """
    获取智能体状态
    """
    try:
        return base_dialogue_agent.get_agent_status(thread_id)
    except Exception as e:
        logger.error(f"获取智能体状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取智能体状态失败: {str(e)}")


# ==================== 对话历史管理 API ====================

@router.get("/conversations/{conversation_id}/history")
async def get_conversation_history(
    conversation_id: str,
    limit: Optional[int] = Query(50, ge=1, le=200, description="返回消息数量限制")
):
    """
    获取对话历史记录
    """
    try:
        # 使用BaseDialogueAgent获取线程历史
        messages = base_dialogue_agent.get_thread_history(conversation_id)

        # 转换为API响应格式
        history = []
        for i, msg in enumerate(messages[-limit:] if limit else messages):
            history.append({
                "id": f"{conversation_id}_{i}",
                "role": msg.type if hasattr(msg, 'type') else "unknown",
                "content": msg.content if hasattr(msg, 'content') else str(msg),
                "timestamp": datetime.now().isoformat(),
                "sequence_number": i + 1
            })

        return {
            "success": True,
            "conversation_id": conversation_id,
            "message_count": len(history),
            "messages": history
        }

    except Exception as e:
        logger.error(f"获取对话历史失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取对话历史失败: {str(e)}")


@router.delete("/conversations/{conversation_id}")
async def delete_conversation(conversation_id: str):
    """
    删除对话及其所有历史记录
    """
    try:
        # 删除数据库中的对话记录
        conversation = dialogue_service.get_conversation_by_thread_id(conversation_id)
        if conversation:
            # 删除对话及其相关数据（级联删除）
            success = dialogue_service.delete_conversation(str(conversation.id))
            if success:
                # 同时清理LangGraph检查点数据
                base_dialogue_agent.checkpointer.delete_thread(conversation_id)

                return {
                    "success": True,
                    "message": f"对话 {conversation_id} 已成功删除",
                    "conversation_id": conversation_id
                }
            else:
                raise HTTPException(status_code=500, detail="删除对话失败")
        else:
            # 即使数据库中没有记录，也尝试清理LangGraph数据
            base_dialogue_agent.checkpointer.delete_thread(conversation_id)

            return {
                "success": True,
                "message": f"对话 {conversation_id} 已清理",
                "conversation_id": conversation_id,
                "note": "数据库中未找到对话记录，但已清理相关缓存数据"
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除对话失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除对话失败: {str(e)}")


@router.get("/conversations/{conversation_id}/memory")
async def get_conversation_memory(
    conversation_id: str,
    user_id: Optional[str] = Query(None, description="用户ID")
):
    """
    获取对话的长期记忆
    """
    try:
        if not user_id:
            return {
                "success": False,
                "error": "需要提供user_id来获取长期记忆"
            }

        # 获取长期记忆
        memories = base_dialogue_agent.get_long_term_memory(user_id, limit=20)

        return {
            "success": True,
            "conversation_id": conversation_id,
            "user_id": user_id,
            "memory_count": len(memories),
            "memories": memories
        }

    except Exception as e:
        logger.error(f"获取对话记忆失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取对话记忆失败: {str(e)}")


@router.post("/conversations/{conversation_id}/clear-history")
async def clear_conversation_history(conversation_id: str):
    """
    清空对话历史记录（保留对话但清除消息）
    """
    try:
        # 清理数据库中的消息和检查点
        conversation = dialogue_service.get_conversation_by_thread_id(conversation_id)
        if conversation:
            # 删除所有消息
            dialogue_service.clear_conversation_messages(str(conversation.id))

            # 删除所有检查点
            dialogue_service.clear_conversation_checkpoints(str(conversation.id))

            # 清理LangGraph检查点数据
            base_dialogue_agent.checkpointer.delete_thread(conversation_id)

            return {
                "success": True,
                "message": "对话历史已成功清空",
                "conversation_id": conversation_id
            }
        else:
            return {
                "success": False,
                "message": "对话不存在",
                "conversation_id": conversation_id
            }

    except Exception as e:
        logger.error(f"清空对话历史失败: {e}")
        raise HTTPException(status_code=500, detail=f"清空对话历史失败: {str(e)}")


@router.get("/users/{user_id}/conversations")
async def get_user_conversations(
    user_id: str,
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="分页偏移量")
):
    """
    获取用户的所有对话列表
    """
    try:
        # 从数据库获取用户的对话列表
        conversations = dialogue_service.list_conversations(
            user_id=user_id,
            limit=limit,
            offset=offset
        )

        conversation_list = [
            {
                "id": str(conv.id),
                "thread_id": conv.thread_id,
                "title": conv.title,
                "user_id": conv.user_id,
                "status": conv.status,
                "interaction_type": conv.interaction_type,
                "message_count": conv.message_count,
                "created_at": conv.created_at.isoformat(),
                "last_activity": conv.last_activity.isoformat(),
                "metadata": conv.metadata or {}
            }
            for conv in conversations
        ]

        return {
            "success": True,
            "user_id": user_id,
            "total_conversations": len(conversation_list),
            "conversations": conversation_list,
            "pagination": {
                "limit": limit,
                "offset": offset,
                "has_more": len(conversations) == limit  # 简化的判断
            }
        }

    except Exception as e:
        logger.error(f"获取用户对话列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取用户对话列表失败: {str(e)}")


# ==================== 消息管理 API ====================

@router.put("/conversations/{conversation_id}/messages/{message_id}")
async def update_message(
    conversation_id: str,
    message_id: str,
    content: str = Body(..., description="新的消息内容"),
    metadata: Optional[Dict[str, Any]] = Body(None, description="消息元数据")
):
    """
    更新消息内容
    """
    try:
        # 验证对话是否存在
        conversation = dialogue_service.get_conversation_by_thread_id(conversation_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="对话不存在")

        # 更新消息
        success = dialogue_service.update_message(
            message_id=message_id,
            content=content,
            metadata=metadata
        )

        if not success:
            raise HTTPException(status_code=404, detail="消息不存在或更新失败")

        return {
            "success": True,
            "message": "消息已更新",
            "message_id": message_id,
            "conversation_id": conversation_id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新消息失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新消息失败: {str(e)}")


@router.delete("/conversations/{conversation_id}/messages/{message_id}")
async def delete_message(conversation_id: str, message_id: str):
    """
    删除特定消息
    """
    try:
        # 验证对话是否存在
        conversation = dialogue_service.get_conversation_by_thread_id(conversation_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="对话不存在")

        # 删除消息（软删除）
        success = dialogue_service.delete_message(message_id)

        if not success:
            raise HTTPException(status_code=404, detail="消息不存在或删除失败")

        return {
            "success": True,
            "message": "消息已删除",
            "message_id": message_id,
            "conversation_id": conversation_id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除消息失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除消息失败: {str(e)}")


@router.get("/conversations/{conversation_id}/messages/{message_id}")
async def get_message(conversation_id: str, message_id: str):
    """
    获取特定消息详情
    """
    try:
        # 验证对话是否存在
        conversation = dialogue_service.get_conversation_by_thread_id(conversation_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="对话不存在")

        # 获取消息
        message = dialogue_service.get_message(message_id)

        if not message:
            raise HTTPException(status_code=404, detail="消息不存在")

        return MessageResponse(
            id=str(message.id),
            role=message.role,  # type: ignore
            content=message.content,  # type: ignore
            content_type=message.content_type,  # type: ignore
            sequence_number=message.sequence_number,  # type: ignore
            metadata=message.metadata or {},  # type: ignore
            created_at=message.created_at,  # type: ignore
            is_edited=message.is_edited  # type: ignore
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取消息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取消息失败: {str(e)}")
