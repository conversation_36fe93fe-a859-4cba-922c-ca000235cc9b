"""
基础工具类
定义智能体工具的通用接口和基础功能
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class BaseTool(ABC):
    """基础工具抽象类"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(f"tool.{name}")
    
    @abstractmethod
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行工具逻辑
        
        Args:
            input_data: 输入数据
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        pass
    
    def log_info(self, message: str):
        """记录信息日志"""
        self.logger.info(f"[{self.name}] {message}")
    
    def log_error(self, message: str):
        """记录错误日志"""
        self.logger.error(f"[{self.name}] {message}")
    
    def log_warning(self, message: str):
        """记录警告日志"""
        self.logger.warning(f"[{self.name}] {message}")
    
    def validate_input(self, input_data: Dict[str, Any], required_fields: list) -> bool:
        """验证输入数据"""
        for field in required_fields:
            if field not in input_data:
                self.log_error(f"缺少必需字段: {field}")
                return False
        return True
    
    def create_result(self, success: bool, data: Dict[str, Any] = None, 
                     error: str = None, message: str = "") -> Dict[str, Any]:
        """创建标准化的结果格式"""
        result = {
            "success": success,
            "tool_name": self.name,
            "timestamp": datetime.now().isoformat(),
            "message": message
        }
        
        if success and data:
            result["data"] = data
        elif not success and error:
            result["error"] = error
            
        return result


class ToolExecutionError(Exception):
    """工具执行异常"""
    
    def __init__(self, tool_name: str, message: str, details: Dict[str, Any] = None):
        self.tool_name = tool_name
        self.message = message
        self.details = details or {}
        super().__init__(f"[{tool_name}] {message}")


class ToolResult:
    """工具执行结果"""
    
    def __init__(self, success: bool, data: Dict[str, Any] = None, 
                 error: str = None, tool_name: str = ""):
        self.success = success
        self.data = data or {}
        self.error = error
        self.tool_name = tool_name
        self.timestamp = datetime.now()
    
    def __bool__(self):
        return self.success
    
    def __str__(self):
        return f"ToolResult(tool={self.tool_name}, success={self.success})"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            "success": self.success,
            "tool_name": self.tool_name,
            "timestamp": self.timestamp.isoformat()
        }
        
        if self.success:
            result["data"] = self.data
        else:
            result["error"] = self.error
            
        return result
