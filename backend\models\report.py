"""
报告相关数据模型
"""

from sqlalchemy import Column, String, Integer, ForeignKey, Numeric, DateTime
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from .core import BaseModel


class Report(BaseModel):
    """报告表"""

    __tablename__ = "reports"

    name = Column(String(255), nullable=False, comment="报告名称")
    template_id = Column(UUID(as_uuid=True), ForeignKey("templates.id"), nullable=False, comment="模板ID")
    data_source_id = Column(UUID(as_uuid=True), ForeignKey("data_sources.id"), nullable=True, comment="数据源ID")
    session_id = Column(UUID(as_uuid=True), ForeignKey("agent_sessions.id"), nullable=True, comment="会话ID")
    file_path = Column(String(500), nullable=True, comment="生成的报告文件路径")
    file_size = Column(Integer, nullable=True, comment="文件大小(字节)")
    status = Column(String(50), default="draft", comment="报告状态")
    generation_method = Column(String(50), default="ai", comment="生成方法")
    quality_score = Column(Numeric(3, 2), nullable=True, comment="质量评分")
    generation_time = Column(Integer, nullable=True, comment="生成耗时(秒)")
    download_count = Column(Integer, default=0, comment="下载次数")
    report_metadata = Column(JSONB, nullable=True, comment="报告元数据")

    # 关联关系
    template = relationship("Template", back_populates="reports")
    data_source = relationship("DataSource", back_populates="reports")
    session = relationship("AgentSession", back_populates="reports")
    generation_task = relationship("GenerationTask", back_populates="report", uselist=False)
    quality_checks = relationship("QualityCheck", back_populates="report", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Report(id={self.id}, name='{self.name}', status='{self.status}')>"


class GenerationTask(BaseModel):
    """生成任务表"""

    __tablename__ = "generation_tasks"

    session_id = Column(UUID(as_uuid=True), ForeignKey("agent_sessions.id"), nullable=True, comment="会话ID")
    report_id = Column(UUID(as_uuid=True), ForeignKey("reports.id"), nullable=True, comment="关联报告")
    task_type = Column(String(50), nullable=False, comment="任务类型")
    status = Column(String(50), default="pending", comment="任务状态")
    progress = Column(Integer, default=0, comment="进度百分比")
    started_at = Column(DateTime, nullable=True, comment="开始时间")
    completed_at = Column(DateTime, nullable=True, comment="完成时间")
    error_message = Column(String, nullable=True, comment="错误信息")
    result_data = Column(JSONB, nullable=True, comment="结果数据")

    # 关联关系
    session = relationship("AgentSession", back_populates="generation_tasks")
    report = relationship("Report", back_populates="generation_task")

    def __repr__(self):
        return f"<GenerationTask(id={self.id}, report_id={self.report_id}, status='{self.status}')>"


class QualityCheck(BaseModel):
    """质量检查表"""

    __tablename__ = "quality_checks"

    report_id = Column(UUID(as_uuid=True), ForeignKey("reports.id"), nullable=False, comment="关联报告")
    check_type = Column(String(50), nullable=False, comment="检查类型")
    check_result = Column(String(20), nullable=False, comment="检查结果")
    issues_found = Column(Integer, default=0, comment="发现问题数量")
    issues_detail = Column(JSONB, nullable=True, comment="问题详情")
    suggestions = Column(String, nullable=True, comment="改进建议")

    # 关联关系
    report = relationship("Report", back_populates="quality_checks")

    def __repr__(self):
        return f"<QualityCheck(id={self.id}, report_id={self.report_id}, type='{self.check_type}')>"


class ReviewRecord(BaseModel):
    """审核记录表"""

    __tablename__ = "review_records"

    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, comment="审核用户")
    target_type = Column(String(50), nullable=False, comment="审核对象类型")
    target_id = Column(UUID(as_uuid=True), nullable=False, comment="审核对象ID")
    action = Column(String(50), nullable=False, comment="审核动作")
    old_value = Column(String, nullable=True, comment="原始值")
    new_value = Column(String, nullable=True, comment="修改后值")
    comment = Column(String, nullable=True, comment="审核意见")

    # 关联关系
    # user = relationship("User", back_populates="review_records")  # 暂时注释掉

    def __repr__(self):
        return f"<ReviewRecord(id={self.id}, user_id={self.user_id}, action='{self.action}')>"