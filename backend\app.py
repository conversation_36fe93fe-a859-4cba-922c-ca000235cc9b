from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from api.upload import router as upload_router
from api.workflow import router as workflow_router
from api.dialogue import router as dialogue_router
import os

# 加载环境变量
if os.path.exists(".env"):
    from dotenv import load_dotenv
    load_dotenv()

# 创建FastAPI应用实例
app = FastAPI(
    title="Multi-Agent System API",
    version="1.0.0",
    description="多智能体系统API",
    debug=True,
)

# 配置CORS
cors_origins = [
    "http://localhost:3000",
    "http://localhost:5173",
    "http://127.0.0.1:3000",
    "http://127.0.0.1:5173"
]
print(f"配置CORS允许的源: {cors_origins}")

app.add_middleware(
    CORSMiddleware,
    allow_origins=cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册简化的路由
app.include_router(upload_router)
app.include_router(workflow_router)
app.include_router(dialogue_router)

# 健康检查接口
@app.get("/")
def read_root():
    return {
        "message": "Multi-Agent System API服务",
        "version": "1.0.0",
        "status": "running"
    }

@app.get("/health")
def health_check():
    return {
        "status": "healthy",
        "environment": "development",
        "version": "1.0.0"
    }

# 启动事件
@app.on_event("startup")
async def startup_event():
    print("🚀 Multi-Agent System API v1.0.0 启动成功!")
    print("📝 环境: development")
    print("🔧 调试模式: True")
    print("✅ 服务启动完成")

# 关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    print("👋 应用正在关闭...")

if __name__ == "__main__":
    import uvicorn
    print("Starting server on http://0.0.0.0:8000")
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )