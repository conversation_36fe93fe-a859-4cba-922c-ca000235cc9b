# 项目架构设计（0620版）

## 1. 项目基础结构

```
report_gen/
├── backend/                    # 后端主目录
│   ├── __init__.py
│   ├── app.py                 # FastAPI应用入口
│   ├── config.py              # 配置文件
│   ├── extensions.py          # 扩展初始化
│   ├── dependencies.py        # 依赖项定义
│   ├── models/                # 数据模型
│   │   ├── core.py            # 核心模型
│   │   ├── template.py        # 模板模型
│   │   ├── data.py            # 数据模型
│   │   └── report.py          # 报告模型
│   ├── routers/               # 路由处理
│   │   ├── uploading.py       # 上传相关接口
│   │   ├── extraction.py      # 信息抽取相关接口
│   │   ├── reportgen.py       # 报告生成相关接口
│   ├── schemas/               # 请求与响应数据结构
│   │   ├── core.py            # 请求响应
│   ├── api/                   # API接口
│   │   ├── agent/             # 智能体定义
│   │   │   ├──base.py         # 智能体基类
│   │   │   ├──toolcall.py     # toolcall agent
│   │   │   ├──react.py        # react agent
│   │   │   ├──langgraph.py    # langraph agent
│   │   ├── client/            # 大模型客户端
│   │   │   ├──qwen.py         # 千问客户端定义
│   │   ├── mcp/               # mcp server
│   │   │   ├──server.py       # 服务定义
│   │   ├── prompt/            # 提示词管理
│   │   │   ├──extract.py      # 提取提示词
│   │   │   ├──synthetic.py    # 合成提示词
│   │   │   ├──prompthelper.py # 提示词助手
│   │   │   ├──func_intro.py   # 功能介绍
│   │   ├── tool/              # 智能体工具
│   │   │   ├──para_split.py   # 长文本切片
│   │   │   ├──keys_extract.py # 关键信息提取
│   │   │   ├──keys_insert.py  # 关键信息合成
│   │   │   ├──keys_modify.py  # 信息修改
│   │   │   ├──keys_exam.py    # 信息检查
│   │   │   ├──keys_search.py  # 信息检索
│   │   │   ├──doc_search.py   # 文档查询
│   │   │   ├──doc_exam.py     # 文档质量检查
│   │   ├── main.py            # 智能体服务入口
│   │   ├── llm.py             # 大模型链接定义
│   │   ├── embedder.py        # 嵌入模型定义
│   ├── tasks/                 # 后台任务
│   │   ├── template.py        # 模板处理任务
│   │   ├── extract.py         # 信息抽取任务
│   │   └── report.py          # 报告生成任务
│   ├── commands/              # CLI命令
│   ├── dao/                   # 数据访问对象
│   ├── common/                # 通用功能
│   ├── db_engine_specs/       # 数据库引擎
│   └── utils/                 # 工具函数
│       ├── core.py            # 核心工具
│       ├── docx.py            # Word文档处理
│       ├── extract.py         # 信息抽取
│       └── ai.py              # AI模型集成
│   ├── tests/                 # 测试目录
│   │   ├── unit/              # 单元测试
│   │   ├── integration/       # 集成测试
│   │   └── e2e/               # 端到端测试
│   └── requirements/          # 依赖管理
│       ├── base.txt           # 基础依赖
│       ├── dev.txt            # 开发环境依赖
│       └── prod.txt           # 生产环境依赖
├── frontend/                  # 前端主目录
│   ├── src/
│   │   ├── components/        # 组件
│   │   │   ├── common/        # 通用组件
│   │   │   ├── template/      # 模板组件
│   │   │   ├── data/          # 数据组件
│   │   │   └── report/        # 报告组件
│   │   ├── layouts/           # 布局组件
│   │   ├── pages/             # 页面
│   │   ├── services/          # API服务
│   │   ├── models/            # 数据模型
│   │   ├── hooks/             # 自定义Hooks
│   │   ├── utils/             # 工具函数
│   │   └── assets/            # 静态资源
│   ├── public/                # 公共资源
│   └── config/                # 配置文件
├── docker/                    # Docker配置
│   ├── backend/
│   │   └── Dockerfile
│   ├── frontend/
│   │   └── Dockerfile
│   └── docker-compose.yml
├── docs/                      # 项目文档
│   ├── api/                   # API文档
│   ├── deployment/            # 部署文档
│   └── development/           # 开发文档
└── scripts/                   # 部署和维护脚本
    ├── setup/                 # 环境配置脚本
    └── deploy/                # 部署脚本
```

## 2. 技术栈说明

### 2.1 后端技术栈

* Python 3.12
* FastAPI 0.110+
* SQLAlchemy 2.0+
* Alembic (数据库迁移)
* Celery 5.3+ (任务队列)
* Redis 7.0+ (缓存和消息队列)
* PostgreSQL 15+ (主数据库)
* OpenAPI/Swagger (API文档)
* pytest (测试框架)
* LangGraph 0.1+ (AI框架)
* Qwen-32B (大语言模型)
* python-docx (Word文档处理)
* Pydantic (数据校验与序列化)
* 依赖注入与中间件（FastAPI原生）
* 智能体与工具链（自定义agent、tool、prompt、client等模块）
* bge-large-zh-v1.5（嵌入模型，硅基流动接口）

### 2.2 前端技术栈

* React 18+
* TypeScript 5+
* Ant Design Pro 5+
* Redux Toolkit (状态管理)
* React Query (数据获取)
* TailwindCSS 3+ (样式)
* Vite 5+ (构建工具)
* Jest (测试框架)
* ECharts 5+ (可视化)

## 3. 核心功能模块

### 3.1 模板管理模块
- 模板CRUD（routers/uploading.py、models/template.py、schemas/core.py）
- 模板支持多种生成方式：AI自动识别、人工编辑上传、历史文档导入
- 模板采用结构化占位符（如：{{字段名}}），并配套JSON Schema描述字段属性（名称、类型、是否重复、示例等）
- 可视化模板标注工具（规划中）
- 占位符管理（api/prompt/、api/tool/）

### 3.2 数据管理模块
- 数据源管理
- 数据抽取（routers/extraction.py、api/tool/、api/prompt/）
- 数据验证
- 数据预览

### 3.3 报告生成模块
- 报告模板选择
- 数据填充
- 报告预览
- 报告导出（仅支持Word格式）
- 报告编写过程基于原有模板增添、修改内容，保持Word排版不变，最终供用户下载（routers/reportgen.py、utils/docx.py、api/agent/、api/tool/）

### 3.4 AI功能模块
- 大语言模型服务（Qwen-32B，api/client/qwen.py、api/llm.py、api/agent/langgraph.py）
  - 模板解析与理解
  - 自然语言交互
  - 报告智能生成
  - 内容优化建议
- 信息抽取服务（api/tool/、api/prompt/、api/agent/）
  - 关键信息识别
  - 数据结构化处理
  - 模板匹配
  - 数据验证
- AI模型管理
  - 模型版本控制
  - 模型性能监控
  - 训练数据管理
  - 效果评估

## 4. 系统服务

### 4.1 基础服务
- 文件服务（routers/uploading.py、utils/docx.py）
- 缓存服务

### 4.2 业务服务
- 模板解析服务（api/prompt/、api/tool/、api/agent/）
- 数据抽取服务（routers/extraction.py、api/tool/）
- 报告生成服务（routers/reportgen.py、api/agent/、utils/docx.py）
- AI服务
  - LLM服务（Qwen-32B，api/client/qwen.py、api/llm.py）
  - 向量数据库服务（api/embedder.py，bge-large-zh-v1.5，硅基流动接口）
  - 模型推理服务（api/agent/、api/tool/）

### 4.3 监控服务
- 性能监控
- 日志收集
- 告警服务
- 统计分析

## 5. 部署架构

### 5.1 开发环境
- Docker Compose
- 本地数据库
- 热重载
- 调试工具

### 5.2 测试环境
- Kubernetes集群
- CI/CD流水线
- 自动化测试
- 性能测试

### 5.3 生产环境
- 高可用集群
- 负载均衡
- 数据备份
- 监控告警

## 6. 开发规范

### 6.1 代码规范
- Python: PEP 8
- TypeScript: ESLint
- 命名规范
- 注释规范

### 6.2 文档规范
- API文档
- 开发文档
- 部署文档
- 用户手册

### 6.3 Git规范
- 分支管理
- 提交信息
- 代码审查
- 版本发布

### 6.4 测试规范
- 单元测试
- 集成测试
- 端到端测试
- 性能测试

## 7. AI服务架构

### 7.1 模型服务
- 大语言模型服务
  - 模型：Qwen-32B（api/client/qwen.py、api/llm.py）
  - 用途：自然语言处理、内容生成
  - 部署：API调用/私有部署
- 向量数据库
  - 类型：Milvus/FAISS（api/embedder.py，bge-large-zh-v1.5，硅基流动接口）
  - 用途：语义检索、相似度匹配
  - 部署：独立服务

### 7.2 AI工作流
- 文档处理流程
  - Word文档上传（routers/uploading.py、utils/docx.py）
  - 结构化提取（api/tool/、api/prompt/、routers/extraction.py）
  - 数据验证、用户交互确认、默认值补全
- 模板解析流程
  - 模板识别（AI自动/人工编辑/历史导入）
  - 占位符提取，配套JSON Schema
  - 规则学习
  - 模板验证
- 报告生成流程
  - 数据整合
  - 内容生成（保持Word排版不变，api/agent/、utils/docx.py）
  - 格式优化
  - 质量控制（如错别字检查，后续扩展敏感词、格式规范等）

### 7.3 AI系统优化
- 性能优化
  - 模型量化
  - 批处理优化
  - 缓存策略
  - 并行处理
- 准确性优化
  - 模型微调
  - 规则增强
  - 人工校验
  - 反馈学习
- 资源管理
  - GPU资源调度
  - 负载均衡
  - 队列管理
  - 成本控制

### 7.4 AI监控与运维
- 性能监控
  - 响应时间
  - 准确率
  - 资源使用
  - 成本统计
- 质量监控
  - 识别准确率
  - 生成质量
  - 用户反馈
  - 异常检测
- 运维管理
  - 模型更新
  - 故障恢复
  - 版本控制
  - 数据备份

```