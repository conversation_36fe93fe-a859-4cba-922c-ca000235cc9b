# 文本生成类智能体定义
## 目的
文本生成类智能体是该系统的核心，用于根据用户输入和系统内部数据，生成符合用户需求的文本内容。
## 相关工具
1. **智能模板识别工具**：基于用户输入的自然语言描述，智能分析文本生成意图，识别最适合的报告模板类型，支持多模板匹配和用户确认选择
2. **智能模板解析工具**：解析选定Word模板中的占位符结构，结合模板关键信息定义文件，生成针对性的数据提取prompt，为后续的智能提取做准备
3. **智能数据提取与验证工具**：基于模板关键信息提取prompt，对用户上传的文件、用户输入的文本进行智能解析和提取，自动完成数据完整性检查，对于提取不到的内容使用默认值填充，并提供统一的人工确认，缺失内容，让人工输入或补充上传文件继续提取；
4. **智能报告生成工具**：基于Word模板和已验证的提取数据，智能生成高质量的报告文件，支持实时预览、质量检查和多格式输出

## 交互流程
1. **模板识别阶段**：用户输入生成报告的请求，文本生成类智能体调用智能模板识别工具，基于自然语言意图分析识别出最适合的报告模板类型。如果识别到多个候选模板，则向用户展示匹配度排序的选项供选择；如果未找到合适的模板，则引导用户重新描述需求或提示先完成模板制作，结束本次交互。

2. **模板解析阶段**：确定报告模板后，调用智能模板解析工具，自动扫描Word模板中的占位符结构，结合模板关键信息定义文件生成结构化的数据提取prompt。解析过程中实时显示进度：正在解析的模板名称、识别到的占位符数量、生成的字段定义数量，确保用户了解解析状态。

3. **数据提取与验证阶段**：调用智能数据提取与验证工具，执行一体化的数据处理流程：

   **3.1 智能提取阶段**
   - 基于模板关键信息提取prompt，对用户上传的文件、用户输入的文本进行智能解析
   - 根据模板关键信息文件中的字段定义，逐个提取关键信息内容
   - 对于提取不到的内容，自动使用模板关键信息文件中的默认值进行填充
   - 实时显示提取进度：当前处理字段、已完成字段数量、成功提取的数据内容

   **3.2 自动验证阶段**
   - 立即对提取结果进行完整性检查，验证是否符合模板关键信息文件中的格式要求
   - 检查必填项是否完整，数据格式是否正确，逻辑关系是否合理
   - 计算每个字段的提取置信度和整体数据完整性评分
   - 标识成功提取、使用默认值、缺失或异常的字段

   **3.3 结果整合阶段**
   - 将提取结果、验证状态、置信度信息整合为统一的数据结构
   - 生成操作建议：补充建议、默认值应用建议、质量改进建议
   - 准备用户确认界面所需的所有数据和状态信息

   **3.4 人工确认阶段**
   - 向用户展示分类的数据结果：
     * 已成功提取的字段（显示置信度和来源）
     * 使用默认值的字段（允许用户修改）
     * 缺失或异常的字段（提供补充选项）
   - 提供用户操作选项：
     * 直接确认：对当前提取结果满意，继续下一步报告生成
     * 手动补充：在界面中直接编辑和补充缺失或错误的字段值
     * 补充文件：上传新的文件进行补充提取，系统自动重新运行整个流程
     * 调整模板：发现模板定义问题时，跳转到模板编辑进行调整
   - 只有用户明确确认后，才进入下一步报告生成阶段
   - 数据提取完成后，保存至数据管理模块中

4. **报告生成与输出阶段**：调用智能报告生成工具，执行高质量的报告生成流程：

   **4.1 数据映射阶段**
   - 将已确认的提取数据精确映射到Word模板的占位符位置
   - 验证数据类型和格式与模板要求的匹配性
   - 处理重复字段和复杂数据结构的填充逻辑

   **4.2 内容生成阶段**
   - 基于模板结构和已验证数据生成完整报告内容
   - 保持Word模板的原有格式、样式和布局
   - 实时显示生成进度：数据填充进度、格式处理状态

   **4.3 质量检查阶段**
   - 自动检查生成报告的格式规范性、内容完整性和逻辑一致性
   - 验证所有占位符是否正确填充，是否存在格式错误
   - 计算报告质量评分：完整性、准确性、格式规范性等指标

   **4.4 预览确认阶段**
   - 生成可交互的报告预览，支持分页浏览、缩放、全屏查看
   - 显示报告质量指标和数据填充状态统计
   - 提供用户操作选项：
     * 直接下载：对报告满意，下载Word文件
     * 导出PDF：生成PDF格式的报告文件（可选功能）
     * 重新生成：调整生成参数后重新生成报告
     * 返回修改：返回数据提取步骤修改数据内容
     * 保存草稿：保存当前报告到结果目录，稍后继续处理

   **4.5 文件管理阶段**
   - 将最终确认的报告文件存储至结果管理模块中
   - 生成文件下载链接和访问权限
   - 记录生成历史和用户操作日志，便于后续追踪和优化
