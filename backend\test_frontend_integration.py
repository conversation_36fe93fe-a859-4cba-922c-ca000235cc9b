#!/usr/bin/env python3
"""
测试前端集成 - 验证数据确认界面
"""

import requests
import json
import time

def test_frontend_integration():
    """测试前端集成"""
    
    print("🧪 测试前端集成 - 验证数据确认界面")
    
    # 使用已有的工作流ID
    workflow_id = "f9b279d4-45e4-43c9-8abf-1999a099f734"
    
    print(f"\n📋 第1步: 检查当前工作流状态...")
    
    try:
        # 检查状态
        status_response = requests.get(f'http://localhost:8000/api/workflow/langgraph/status/{workflow_id}')
        
        if status_response.status_code == 200:
            status_data = status_response.json()
            
            print(f"✅ 状态检查成功")
            print(f"  当前阶段: {status_data.get('current_stage')}")
            print(f"  待处理交互: {status_data.get('pending_interactions', [])}")
            print(f"  需要用户操作: {status_data.get('requires_user_action')}")
            print(f"  提取结果数量: {len(status_data.get('extraction_results', []))}")
            
            # 检查是否处于数据确认阶段
            if (status_data.get('current_stage') == 'data_extraction' and 
                'supplement' in status_data.get('pending_interactions', []) and
                status_data.get('requires_user_action')):
                
                print(f"\n✅ 工作流正处于数据确认阶段！")
                
                # 显示提取结果预览
                extraction_results = status_data.get('extraction_results', [])
                if extraction_results:
                    print(f"\n📊 提取结果预览 (前5个):")
                    for i, result in enumerate(extraction_results[:5]):
                        field_name = result.get('field_name', 'unknown')
                        field_value = result.get('field_value', 'N/A')
                        confidence = result.get('confidence', 0)
                        print(f"  {i+1}. {field_name}: {field_value} (置信度: {confidence:.2f})")
                
                print(f"\n🎯 前端应该显示数据确认界面")
                print(f"   - 包含提取统计信息")
                print(f"   - 显示所有提取结果")
                print(f"   - 提供确认和修改按钮")
                
                # 模拟用户确认数据
                print(f"\n📋 第2步: 模拟用户确认数据...")
                
                confirm_response = requests.post(
                    f'http://localhost:8000/api/workflow/langgraph/resume/{workflow_id}',
                    json={'action': 'confirm'}
                )
                
                if confirm_response.status_code == 200:
                    confirm_result = confirm_response.json()
                    print(f"✅ 数据确认成功: {confirm_result.get('status')}")
                    
                    # 等待处理完成
                    print(f"\n📋 第3步: 等待报告生成...")
                    time.sleep(3)
                    
                    # 检查最终状态
                    final_status_response = requests.get(f'http://localhost:8000/api/workflow/langgraph/status/{workflow_id}')
                    
                    if final_status_response.status_code == 200:
                        final_status = final_status_response.json()
                        print(f"✅ 最终状态检查成功")
                        print(f"  当前阶段: {final_status.get('current_stage')}")
                        print(f"  待处理交互: {final_status.get('pending_interactions', [])}")
                        print(f"  需要用户操作: {final_status.get('requires_user_action')}")
                        
                        if not final_status.get('requires_user_action'):
                            print(f"\n🎉 工作流完成！前端应该显示最终结果")
                        else:
                            print(f"\n⚠️ 工作流仍需要用户操作")
                    else:
                        print(f"❌ 最终状态检查失败: {final_status_response.text}")
                else:
                    print(f"❌ 数据确认失败: {confirm_response.text}")
            else:
                print(f"\n⚠️ 工作流不在数据确认阶段")
                print(f"   当前阶段: {status_data.get('current_stage')}")
                print(f"   待处理交互: {status_data.get('pending_interactions', [])}")
        else:
            print(f"❌ 状态检查失败: {status_response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def create_new_workflow_for_test():
    """创建新的工作流用于测试"""
    
    print(f"\n🆕 创建新的工作流用于测试...")
    
    try:
        # 启动新工作流
        start_response = requests.post(
            'http://localhost:8000/api/workflow/langgraph/start',
            json={
                'user_input': '请帮我生成会议纪要',
                'files': []
            }
        )
        
        if start_response.status_code == 200:
            result = start_response.json()
            workflow_id = result.get('workflow_id')
            
            print(f"✅ 新工作流创建成功: {workflow_id}")
            
            # 等待模板推荐
            time.sleep(2)
            
            # 确认模板
            confirm_response = requests.post(
                f'http://localhost:8000/api/workflow/langgraph/resume/{workflow_id}',
                json={'action': 'confirm'}
            )
            
            if confirm_response.status_code == 200:
                print(f"✅ 模板确认成功")
                
                # 等待数据提取
                print(f"⏳ 等待数据提取完成...")
                time.sleep(5)
                
                # 检查是否到达数据确认阶段
                status_response = requests.get(f'http://localhost:8000/api/workflow/langgraph/status/{workflow_id}')
                
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    
                    if (status_data.get('current_stage') == 'data_extraction' and 
                        'supplement' in status_data.get('pending_interactions', [])):
                        print(f"✅ 新工作流已到达数据确认阶段: {workflow_id}")
                        return workflow_id
                    else:
                        print(f"⚠️ 新工作流未到达数据确认阶段")
                        print(f"   当前阶段: {status_data.get('current_stage')}")
                        print(f"   待处理交互: {status_data.get('pending_interactions', [])}")
                else:
                    print(f"❌ 状态检查失败")
            else:
                print(f"❌ 模板确认失败")
        else:
            print(f"❌ 工作流创建失败: {start_response.text}")
            
    except Exception as e:
        print(f"❌ 创建新工作流失败: {e}")
    
    return None

if __name__ == "__main__":
    # 首先测试现有工作流
    test_frontend_integration()
    
    # 如果需要，创建新工作流测试
    # new_workflow_id = create_new_workflow_for_test()
    # if new_workflow_id:
    #     print(f"\n🔄 使用新工作流重新测试...")
    #     test_frontend_integration()
