#!/usr/bin/env python3
"""
简单测试工作流恢复
"""

import requests
import tempfile
import os

def simple_test():
    # 创建测试文件
    content = 'test content'
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(content)
        test_file_path = f.name

    try:
        # 启动工作流
        with open(test_file_path, 'rb') as f:
            files = {'files': ('test.txt', f, 'text/plain')}
            data = {'text_input': '测试'}
            response = requests.post('http://localhost:8000/api/workflow/langgraph/start', files=files, data=data, timeout=15)
        
        print(f'启动状态码: {response.status_code}')
        if response.status_code == 200:
            result = response.json()
            workflow_id = result.get('workflow_id')
            print(f'工作流ID: {workflow_id}')
            
            # 恢复工作流
            resume_data = {'action': 'confirm'}
            resume_response = requests.post(f'http://localhost:8000/api/workflow/langgraph/resume/{workflow_id}', json=resume_data, timeout=15)
            print(f'恢复状态码: {resume_response.status_code}')
            if resume_response.status_code == 200:
                resume_result = resume_response.json()
                print(f'恢复状态: {resume_result.get("status")}')
            else:
                print(f'恢复失败: {resume_response.text}')
        else:
            print(f'启动失败: {response.text}')
    finally:
        if os.path.exists(test_file_path):
            os.unlink(test_file_path)

if __name__ == "__main__":
    simple_test()
