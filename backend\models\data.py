"""
数据源相关数据模型
"""

from sqlalchemy import Column, String, Text, Integer, Boolean, ForeignKey, Numeric
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from .core import BaseModel


class DataSource(BaseModel):
    """数据源表"""

    __tablename__ = "data_sources"

    name = Column(String(255), nullable=False, comment="数据源名称")
    description = Column(Text, nullable=True, comment="数据源描述")
    file_path = Column(String(500), nullable=False, comment="文件路径")
    file_size = Column(Integer, nullable=True, comment="文件大小(字节)")
    mime_type = Column(String(100), nullable=True, comment="MIME类型")
    file_hash = Column(String(64), nullable=True, comment="文件哈希值")
    source_type = Column(String(50), nullable=False, default="document", comment="数据源类型")
    status = Column(String(50), default="uploaded", comment="处理状态")
    extraction_count = Column(Integer, default=0, comment="抽取数据条数")
    file_metadata = Column(JSONB, nullable=True, comment="文件元数据")

    # 关联关系
    extracted_data = relationship("ExtractedData", back_populates="data_source", cascade="all, delete-orphan")
    reports = relationship("Report", back_populates="data_source")

    def __repr__(self):
        return f"<DataSource(id={self.id}, name='{self.name}', status='{self.status}')>"


class ExtractedData(BaseModel):
    """抽取数据表"""

    __tablename__ = "extracted_data"

    data_source_id = Column(UUID(as_uuid=True), ForeignKey("data_sources.id"), nullable=False, comment="数据源ID")
    template_id = Column(UUID(as_uuid=True), ForeignKey("templates.id"), nullable=True, comment="模板ID")
    session_id = Column(UUID(as_uuid=True), ForeignKey("agent_sessions.id"), nullable=True, comment="会话ID")
    field_name = Column(String(255), nullable=False, comment="字段名称")
    field_path = Column(String(500), nullable=True, comment="完整字段路径")
    field_value = Column(Text, nullable=True, comment="字段值")
    original_text = Column(Text, nullable=True, comment="原始文本")
    confidence_score = Column(Numeric(3, 2), nullable=True, comment="置信度分数")
    extraction_method = Column(String(50), default="ai", comment="抽取方法")
    is_verified = Column(Boolean, default=False, comment="是否已验证")
    verification_status = Column(String(20), default="pending", comment="验证状态")
    position_info = Column(JSONB, nullable=True, comment="位置信息")
    parent_id = Column(UUID(as_uuid=True), ForeignKey("extracted_data.id"), nullable=True, comment="父数据ID")
    level = Column(Integer, nullable=False, default=1, comment="层级深度")

    # 关联关系
    data_source = relationship("DataSource", back_populates="extracted_data")
    template = relationship("Template", back_populates="extracted_data")
    session = relationship("AgentSession", back_populates="extracted_data")
    parent = relationship("ExtractedData", remote_side="ExtractedData.id", back_populates="children")
    children = relationship("ExtractedData", back_populates="parent", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<ExtractedData(id={self.id}, field='{self.field_name}', confidence={self.confidence_score})>"