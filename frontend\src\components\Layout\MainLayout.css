/* 主布局样式 */

.main-layout {
  height: 100vh;
  overflow: hidden;
}

.navigation-sidebar {
  background: linear-gradient(180deg, #001529 0%, #002140 100%);
  border-right: 1px solid #303030;
}

.conversation-sidebar {
  border-right: 1px solid #f0f0f0;
  background: #fafafa;
}

.main-workspace {
  background: #fff;
}

/* 导航菜单样式 */
.ant-menu-dark.ant-menu-inline .ant-menu-item-selected {
  background-color: #1890ff;
}

.ant-menu-dark.ant-menu-inline .ant-menu-item:hover {
  background-color: rgba(24, 144, 255, 0.2);
}

/* 对话历史卡片样式 */
.conversation-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.conversation-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.conversation-card.selected {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 消息气泡样式 */
.message-bubble {
  max-width: 80%;
  word-break: break-word;
  white-space: pre-wrap;
}

.message-bubble.user {
  background: #1890ff;
  color: #fff;
  border-top-right-radius: 4px;
}

.message-bubble.agent {
  background: #f6f6f6;
  color: #000;
  border-top-left-radius: 4px;
}

/* 滚动条样式 */
.conversation-history::-webkit-scrollbar,
.message-container::-webkit-scrollbar {
  width: 6px;
}

.conversation-history::-webkit-scrollbar-track,
.message-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.conversation-history::-webkit-scrollbar-thumb,
.message-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.conversation-history::-webkit-scrollbar-thumb:hover,
.message-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .conversation-sidebar {
    width: 240px !important;
  }
}

@media (max-width: 768px) {
  .navigation-sidebar {
    width: 60px !important;
  }
  
  .conversation-sidebar {
    width: 200px !important;
  }
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-left {
  animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}
