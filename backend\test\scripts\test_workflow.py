#!/usr/bin/env python3
"""
端到端工作流测试
"""

import json
import requests
from pathlib import Path

def test_end_to_end_workflow():
    """测试端到端工作流"""
    print("🚀 测试端到端工作流...")
    
    base_url = "http://localhost:8000"
    
    # 准备测试文件
    test_file = Path("test/documents/test_meeting.txt")
    if not test_file.exists():
        print("❌ 测试文件不存在")
        return False
    
    # 准备模板字段
    template_fields = [
        {
            "field_name": "会议主题",
            "description": "会议讨论的主要议题",
            "example": "数字化转型项目进展讨论",
            "format": "普通文本"
        },
        {
            "field_name": "会议时间",
            "description": "会议举行的具体时间",
            "example": "2024年7月4日 14:00-16:00",
            "format": "日期时间"
        },
        {
            "field_name": "主持人",
            "description": "会议的主持人姓名",
            "example": "张总经理",
            "format": "人名"
        },
        {
            "field_name": "项目完成度",
            "description": "项目当前的完成百分比",
            "example": "75%",
            "format": "百分比"
        }
    ]
    
    # 报告模板
    report_template = """
# 会议纪要报告

## 会议基本信息
- **会议主题**: {会议主题}
- **会议时间**: {会议时间}
- **主持人**: {主持人}

## 项目进展
- **完成进度**: {项目完成度}

## 报告生成信息
- 生成时间: {生成时间}
- 数据来源: 文档自动解析
- 置信度评估: 见详细数据

---
*本报告由AI自动生成，请核验关键信息*
"""
    
    try:
        print("📤 执行端到端工作流...")
        
        # 准备请求数据
        files = {
            'file': (test_file.name, open(test_file, 'rb'), 'text/plain')
        }
        
        data = {
            'template_fields': json.dumps(template_fields),
            'report_template': report_template
        }
        
        # 发送请求
        response = requests.post(
            f"{base_url}/api/workflow/process-document",
            files=files,
            data=data
        )
        
        files['file'][1].close()  # 关闭文件
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ 工作流执行成功")
            print(f"🆔 工作流ID: {result['workflow_id']}")
            print(f"📊 处理状态: {result['status']}")
            print(f"⏱️ 处理时间: {result['processing_time']:.2f}秒")
            
            # 显示文件信息
            file_info = result['file_info']
            print(f"\n📄 文件信息:")
            print(f"  - 文件名: {file_info['filename']}")
            print(f"  - 文档类型: {file_info['document_type']}")
            print(f"  - 文本长度: {file_info['text_length']}")
            print(f"  - 段落数: {file_info['paragraph_count']}")
            
            # 显示抽取结果
            extracted_data = result['extracted_data']
            confidence_scores = result['confidence_scores']
            print(f"\n📋 信息抽取结果:")
            for field_name, value in extracted_data.items():
                confidence = confidence_scores.get(field_name, 0.0)
                print(f"  - {field_name}: {value[:50]}{'...' if len(value) > 50 else ''}")
                print(f"    置信度: {confidence:.2f}")
            
            # 显示验证结果
            validation_results = result['validation_results']
            if validation_results:
                print(f"\n🔍 验证结果:")
                valid_count = 0
                for field_name, validation in validation_results.items():
                    is_valid = validation.get("is_valid", False)
                    confidence = validation.get("confidence", 0.0)
                    status = "✅ 有效" if is_valid else "❌ 无效"
                    print(f"  - {field_name}: {status} (置信度: {confidence:.2f})")
                    if is_valid:
                        valid_count += 1
                
                validation_rate = valid_count / len(validation_results) if validation_results else 0
                print(f"  📈 验证通过率: {validation_rate:.2%}")
            
            # 显示生成的报告
            generated_report = result['generated_report']
            print(f"\n📝 生成的报告:")
            print("=" * 60)
            print(generated_report)
            print("=" * 60)
            
            # 计算整体成功率
            extraction_success = len([v for v in extracted_data.values() if v and v != "未找到"])
            total_fields = len(template_fields)
            success_rate = extraction_success / total_fields if total_fields > 0 else 0
            
            print(f"\n🎯 整体评估:")
            print(f"  - 抽取成功率: {success_rate:.2%} ({extraction_success}/{total_fields})")
            print(f"  - 平均置信度: {sum(confidence_scores.values()) / len(confidence_scores) if confidence_scores else 0:.2f}")
            print(f"  - 处理效率: {result['processing_time']:.2f}秒")
            
            return success_rate > 0.5  # 50%以上认为成功
            
        else:
            print(f"❌ 工作流执行失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保FastAPI应用正在运行")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False


def test_demo_fields_api():
    """测试演示字段API"""
    print("\n🔍 测试演示字段API...")
    
    base_url = "http://localhost:8000"
    
    try:
        response = requests.get(f"{base_url}/api/workflow/demo-fields")
        
        if response.status_code == 200:
            demo_fields = response.json()
            print("✅ 演示字段API正常")
            
            print("📋 会议字段模板:")
            for field in demo_fields['meeting_fields']:
                print(f"  - {field['field_name']}: {field['description']}")
            
            print("📋 项目字段模板:")
            for field in demo_fields['project_fields']:
                print(f"  - {field['field_name']}: {field['description']}")
            
            return True
        else:
            print(f"❌ 演示字段API失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 演示字段API测试异常: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始端到端工作流测试\n")
    
    tests = [
        ("演示字段API", test_demo_fields_api),
        ("端到端工作流", test_end_to_end_workflow)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"{'='*60}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 发生异常: {e}")
            results.append((test_name, False))
    
    print(f"\n{'='*60}")
    print("📊 端到端工作流测试结果:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    print(f"\n{'='*60}")
    if all_passed:
        print("🎉 端到端工作流测试通过！")
        print("✅ 完整的报告生成流程已就绪")
        print("🚀 可以开始实际使用了")
    else:
        print("❌ 部分测试失败，需要进一步检查")
        print("💡 请确保FastAPI服务正在运行")
    
    return all_passed


if __name__ == "__main__":
    main()
