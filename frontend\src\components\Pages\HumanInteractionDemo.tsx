/**
 * 人机交互演示页面
 * 展示新的结构化字段验证界面和工作流进度
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Button,
  Space,
  Form,
  Input,
  Select,
  Upload,
  Alert,
  Steps,
  Row,
  Col,
  message,
  Divider,
} from 'antd';
import {
  UploadOutlined,
  PlayCircleOutlined,
  EyeOutlined,
  SettingOutlined,
} from '@ant-design/icons';

import {
  HumanInteractionOrchestrator,
  HumanInteractionSession,
  InteractionSessionStatus,
  FieldValidationStatus,
  WorkflowStage,
  StageStatus
} from '../HumanInteraction';
import useHumanInteraction from '../../hooks/useHumanInteraction';

const { Title, Paragraph, Text } = Typography;
const { Step } = Steps;
const { Dragger } = Upload;

// 演示数据
const mockSession: HumanInteractionSession = {
  session_id: 'demo-session-001',
  workflow_id: 'demo-workflow-001',
  document_name: '会议纪要_2024-07-24.txt',
  template_name: '会议纪要模板',
  status: InteractionSessionStatus.VALIDATING,
  validation_session: {
    session_id: 'demo-session-001',
    document_name: '会议纪要_2024-07-24.txt',
    total_fields: 6,
    validated_fields: 2,
    rejected_fields: 1,
    pending_fields: 3,
    overall_progress: 50,
    current_stage: 'field_validation',
    extracted_fields: [
      {
        field_name: '会议主题',
        field_description: '会议讨论的主要议题和主题',
        field_example: '数字化转型项目进展讨论',
        extracted_value: '季度工作总结与下季度计划',
        confidence: 0.92,
        extraction_method: 'pattern_matching',
        original_context: '本次会议主要围绕季度工作总结与下季度计划展开讨论，各部门负责人汇报了工作进展...',
        is_required: true,
        validation_status: FieldValidationStatus.VALIDATED
      },
      {
        field_name: '会议时间',
        field_description: '会议举行的具体日期和时间',
        field_example: '2024年7月24日 14:00-16:00',
        extracted_value: '2024年7月24日 14:30-16:30',
        confidence: 0.95,
        extraction_method: 'datetime_extraction',
        original_context: '会议于2024年7月24日14:30在会议室A召开，16:30结束...',
        is_required: true,
        validation_status: FieldValidationStatus.VALIDATED
      },
      {
        field_name: '主持人',
        field_description: '会议的主持人姓名',
        field_example: '张总经理',
        extracted_value: '王部长',
        confidence: 0.78,
        extraction_method: 'named_entity_recognition',
        original_context: '会议由王部长主持，各部门负责人参加...',
        is_required: true,
        validation_status: FieldValidationStatus.REJECTED,
        user_rejection_reason: '应该是李总监主持，不是王部长'
      },
      {
        field_name: '参会人员',
        field_description: '参加会议的人员列表',
        field_example: '张三、李四、王五',
        extracted_value: '李总监、张经理、王主任、刘专员',
        confidence: 0.85,
        extraction_method: 'named_entity_recognition',
        original_context: '参会人员包括李总监、张经理、王主任、刘专员等...',
        is_required: false,
        validation_status: FieldValidationStatus.PENDING
      },
      {
        field_name: '重要决议',
        field_description: '会议中做出的重要决定和决议',
        field_example: '决定启动新项目，预算100万',
        extracted_value: '决定加快数字化进程，增加技术投入',
        confidence: 0.73,
        extraction_method: 'keyword_extraction',
        original_context: '经过讨论，与会人员一致同意加快数字化进程，增加技术投入，预计投入资金200万元...',
        is_required: true,
        validation_status: FieldValidationStatus.PENDING
      },
      {
        field_name: '后续行动',
        field_description: '会议确定的后续行动计划',
        field_example: '下周完成方案设计',
        extracted_value: '各部门一周内提交实施计划',
        confidence: 0.68,
        extraction_method: 'action_extraction',
        original_context: '会议要求各部门在一周内提交具体的实施计划，以便统筹安排...',
        is_required: false,
        validation_status: FieldValidationStatus.PENDING
      }
    ],
    validation_start_time: new Date()
  },
  workflow_progress: {
    workflow_id: 'demo-workflow-001',
    document_name: '会议纪要_2024-07-24.txt',
    current_stage: WorkflowStage.HUMAN_VALIDATION,
    overall_progress: 75,
    total_duration: 125,
    estimated_remaining: 180,
    stages: [
      {
        stage: WorkflowStage.DOCUMENT_UPLOAD,
        title: '文档上传',
        description: '上传并解析文档内容',
        status: StageStatus.COMPLETED,
        start_time: new Date(Date.now() - 120000),
        end_time: new Date(Date.now() - 115000),
        duration: 5,
        progress: 100
      },
      {
        stage: WorkflowStage.DOCUMENT_ANALYSIS,
        title: '文档分析',
        description: '分析文档结构和内容类型',
        status: StageStatus.COMPLETED,
        start_time: new Date(Date.now() - 115000),
        end_time: new Date(Date.now() - 105000),
        duration: 10,
        progress: 100
      },
      {
        stage: WorkflowStage.FIELD_EXTRACTION,
        title: '字段抽取',
        description: '使用AI模型抽取关键字段信息',
        status: StageStatus.COMPLETED,
        start_time: new Date(Date.now() - 105000),
        end_time: new Date(Date.now() - 60000),
        duration: 45,
        progress: 100
      },
      {
        stage: WorkflowStage.HUMAN_VALIDATION,
        title: '人工验证',
        description: '人工确认和修正抽取结果',
        status: StageStatus.IN_PROGRESS,
        start_time: new Date(Date.now() - 60000),
        progress: 50
      },
      {
        stage: WorkflowStage.REPORT_GENERATION,
        title: '报告生成',
        description: '根据验证结果生成最终报告',
        status: StageStatus.PENDING
      },
      {
        stage: WorkflowStage.COMPLETED,
        title: '完成',
        description: '处理流程完成',
        status: StageStatus.PENDING
      }
    ],
    is_paused: false
  },
  created_at: new Date(Date.now() - 180000),
  updated_at: new Date(Date.now() - 30000),
  user_preferences: {
    auto_advance: false,
    confidence_threshold: 0.8,
    batch_validation: false
  }
};

const HumanInteractionDemo: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [demoMode, setDemoMode] = useState<'setup' | 'interaction' | 'completed'>('setup');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [form] = Form.useForm();

  const {
    session,
    isLoading,
    error,
    createSession,
    validateField,
    completeSession,
    pauseSession,
    resumeSession,
    cancelSession
  } = useHumanInteraction({
    onSessionComplete: (reportId, report) => {
      message.success('报告生成完成！');
      setDemoMode('completed');
      console.log('Generated report:', { reportId, report });
    },
    onSessionError: (errorMsg) => {
      message.error(`会话错误: ${errorMsg}`);
    },
    onValidationProgress: (progress) => {
      console.log('Validation progress:', progress);
    }
  });

  // 演示模板选项
  const templateOptions = [
    { label: '会议纪要模板', value: '会议纪要模板' },
    { label: '项目报告模板', value: '项目报告模板' },
    { label: '工作总结模板', value: '工作总结模板' },
  ];

  // 处理文件上传
  const handleFileUpload = (file: File) => {
    setSelectedFile(file);
    message.success(`文件 "${file.name}" 已选择`);
    return false; // 阻止自动上传
  };

  // 开始演示会话
  const startDemoSession = async () => {
    if (!selectedFile || !selectedTemplate) {
      message.warning('请选择文件和模板');
      return;
    }

    try {
      await createSession({
        document_path: `/uploads/${selectedFile.name}`,
        template_name: selectedTemplate,
        user_preferences: {
          auto_advance: false,
          confidence_threshold: 0.8,
          batch_validation: false
        }
      });
      setDemoMode('interaction');
    } catch (error) {
      console.error('创建会话失败:', error);
    }
  };

  // 使用演示数据
  const useDemoData = () => {
    setDemoMode('interaction');
    message.info('正在使用演示数据...');
  };

  // 处理字段验证
  const handleFieldValidate = (
    fieldName: string,
    status: FieldValidationStatus,
    value?: string,
    reason?: string
  ) => {
    console.log('Demo field validation:', { fieldName, status, value, reason });
    
    if (session) {
      validateField(fieldName, status, value, reason);
    } else {
      // 演示模式下的处理
      message.success(`字段 "${fieldName}" 验证完成`);
    }
  };

  // 处理验证完成
  const handleValidationComplete = (validatedData: Record<string, string>) => {
    console.log('Demo validation complete:', validatedData);
    
    if (session) {
      completeSession(validatedData);
    } else {
      // 演示模式下的处理
      setDemoMode('completed');
      message.success('演示验证完成！');
    }
  };

  // 重置演示
  const resetDemo = () => {
    setDemoMode('setup');
    setCurrentStep(0);
    setSelectedFile(null);
    setSelectedTemplate('');
    form.resetFields();
  };

  if (demoMode === 'interaction') {
    return (
      <HumanInteractionOrchestrator
        session={session || mockSession}
        onValidationComplete={handleValidationComplete}
        onSessionCancel={() => {
          if (session) {
            cancelSession();
          }
          resetDemo();
        }}
        onSessionPause={() => {
          if (session) {
            pauseSession();
          } else {
            message.info('演示会话已暂停');
          }
        }}
        onSessionResume={() => {
          if (session) {
            resumeSession();
          } else {
            message.info('演示会话已恢复');
          }
        }}
      />
    );
  }

  if (demoMode === 'completed') {
    return (
      <div style={{ maxWidth: 800, margin: '0 auto', padding: '24px' }}>
        <Card style={{ textAlign: 'center', padding: '40px' }}>
          <Title level={2}>🎉 验证完成！</Title>
          <Paragraph>
            所有字段验证已完成，报告已成功生成。
          </Paragraph>
          <Space>
            <Button type="primary" size="large" onClick={resetDemo}>
              开始新的验证
            </Button>
            <Button size="large" onClick={() => setDemoMode('interaction')}>
              查看验证过程
            </Button>
          </Space>
        </Card>
      </div>
    );
  }

  return (
    <div style={{ maxWidth: 1000, margin: '0 auto', padding: '24px' }}>
      <Title level={2}>🤝 人机交互验证演示</Title>
      <Paragraph>
        体验新的结构化字段验证界面，替代传统的聊天式交互，提供更直观高效的人工确认流程。
      </Paragraph>

      {/* 功能特性介绍 */}
      <Alert
        message="新功能特性"
        description={
          <ul style={{ marginBottom: 0 }}>
            <li><strong>结构化验证界面</strong>：替代聊天式交互，提供专门的字段确认界面</li>
            <li><strong>可视化进度指示</strong>：实时显示工作流各阶段的处理进度</li>
            <li><strong>字段级操作</strong>：支持确认、修改、拒绝、跳过等多种验证操作</li>
            <li><strong>上下文展示</strong>：显示字段抽取的原文上下文，辅助人工判断</li>
            <li><strong>批量管理</strong>：支持字段列表视图和批量操作功能</li>
          </ul>
        }
        type="info"
        style={{ marginBottom: '24px' }}
      />

      <Row gutter={[24, 24]}>
        {/* 设置区域 */}
        <Col span={16}>
          <Card title="开始验证" style={{ height: '100%' }}>
            <Steps current={currentStep} style={{ marginBottom: '24px' }}>
              <Step title="上传文档" description="选择要处理的文档" />
              <Step title="选择模板" description="选择字段抽取模板" />
              <Step title="开始验证" description="启动人机交互验证" />
            </Steps>

            <Form form={form} layout="vertical" style={{ marginTop: '24px' }}>
              {/* 文档上传 */}
              <Form.Item
                label="上传文档"
                required
                help="支持 .txt, .docx, .pdf 格式的文档"
              >
                <Dragger
                  accept=".txt,.docx,.pdf"
                  beforeUpload={handleFileUpload}
                  fileList={selectedFile ? [selectedFile as any] : []}
                  onRemove={() => setSelectedFile(null)}
                >
                  <div style={{ padding: '20px' }}>
                    <UploadOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
                    <div style={{ marginTop: '8px' }}>
                      点击或拖拽文件到此区域上传
                    </div>
                    <div style={{ color: '#999', fontSize: '12px' }}>
                      {selectedFile ? `已选择: ${selectedFile.name}` : '请选择要处理的文档文件'}
                    </div>
                  </div>
                </Dragger>
              </Form.Item>

              {/* 模板选择 */}
              <Form.Item
                label="选择模板"
                required
                help="选择适合的字段抽取模板"
              >
                <Select
                  value={selectedTemplate}
                  onChange={setSelectedTemplate}
                  placeholder="请选择模板"
                  options={templateOptions}
                  size="large"
                />
              </Form.Item>

              {/* 用户偏好设置 */}
              <Card size="small" title="验证设置" style={{ marginBottom: '16px' }}>
                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item label="置信度阈值" name="confidence_threshold" initialValue={0.8}>
                      <Select>
                        <Select.Option value={0.6}>0.6 (宽松)</Select.Option>
                        <Select.Option value={0.8}>0.8 (标准)</Select.Option>
                        <Select.Option value={0.9}>0.9 (严格)</Select.Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="自动推进" name="auto_advance" valuePropName="checked" initialValue={false}>
                      <Select>
                        <Select.Option value={false}>手动确认</Select.Option>
                        <Select.Option value={true}>自动推进</Select.Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="批量模式" name="batch_validation" valuePropName="checked" initialValue={false}>
                      <Select>
                        <Select.Option value={false}>逐个验证</Select.Option>
                        <Select.Option value={true}>批量验证</Select.Option>
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              </Card>

              {/* 操作按钮 */}
              <Space size="large" style={{ width: '100%', justifyContent: 'center' }}>
                <Button
                  type="primary"
                  size="large"
                  icon={<PlayCircleOutlined />}
                  onClick={startDemoSession}
                  disabled={!selectedFile || !selectedTemplate}
                  loading={isLoading}
                >
                  开始验证
                </Button>
                <Button
                  size="large"
                  icon={<EyeOutlined />}
                  onClick={useDemoData}
                >
                  使用演示数据
                </Button>
              </Space>
            </Form>
          </Card>
        </Col>

        {/* 说明区域 */}
        <Col span={8}>
          <Card title="使用说明" style={{ height: '100%' }}>
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <div>
                <Title level={5}>📋 验证流程</Title>
                <Paragraph style={{ fontSize: '12px' }}>
                  1. 上传待处理文档<br/>
                  2. 选择字段抽取模板<br/>
                  3. 系统自动抽取字段<br/>
                  4. 人工验证确认结果<br/>
                  5. 生成最终报告
                </Paragraph>
              </div>

              <Divider style={{ margin: '12px 0' }} />

              <div>
                <Title level={5}>⚡ 验证操作</Title>
                <Paragraph style={{ fontSize: '12px' }}>
                  <Text strong>确认正确</Text>：接受AI抽取结果<br/>
                  <Text strong>修改内容</Text>：编辑并改正字段值<br/>
                  <Text strong>拒绝结果</Text>：标记为无效或错误<br/>
                  <Text strong>暂时跳过</Text>：稍后处理该字段
                </Paragraph>
              </div>

              <Divider style={{ margin: '12px 0' }} />

              <div>
                <Title level={5}>📊 进度跟踪</Title>
                <Paragraph style={{ fontSize: '12px' }}>
                  实时查看验证进度、字段状态、处理时间等统计信息，支持暂停恢复操作。
                </Paragraph>
              </div>

              {error && (
                <Alert
                  message="错误"
                  description={error}
                  type="error"
                  size="small"
                  style={{ marginTop: '16px' }}
                />
              )}
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default HumanInteractionDemo;