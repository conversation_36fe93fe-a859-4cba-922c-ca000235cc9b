"""
对话持久化功能测试
测试LangGraph对话持久化、恢复和历史管理功能
"""

import pytest
import asyncio
import uuid
from datetime import datetime
from typing import Dict, Any

from sqlalchemy.orm import Session
from fastapi.testclient import TestClient

from database import get_db, create_tables
from models.dialogue import Conversation, Message, ConversationCheckpoint
from services.dialogue_service import dialogue_service
from services.database_checkpointer import database_checkpointer
from agents.dialogue_agent import base_dialogue_agent
from api.dialogue import router
from main import app


class TestDialoguePersistence:
    """对话持久化测试类"""

    @pytest.fixture(autouse=True)
    def setup_database(self):
        """设置测试数据库"""
        # 创建表
        create_tables()
        yield
        # 清理测试数据（可选）

    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TestClient(app)

    @pytest.fixture
    def db_session(self):
        """获取数据库会话"""
        return next(get_db())

    def test_create_conversation(self, db_session: Session):
        """测试创建对话"""
        # 创建对话
        conversation = dialogue_service.create_conversation(
            user_id="test_user_001",
            title="测试对话",
            interaction_type="chat"
        )
        
        assert conversation is not None
        assert conversation.user_id == "test_user_001"
        assert conversation.title == "测试对话"
        assert conversation.status == "active"
        assert conversation.thread_id is not None
        
        # 验证数据库中的记录
        db_conversation = db_session.query(Conversation).filter(
            Conversation.id == conversation.id
        ).first()
        
        assert db_conversation is not None
        assert db_conversation.user_id == "test_user_001"

    def test_add_message(self, db_session: Session):
        """测试添加消息"""
        # 创建对话
        conversation = dialogue_service.create_conversation(
            user_id="test_user_002",
            title="消息测试对话"
        )
        
        # 添加用户消息
        user_message = dialogue_service.add_message(
            conversation_id=str(conversation.id),
            role="human",
            content="你好，这是一条测试消息"
        )
        
        assert user_message is not None
        assert user_message.role == "human"
        assert user_message.content == "你好，这是一条测试消息"
        assert user_message.sequence_number == 1
        
        # 添加助手消息
        assistant_message = dialogue_service.add_message(
            conversation_id=str(conversation.id),
            role="assistant",
            content="你好！我收到了你的消息。"
        )
        
        assert assistant_message is not None
        assert assistant_message.role == "assistant"
        assert assistant_message.sequence_number == 2
        
        # 验证对话消息计数
        updated_conversation = dialogue_service.get_conversation(str(conversation.id))
        assert updated_conversation.message_count == 2

    def test_get_messages(self, db_session: Session):
        """测试获取消息历史"""
        # 创建对话并添加消息
        conversation = dialogue_service.create_conversation(
            user_id="test_user_003",
            title="历史测试对话"
        )
        
        # 添加多条消息
        for i in range(5):
            dialogue_service.add_message(
                conversation_id=str(conversation.id),
                role="human" if i % 2 == 0 else "assistant",
                content=f"测试消息 {i + 1}"
            )
        
        # 获取所有消息
        messages = dialogue_service.get_messages(str(conversation.id))
        assert len(messages) == 5
        
        # 验证消息顺序
        for i, message in enumerate(messages):
            assert message.sequence_number == i + 1
            assert message.content == f"测试消息 {i + 1}"
        
        # 测试限制数量
        limited_messages = dialogue_service.get_messages(
            str(conversation.id), 
            limit=3
        )
        assert len(limited_messages) == 3

    @pytest.mark.asyncio
    async def test_dialogue_agent_chat(self, db_session: Session):
        """测试智能体对话功能"""
        user_id = "test_user_004"
        thread_id = str(uuid.uuid4())
        
        # 发送第一条消息
        result1 = await base_dialogue_agent.chat(
            user_message="你好，我是测试用户",
            user_id=user_id,
            thread_id=thread_id
        )
        
        assert result1["success"] is True
        assert result1["thread_id"] == thread_id
        assert "response" in result1
        
        # 发送第二条消息（测试对话连续性）
        result2 = await base_dialogue_agent.chat(
            user_message="请记住我刚才说的话",
            user_id=user_id,
            thread_id=thread_id
        )
        
        assert result2["success"] is True
        assert result2["thread_id"] == thread_id
        
        # 获取对话历史
        history = base_dialogue_agent.get_thread_history(thread_id)
        assert len(history) >= 4  # 至少包含2条用户消息和2条助手回复

    def test_database_checkpointer(self, db_session: Session):
        """测试数据库检查点存储器"""
        thread_id = str(uuid.uuid4())
        
        # 创建测试检查点数据
        checkpoint = {
            "v": 1,
            "id": str(uuid.uuid4()),
            "ts": datetime.now().isoformat(),
            "channel_values": {"messages": ["test message"]},
            "channel_versions": {"messages": "1"},
            "versions_seen": {}
        }
        
        metadata = {
            "source": "input",
            "step": 1,
            "parents": {}
        }
        
        config = {
            "configurable": {
                "thread_id": thread_id
            }
        }
        
        # 保存检查点
        result = database_checkpointer.put(
            config=config,
            checkpoint=checkpoint,
            metadata=metadata,
            new_versions={"messages": "1"}
        )
        
        assert result is not None
        assert result["configurable"]["thread_id"] == thread_id
        
        # 获取检查点
        retrieved = database_checkpointer.get_tuple(config)
        assert retrieved is not None
        
        retrieved_checkpoint, retrieved_metadata = retrieved
        assert retrieved_checkpoint["id"] == checkpoint["id"]
        assert retrieved_metadata["step"] == metadata["step"]

    def test_conversation_crud_api(self, client: TestClient):
        """测试对话CRUD API"""
        # 创建对话
        create_response = client.post("/api/dialogue/conversations", json={
            "user_id": "test_user_005",
            "title": "API测试对话",
            "interaction_type": "chat"
        })
        
        assert create_response.status_code == 200
        conversation_data = create_response.json()
        conversation_id = conversation_data["thread_id"]
        
        # 发送消息
        message_response = client.post(
            f"/api/dialogue/conversations/{conversation_id}/messages",
            json={
                "message": "这是通过API发送的测试消息",
                "require_human_approval": False
            }
        )
        
        assert message_response.status_code == 200
        message_data = message_response.json()
        assert message_data["success"] is True
        
        # 获取对话历史
        history_response = client.get(
            f"/api/dialogue/conversations/{conversation_id}/history"
        )
        
        assert history_response.status_code == 200
        history_data = history_response.json()
        assert history_data["success"] is True
        assert len(history_data["messages"]) >= 2  # 用户消息 + 助手回复
        
        # 删除对话
        delete_response = client.delete(
            f"/api/dialogue/conversations/{conversation_id}"
        )
        
        assert delete_response.status_code == 200
        delete_data = delete_response.json()
        assert delete_data["success"] is True

    def test_message_crud_operations(self, db_session: Session):
        """测试消息的增删改查操作"""
        # 创建对话
        conversation = dialogue_service.create_conversation(
            user_id="test_user_006",
            title="消息CRUD测试"
        )
        
        # 添加消息
        message = dialogue_service.add_message(
            conversation_id=str(conversation.id),
            role="human",
            content="原始消息内容"
        )
        
        message_id = str(message.id)
        
        # 获取消息
        retrieved_message = dialogue_service.get_message(message_id)
        assert retrieved_message is not None
        assert retrieved_message.content == "原始消息内容"
        
        # 更新消息
        update_success = dialogue_service.update_message(
            message_id=message_id,
            content="更新后的消息内容",
            metadata={"edited": True}
        )
        
        assert update_success is True
        
        # 验证更新
        updated_message = dialogue_service.get_message(message_id)
        assert updated_message.content == "更新后的消息内容"
        assert updated_message.is_edited is True
        assert updated_message.metadata.get("edited") is True
        
        # 删除消息（软删除）
        delete_success = dialogue_service.delete_message(message_id)
        assert delete_success is True
        
        # 验证软删除
        deleted_message = dialogue_service.get_message(message_id)
        assert deleted_message.is_deleted is True

    def test_conversation_cleanup(self, db_session: Session):
        """测试对话清理功能"""
        # 创建对话并添加数据
        conversation = dialogue_service.create_conversation(
            user_id="test_user_007",
            title="清理测试对话"
        )
        
        conversation_id = str(conversation.id)
        
        # 添加消息
        for i in range(3):
            dialogue_service.add_message(
                conversation_id=conversation_id,
                role="human",
                content=f"测试消息 {i + 1}"
            )
        
        # 添加检查点
        dialogue_service.save_checkpoint(
            conversation_id=conversation_id,
            checkpoint_id=str(uuid.uuid4()),
            checkpoint_data={"test": "data"},
            step_number=1
        )
        
        # 清空消息
        clear_messages_success = dialogue_service.clear_conversation_messages(conversation_id)
        assert clear_messages_success is True
        
        # 验证消息已清空
        messages = dialogue_service.get_messages(conversation_id)
        assert len(messages) == 0
        
        # 清空检查点
        clear_checkpoints_success = dialogue_service.clear_conversation_checkpoints(conversation_id)
        assert clear_checkpoints_success is True
        
        # 验证检查点已清空
        checkpoints = db_session.query(ConversationCheckpoint).filter(
            ConversationCheckpoint.conversation_id == conversation.id
        ).all()
        assert len(checkpoints) == 0
        
        # 删除对话
        delete_success = dialogue_service.delete_conversation(conversation_id)
        assert delete_success is True
        
        # 验证对话已删除
        deleted_conversation = dialogue_service.get_conversation(conversation_id)
        assert deleted_conversation is None


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
