#!/usr/bin/env python3
"""
重新创建数据库表
"""

import os

# 设置环境变量
os.environ['DATABASE_URL'] = 'postgresql://report_user:report_pass@localhost:5432/report_gen'

def recreate_database():
    """重新创建数据库表"""
    print("🔄 重新创建数据库表...")
    
    try:
        from models.core import engine, drop_tables, create_tables
        from sqlalchemy import text
        
        print("📝 删除现有表...")
        drop_tables()
        print("✅ 现有表删除成功")
        
        print("📝 创建新表...")
        create_tables()
        print("✅ 新表创建成功")
        
        # 检查创建的表
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name
            """))
            tables = [row[0] for row in result.fetchall()]
            print(f"✅ 创建的表: {tables}")
            
            # 检查templates表的字段
            result = conn.execute(text("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'templates'
                ORDER BY ordinal_position
            """))
            columns = [(row[0], row[1]) for row in result.fetchall()]
            print(f"✅ templates表字段: {columns}")
            
        return True
        
    except Exception as e:
        print(f"❌ 重新创建数据库失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    recreate_database()
