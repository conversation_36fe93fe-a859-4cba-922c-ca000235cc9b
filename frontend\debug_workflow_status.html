<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作流状态调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status-info {
            background: #e6f7ff;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            border-left: 4px solid #1890ff;
        }
        .extraction-result {
            background: #f6ffed;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            border-left: 3px solid #52c41a;
        }
        .confidence {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .confidence-high { background: #f6ffed; color: #52c41a; }
        .confidence-medium { background: #fff7e6; color: #faad14; }
        .confidence-low { background: #fff2f0; color: #f5222d; }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
        button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        .json-display {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 工作流状态调试</h1>
        
        <div>
            <label for="workflowId">工作流ID:</label>
            <input type="text" id="workflowId" value="f9b279d4-45e4-43c9-8abf-1999a099f734" style="width: 300px; padding: 5px;">
            <button onclick="checkStatus()">检查状态</button>
            <button onclick="confirmData()">确认数据</button>
        </div>

        <div id="statusDisplay"></div>
        <div id="extractionResults"></div>
        <div id="rawResponse"></div>
    </div>

    <script>
        let currentWorkflowId = 'f9b279d4-45e4-43c9-8abf-1999a099f734';

        async function checkStatus() {
            const workflowId = document.getElementById('workflowId').value || currentWorkflowId;
            
            try {
                const response = await fetch(`http://localhost:8000/api/workflow/langgraph/status/${workflowId}`);
                const data = await response.json();
                
                displayStatus(data);
                displayExtractionResults(data);
                displayRawResponse(data);
                
            } catch (error) {
                console.error('检查状态失败:', error);
                document.getElementById('statusDisplay').innerHTML = `<div style="color: red;">错误: ${error.message}</div>`;
            }
        }

        function displayStatus(data) {
            const statusHtml = `
                <div class="status-info">
                    <h3>📊 工作流状态</h3>
                    <p><strong>成功:</strong> ${data.success}</p>
                    <p><strong>工作流ID:</strong> ${data.workflow_id}</p>
                    <p><strong>当前阶段:</strong> ${data.current_stage}</p>
                    <p><strong>下一步动作:</strong> ${data.next_action}</p>
                    <p><strong>需要用户操作:</strong> ${data.requires_user_action}</p>
                    <p><strong>待处理交互:</strong> ${JSON.stringify(data.pending_interactions)}</p>
                    <p><strong>人机交互数量:</strong> ${data.human_interactions ? data.human_interactions.length : 0}</p>
                    <p><strong>提取结果数量:</strong> ${data.extraction_results ? data.extraction_results.length : 0}</p>
                </div>
            `;
            document.getElementById('statusDisplay').innerHTML = statusHtml;
        }

        function displayExtractionResults(data) {
            if (!data.extraction_results || data.extraction_results.length === 0) {
                document.getElementById('extractionResults').innerHTML = '<p>没有提取结果</p>';
                return;
            }

            let resultsHtml = '<h3>📋 提取结果</h3>';
            
            data.extraction_results.forEach((result, index) => {
                const confidence = result.confidence || 0;
                const confidenceClass = confidence > 0.8 ? 'confidence-high' : 
                                      confidence > 0.6 ? 'confidence-medium' : 'confidence-low';
                
                resultsHtml += `
                    <div class="extraction-result">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <strong>${result.field_name}</strong>
                            <span class="confidence ${confidenceClass}">${(confidence * 100).toFixed(1)}%</span>
                        </div>
                        <div style="margin-top: 5px;">${result.field_value}</div>
                    </div>
                `;
            });
            
            document.getElementById('extractionResults').innerHTML = resultsHtml;
        }

        function displayRawResponse(data) {
            const rawHtml = `
                <h3>🔧 原始响应</h3>
                <div class="json-display">${JSON.stringify(data, null, 2)}</div>
            `;
            document.getElementById('rawResponse').innerHTML = rawHtml;
        }

        async function confirmData() {
            const workflowId = document.getElementById('workflowId').value || currentWorkflowId;
            
            try {
                const response = await fetch(`http://localhost:8000/api/workflow/langgraph/resume/${workflowId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ action: 'confirm' })
                });
                
                const result = await response.json();
                
                alert(`确认结果: ${result.status || result.message || '成功'}`);
                
                // 重新检查状态
                setTimeout(checkStatus, 1000);
                
            } catch (error) {
                console.error('确认数据失败:', error);
                alert(`确认失败: ${error.message}`);
            }
        }

        // 页面加载时自动检查状态
        window.onload = function() {
            checkStatus();
        };
    </script>
</body>
</html>
