/**
 * 人机交互协调器 - 统一管理人工验证流程
 * 整合字段验证界面和工作流进度指示器
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Typography,
  Button,
  Space,
  Modal,
  message,
  notification,
  Row,
  Col,
  Alert,
  Drawer,
  Tabs,
  Tag,
  Statistic,
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  SettingOutlined,
  HistoryOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';

import FieldValidationInterface, { 
  FieldValidationStatus, 
  ExtractedField, 
  ValidationSession 
} from './FieldValidationInterface';
import WorkflowProgressIndicator, { 
  WorkflowProgress, 
  WorkflowStage, 
  StageStatus,
  WorkflowStageInfo 
} from './WorkflowProgressIndicator';

const { Title, Paragraph } = Typography;
const { TabPane } = Tabs;

// 人机交互会话状态
export enum InteractionSessionStatus {
  INITIALIZING = 'initializing',
  WAITING_FOR_VALIDATION = 'waiting_for_validation',
  VALIDATING = 'validating',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  ERROR = 'error'
}

// 人机交互会话数据
export interface HumanInteractionSession {
  session_id: string;
  workflow_id: string;
  document_name: string;
  template_name: string;
  status: InteractionSessionStatus;
  validation_session?: ValidationSession;
  workflow_progress: WorkflowProgress;
  created_at: Date;
  updated_at: Date;
  user_preferences?: {
    auto_advance: boolean;
    confidence_threshold: number;
    batch_validation: boolean;
  };
  statistics?: {
    total_validation_time: number;
    fields_validated_per_minute: number;
    accuracy_rate: number;
  };
}

interface HumanInteractionOrchestratorProps {
  session?: HumanInteractionSession;
  onValidationComplete: (validatedData: Record<string, string>) => void;
  onSessionCancel: () => void;
  onSessionPause: () => void;
  onSessionResume: () => void;
  isVisible?: boolean;
}

const HumanInteractionOrchestrator: React.FC<HumanInteractionOrchestratorProps> = ({
  session,
  onValidationComplete,
  onSessionCancel,
  onSessionPause,
  onSessionResume,
  isVisible = true
}) => {
  const [currentTab, setCurrentTab] = useState<string>('validation');
  const [settingsVisible, setSettingsVisible] = useState(false);
  const [historyVisible, setHistoryVisible] = useState(false);
  const [validationStartTime, setValidationStartTime] = useState<Date | null>(null);
  const [validatedFieldsCount, setValidatedFieldsCount] = useState(0);

  // 更新验证统计
  const updateValidationStats = useCallback(() => {
    if (!session?.validation_session || !validationStartTime) return;

    const now = new Date();
    const elapsedMinutes = (now.getTime() - validationStartTime.getTime()) / (1000 * 60);
    const fieldsPerMinute = validatedFieldsCount / Math.max(elapsedMinutes, 1);

    // 这里可以发送统计数据到后端
    console.log('Validation stats:', {
      elapsedMinutes: elapsedMinutes.toFixed(2),
      fieldsPerMinute: fieldsPerMinute.toFixed(2),
      validatedFields: validatedFieldsCount
    });
  }, [session, validationStartTime, validatedFieldsCount]);

  // 监听验证开始
  useEffect(() => {
    if (session?.status === InteractionSessionStatus.VALIDATING && !validationStartTime) {
      setValidationStartTime(new Date());
    }
  }, [session?.status, validationStartTime]);

  // 定期更新统计
  useEffect(() => {
    if (session?.status === InteractionSessionStatus.VALIDATING) {
      const interval = setInterval(updateValidationStats, 30000); // 每30秒更新一次
      return () => clearInterval(interval);
    }
  }, [session?.status, updateValidationStats]);

  if (!session || !isVisible) {
    return null;
  }

  // 处理字段验证
  const handleFieldValidate = (
    fieldName: string, 
    status: FieldValidationStatus, 
    value?: string, 
    reason?: string
  ) => {
    console.log('Field validation:', { fieldName, status, value, reason });
    
    // 更新已验证字段计数
    if ([
      FieldValidationStatus.VALIDATED, 
      FieldValidationStatus.MODIFIED, 
      FieldValidationStatus.REJECTED
    ].includes(status)) {
      setValidatedFieldsCount(prev => prev + 1);
    }

    // 显示验证反馈
    const actionText = {
      [FieldValidationStatus.VALIDATED]: '确认',
      [FieldValidationStatus.MODIFIED]: '修改',
      [FieldValidationStatus.REJECTED]: '拒绝',
      [FieldValidationStatus.SKIPPED]: '跳过'
    }[status];

    message.success(`字段 "${fieldName}" 已${actionText}`);

    // 可以在这里添加更多的验证逻辑或API调用
  };

  // 处理验证完成
  const handleValidationComplete = (validatedData: Record<string, string>) => {
    const endTime = new Date();
    const totalTime = validationStartTime ? 
      (endTime.getTime() - validationStartTime.getTime()) / 1000 : 0;

    notification.success({
      message: '验证完成',
      description: `成功验证 ${Object.keys(validatedData).length} 个字段，共用时 ${totalTime.toFixed(1)} 秒`,
      duration: 5
    });

    onValidationComplete(validatedData);
  };

  // 处理会话取消
  const handleSessionCancel = () => {
    Modal.confirm({
      title: '确认取消',
      content: '取消验证会丢失当前进度，确定要取消吗？',
      okText: '确认取消',
      cancelText: '继续验证',
      okType: 'danger',
      onOk: () => {
        message.info('验证已取消');
        onSessionCancel();
      }
    });
  };

  // 处理会话暂停/恢复
  const handleSessionPauseResume = () => {
    if (session.status === InteractionSessionStatus.PAUSED) {
      onSessionResume();
      message.info('验证已恢复');
    } else {
      onSessionPause();
      message.info('验证已暂停');
    }
  };

  // 获取会话状态颜色
  const getSessionStatusColor = (status: InteractionSessionStatus): string => {
    switch (status) {
      case InteractionSessionStatus.COMPLETED: return '#52c41a';
      case InteractionSessionStatus.VALIDATING: return '#1890ff';
      case InteractionSessionStatus.PAUSED: return '#faad14';
      case InteractionSessionStatus.ERROR: return '#f5222d';
      case InteractionSessionStatus.CANCELLED: return '#d9d9d9';
      default: return '#722ed1';
    }
  };

  // 获取当前阶段信息
  const currentStage = session.workflow_progress.stages.find(
    stage => stage.stage === session.workflow_progress.current_stage
  );

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* 顶部控制栏 */}
      <Card size="small" style={{ marginBottom: '16px', flex: '0 0 auto' }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Title level={4} style={{ margin: 0 }}>
                🤝 人机协作验证
              </Title>
              <Tag color={getSessionStatusColor(session.status)}>
                {session.status}
              </Tag>
              <Tag>{session.template_name}</Tag>
            </Space>
          </Col>
          <Col>
            <Space>
              {session.validation_session && (
                <Space>
                  <Statistic
                    title="验证进度"
                    value={session.validation_session.validated_fields}
                    suffix={`/ ${session.validation_session.total_fields}`}
                    style={{ marginRight: '16px' }}
                  />
                  <Statistic
                    title="验证效率"
                    value={(validatedFieldsCount / Math.max((Date.now() - (validationStartTime?.getTime() || Date.now())) / (1000 * 60), 1)).toFixed(1)}
                    suffix="字段/分钟"
                    style={{ marginRight: '16px' }}
                  />
                </Space>
              )}
              
              <Button
                icon={session.status === InteractionSessionStatus.PAUSED ? <PlayCircleOutlined /> : <PauseCircleOutlined />}
                onClick={handleSessionPauseResume}
                disabled={![InteractionSessionStatus.VALIDATING, InteractionSessionStatus.PAUSED].includes(session.status)}
              >
                {session.status === InteractionSessionStatus.PAUSED ? '恢复' : '暂停'}
              </Button>
              
              <Button
                icon={<StopOutlined />}
                danger
                onClick={handleSessionCancel}
              >
                取消
              </Button>
              
              <Button
                icon={<SettingOutlined />}
                onClick={() => setSettingsVisible(true)}
              >
                设置
              </Button>
              
              <Button
                icon={<HistoryOutlined />}
                onClick={() => setHistoryVisible(true)}
              >
                历史
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 主要内容区域 */}
      <div style={{ flex: '1 1 auto', overflow: 'hidden' }}>
        <Tabs 
          activeKey={currentTab} 
          onChange={setCurrentTab}
          style={{ height: '100%' }}
          tabBarStyle={{ marginBottom: '16px' }}
        >
          {/* 字段验证标签页 */}
          <TabPane 
            tab="字段验证" 
            key="validation"
            style={{ height: 'calc(100% - 46px)', overflow: 'auto' }}
          >
            {session.status === InteractionSessionStatus.ERROR ? (
              <Alert
                message="验证出错"
                description="字段验证过程中出现错误，请检查并重试。"
                type="error"
                showIcon
                style={{ margin: '24px' }}
              />
            ) : (
              <FieldValidationInterface
                session={session.validation_session}
                onFieldValidate={handleFieldValidate}
                onSessionComplete={handleValidationComplete}
                onSessionCancel={handleSessionCancel}
                isLoading={session.status === InteractionSessionStatus.INITIALIZING}
              />
            )}
          </TabPane>

          {/* 工作流进度标签页 */}
          <TabPane 
            tab="处理进度" 
            key="progress"
            style={{ height: 'calc(100% - 46px)', overflow: 'auto', padding: '0 24px' }}
          >
            <WorkflowProgressIndicator
              progress={session.workflow_progress}
              showDetailedView={true}
              onStageClick={(stage) => {
                console.log('Stage clicked:', stage);
                // 可以根据点击的阶段显示相关信息
              }}
            />
          </TabPane>

          {/* 会话信息标签页 */}
          <TabPane 
            tab="会话信息" 
            key="info"
            style={{ height: 'calc(100% - 46px)', overflow: 'auto', padding: '0 24px' }}
          >
            <Card title="会话详情">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Statistic title="会话ID" value={session.session_id} />
                </Col>
                <Col span={12}>
                  <Statistic title="工作流ID" value={session.workflow_id} />
                </Col>
                <Col span={12}>
                  <Statistic title="文档名称" value={session.document_name} />
                </Col>
                <Col span={12}>
                  <Statistic title="模板名称" value={session.template_name} />
                </Col>
                <Col span={12}>
                  <Statistic title="创建时间" value={session.created_at.toLocaleString()} />
                </Col>
                <Col span={12}>
                  <Statistic title="更新时间" value={session.updated_at.toLocaleString()} />
                </Col>
              </Row>

              {session.statistics && (
                <Card title="验证统计" style={{ marginTop: '16px' }}>
                  <Row gutter={[16, 16]}>
                    <Col span={8}>
                      <Statistic
                        title="总验证时间"
                        value={(session.statistics.total_validation_time / 60).toFixed(1)}
                        suffix="分钟"
                      />
                    </Col>
                    <Col span={8}>
                      <Statistic
                        title="验证效率"
                        value={session.statistics.fields_validated_per_minute.toFixed(1)}
                        suffix="字段/分钟"
                      />
                    </Col>
                    <Col span={8}>
                      <Statistic
                        title="准确率"
                        value={(session.statistics.accuracy_rate * 100).toFixed(1)}
                        suffix="%"
                      />
                    </Col>
                  </Row>
                </Card>
              )}
            </Card>
          </TabPane>
        </Tabs>
      </div>

      {/* 设置抽屉 */}
      <Drawer
        title="验证设置"
        placement="right"
        width={400}
        open={settingsVisible}
        onClose={() => setSettingsVisible(false)}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Alert
            message="用户偏好设置"
            description="这些设置将影响验证流程的行为。"
            type="info"
            icon={<InfoCircleOutlined />}
          />
          {/* 这里可以添加具体的设置项 */}
          <Paragraph>设置功能开发中...</Paragraph>
        </Space>
      </Drawer>

      {/* 历史抽屉 */}
      <Drawer
        title="验证历史"
        placement="right"
        width={500}
        open={historyVisible}
        onClose={() => setHistoryVisible(false)}
      >
        <Alert
          message="历史记录"
          description="查看之前的验证会话记录。"
          type="info"
          icon={<HistoryOutlined />}
        />
        <Paragraph>历史记录功能开发中...</Paragraph>
      </Drawer>
    </div>
  );
};

export default HumanInteractionOrchestrator;