"""
数据管理API路由
"""

from typing import List
from uuid import UUID
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlalchemy.orm import Session
from database import get_db
from models.data import DataSource, ExtractedData
from schemas.data import (
    DataSourceCreate, DataSourceResponse, ExtractedDataResponse,
    DataExtractionRequest, DataExtractionResponse
)
from schemas.common import MessageResponse, PaginatedResponse

router = APIRouter(prefix="/api/data", tags=["数据管理"])


@router.get("/sources", response_model=PaginatedResponse[DataSourceResponse])
async def list_data_sources(
    page: int = 1,
    size: int = 20,
    db: Session = Depends(get_db)
):
    """获取数据源列表"""
    offset = (page - 1) * size
    
    sources = db.query(DataSource).filter(DataSource.is_active == True).offset(offset).limit(size).all()
    total = db.query(DataSource).filter(DataSource.is_active == True).count()
    
    items = [DataSourceResponse.model_validate(source) for source in sources]
    pages = (total + size - 1) // size
    
    return PaginatedResponse(
        items=items,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/sources/{source_id}", response_model=DataSourceResponse)
async def get_data_source(source_id: UUID, db: Session = Depends(get_db)):
    """获取数据源详情"""
    source = db.query(DataSource).filter(
        DataSource.id == source_id,
        DataSource.is_active == True
    ).first()
    
    if not source:
        raise HTTPException(status_code=404, detail="数据源不存在")
    
    return DataSourceResponse.model_validate(source)


@router.post("/sources/upload", response_model=DataSourceResponse)
async def upload_data_source(
    name: str = Form(...),
    description: str = Form(None),
    source_type: str = Form("document"),
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """上传数据源文件"""
    # 验证文件类型
    allowed_types = ['.doc', '.docx', '.pdf', '.txt']
    if not any(file.filename.endswith(ext) for ext in allowed_types):
        raise HTTPException(status_code=400, detail="不支持的文件格式")
    
    # TODO: 实现文件保存逻辑
    file_path = f"uploads/data/{file.filename}"
    
    # 创建数据源记录
    data_source = DataSource(
        name=name,
        description=description,
        source_type=source_type,
        file_path=file_path,
        file_size=file.size,
        mime_type=file.content_type,
        status="uploaded",
        created_by="system"  # TODO: 从认证信息获取
    )
    
    db.add(data_source)
    db.commit()
    db.refresh(data_source)
    
    return DataSourceResponse.model_validate(data_source)


@router.post("/extract", response_model=DataExtractionResponse)
async def extract_data(
    request: DataExtractionRequest,
    db: Session = Depends(get_db)
):
    """执行数据抽取"""
    # 验证数据源存在
    data_source = db.query(DataSource).filter(
        DataSource.id == request.data_source_id,
        DataSource.is_active == True
    ).first()
    
    if not data_source:
        raise HTTPException(status_code=404, detail="数据源不存在")
    
    # TODO: 实现实际的数据抽取逻辑
    # 这里先返回模拟数据
    import time
    start_time = time.time()
    
    # 模拟抽取过程
    extracted_count = 5
    success_count = 5
    failed_count = 0
    
    extraction_time = time.time() - start_time
    
    # 更新数据源状态
    data_source.status = "extracted"
    db.commit()
    
    return DataExtractionResponse(
        data_source_id=request.data_source_id,
        extracted_count=extracted_count,
        success_count=success_count,
        failed_count=failed_count,
        extraction_time=extraction_time
    )


@router.get("/sources/{source_id}/extracted", response_model=List[ExtractedDataResponse])
async def get_extracted_data(source_id: UUID, db: Session = Depends(get_db)):
    """获取数据源的抽取数据"""
    # 验证数据源存在
    source = db.query(DataSource).filter(
        DataSource.id == source_id,
        DataSource.is_active == True
    ).first()
    
    if not source:
        raise HTTPException(status_code=404, detail="数据源不存在")
    
    extracted_data = db.query(ExtractedData).filter(
        ExtractedData.data_source_id == source_id,
        ExtractedData.is_active == True
    ).all()
    
    return [ExtractedDataResponse.model_validate(data) for data in extracted_data]


@router.delete("/sources/{source_id}", response_model=MessageResponse)
async def delete_data_source(source_id: UUID, db: Session = Depends(get_db)):
    """删除数据源(软删除)"""
    source = db.query(DataSource).filter(
        DataSource.id == source_id,
        DataSource.is_active == True
    ).first()
    
    if not source:
        raise HTTPException(status_code=404, detail="数据源不存在")
    
    source.is_active = False
    db.commit()
    
    return MessageResponse(message="数据源删除成功")


@router.post("/sources/batch-delete", response_model=MessageResponse)
async def batch_delete_data_sources(
    source_ids: List[UUID],
    db: Session = Depends(get_db)
):
    """批量删除数据源"""
    if not source_ids:
        raise HTTPException(status_code=400, detail="请提供要删除的数据源ID列表")

    # 查找存在的数据源
    sources = db.query(DataSource).filter(
        DataSource.id.in_(source_ids),
        DataSource.is_active == True
    ).all()

    if not sources:
        raise HTTPException(status_code=404, detail="未找到指定的数据源")

    # 批量软删除
    deleted_count = 0
    for source in sources:
        source.is_active = False
        deleted_count += 1

    db.commit()

    return MessageResponse(message=f"成功删除 {deleted_count} 个数据源")


@router.post("/sources/export")
async def export_data_sources(
    source_ids: List[UUID] = None,
    export_format: str = "json",
    db: Session = Depends(get_db)
):
    """导出数据源"""
    # 构建查询
    query = db.query(DataSource).filter(DataSource.is_active == True)

    if source_ids:
        query = query.filter(DataSource.id.in_(source_ids))

    sources = query.all()

    if not sources:
        raise HTTPException(status_code=404, detail="未找到要导出的数据源")

    # 准备导出数据
    export_data = []
    for source in sources:
        source_data = {
            "id": str(source.id),
            "name": source.name,
            "description": source.description,
            "source_type": source.source_type,
            "file_path": source.file_path,
            "file_size": source.file_size,
            "mime_type": source.mime_type,
            "status": source.status,
            "created_at": source.created_at.isoformat() if source.created_at else None,
            "updated_at": source.updated_at.isoformat() if source.updated_at else None
        }
        export_data.append(source_data)

    if export_format.lower() == "json":
        from fastapi.responses import JSONResponse
        return JSONResponse(
            content={
                "export_time": datetime.now().isoformat(),
                "total_count": len(export_data),
                "data_sources": export_data
            },
            headers={"Content-Disposition": "attachment; filename=data_sources_export.json"}
        )
    else:
        raise HTTPException(status_code=400, detail="不支持的导出格式，目前仅支持 json")


@router.post("/sources/import")
async def import_data_sources(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """导入数据源"""
    if not file.filename.endswith('.json'):
        raise HTTPException(status_code=400, detail="仅支持JSON格式的导入文件")

    try:
        import json
        content = await file.read()
        import_data = json.loads(content.decode('utf-8'))

        if "data_sources" not in import_data:
            raise HTTPException(status_code=400, detail="导入文件格式错误，缺少 data_sources 字段")

        imported_count = 0
        skipped_count = 0

        for source_data in import_data["data_sources"]:
            # 检查是否已存在同名数据源
            existing = db.query(DataSource).filter(
                DataSource.name == source_data["name"],
                DataSource.is_active == True
            ).first()

            if existing:
                skipped_count += 1
                continue

            # 创建新数据源
            new_source = DataSource(
                name=source_data["name"],
                description=source_data.get("description"),
                source_type=source_data.get("source_type", "document"),
                file_path=source_data.get("file_path"),
                file_size=source_data.get("file_size"),
                mime_type=source_data.get("mime_type"),
                status=source_data.get("status", "imported"),
                created_by="import_system"
            )

            db.add(new_source)
            imported_count += 1

        db.commit()

        return {
            "message": "数据源导入完成",
            "imported_count": imported_count,
            "skipped_count": skipped_count,
            "total_processed": imported_count + skipped_count
        }

    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="JSON文件格式错误")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导入失败: {str(e)}")
