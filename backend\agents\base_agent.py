"""
基础智能体类
定义智能体的通用接口和基础功能
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime

from services.llm_service import llm_service, LLMResponse

logger = logging.getLogger(__name__)


@dataclass
class BaseAgentState:
    """基础智能体状态数据类"""
    workflow_id: str
    current_stage: str
    
    # 文档信息
    document_info: Dict[str, Any] = field(default_factory=dict)
    
    # 模板信息
    template_info: Dict[str, Any] = field(default_factory=dict)
    
    # 抽取数据
    extracted_data: Dict[str, Any] = field(default_factory=dict)
    
    # 置信度评分
    confidence_scores: Dict[str, float] = field(default_factory=dict)
    
    # 验证结果
    validation_results: Dict[str, Any] = field(default_factory=dict)
    
    # 生成结果
    generation_results: Dict[str, Any] = field(default_factory=dict)
    
    # 质量评分
    quality_scores: Dict[str, float] = field(default_factory=dict)
    
    # 人工干预记录
    human_interventions: list = field(default_factory=list)
    
    # 循环次数
    iteration_count: int = 0
    
    # 错误日志
    error_logs: list = field(default_factory=list)
    
    # 时间戳
    timestamps: Dict[str, datetime] = field(default_factory=dict)
    
    def add_error(self, agent_name: str, error: str):
        """添加错误日志"""
        self.error_logs.append({
            "agent": agent_name,
            "error": error,
            "timestamp": datetime.now()
        })
    
    def set_timestamp(self, stage: str):
        """设置阶段时间戳"""
        self.timestamps[stage] = datetime.now()
    
    def get_stage_duration(self, stage: str) -> Optional[float]:
        """获取阶段耗时（秒）"""
        if stage in self.timestamps:
            start_time = self.timestamps.get(f"{stage}_start")
            end_time = self.timestamps.get(stage)
            if start_time and end_time:
                return (end_time - start_time).total_seconds()
        return None


class BaseAgent(ABC):
    """基础智能体抽象类"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(f"agent.{name}")
    
    @abstractmethod
    async def process(self, state: BaseAgentState) -> BaseAgentState:
        """
        处理智能体逻辑

        Args:
            state: 当前状态

        Returns:
            BaseAgentState: 更新后的状态
        """
        pass
    
    async def call_llm(self, messages: list, **kwargs) -> LLMResponse:
        """调用LLM服务"""
        try:
            return await llm_service.call_llm(messages, **kwargs)
        except Exception as e:
            self.logger.error(f"{self.name} LLM调用失败: {e}")
            raise
    
    def log_info(self, message: str):
        """记录信息日志"""
        self.logger.info(f"[{self.name}] {message}")
    
    def log_error(self, message: str, state: BaseAgentState):
        """记录错误日志"""
        self.logger.error(f"[{self.name}] {message}")
        state.add_error(self.name, message)

    def update_stage(self, state: BaseAgentState, stage: str):
        """更新当前阶段"""
        state.current_stage = stage
        state.set_timestamp(stage)
        self.log_info(f"进入阶段: {stage}")


class AgentResult:
    """智能体处理结果"""
    
    def __init__(self, success: bool, message: str = "", data: Dict[str, Any] = None):
        self.success = success
        self.message = message
        self.data = data or {}
        self.timestamp = datetime.now()
    
    def __bool__(self):
        return self.success
    
    def __str__(self):
        return f"AgentResult(success={self.success}, message='{self.message}')"
