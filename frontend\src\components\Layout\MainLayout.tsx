/**
 * 主布局组件 - 三栏式布局
 * 左侧：导航栏
 * 中间：历史对话栏
 * 右侧：主操作界面
 */

import React, { useState } from 'react';
import { Layout } from 'antd';
import NavigationSidebar from './NavigationSidebar';
import ConversationHistory from './ConversationHistory';
import MainWorkspace from './MainWorkspace';
import './MainLayout.css';

const { Sider, Content } = Layout;

interface MainLayoutProps {
  children?: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = () => {
  const [currentTab, setCurrentTab] = useState<string>('main');
  const [conversationCollapsed, setConversationCollapsed] = useState(false);

  // 模拟用户信息
  const currentUser = {
    username: '测试用户',
    avatar: null
  };

  const handleTabChange = (tab: string) => {
    setCurrentTab(tab);
  };

  const handleLogout = () => {
    // 实现登出逻辑
    console.log('用户登出');
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* 左侧导航栏 */}
      <NavigationSidebar 
        currentTab={currentTab}
        onTabChange={handleTabChange}
        currentUser={currentUser}
        onLogout={handleLogout}
      />

      {/* 中间历史对话栏 */}
      <Sider
        width={280}
        theme="light"
        collapsible
        collapsed={conversationCollapsed}
        onCollapse={setConversationCollapsed}
        collapsedWidth={0}
        trigger={null}
        style={{
          borderRight: '1px solid #f0f0f0',
          background: '#fafafa'
        }}
      >
        <ConversationHistory collapsed={conversationCollapsed} />
      </Sider>

      {/* 右侧主操作界面 */}
      <Layout style={{ background: '#fff' }}>
        <Content style={{ 
          margin: 0, 
          padding: 0,
          display: 'flex',
          flexDirection: 'column',
          height: '100vh'
        }}>
          <MainWorkspace 
            currentTab={currentTab}
            onToggleConversation={() => setConversationCollapsed(!conversationCollapsed)}
            conversationCollapsed={conversationCollapsed}
          />
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
