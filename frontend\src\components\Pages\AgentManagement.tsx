/**
 * 智能体管理页面
 */

import React from 'react';
import { Card, Typography, Empty } from 'antd';
import { RobotOutlined } from '@ant-design/icons';

const { Title } = Typography;

const AgentManagement: React.FC = () => {
  return (
    <Card
      title={
        <span>
          <RobotOutlined style={{ marginRight: '8px' }} />
          智能体管理
        </span>
      }
    >
      <Empty
        description="智能体管理功能开发中..."
        style={{ padding: '60px 0' }}
      />
    </Card>
  );
};

export default AgentManagement;
