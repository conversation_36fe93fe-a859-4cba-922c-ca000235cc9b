#!/usr/bin/env python3
"""
智能数据提取服务
基于动态读取的字段定义，实现智能的信息提取功能
"""

import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from .llm_service import llm_service
from .template_service import template_service

logger = logging.getLogger(__name__)

class DataExtractionService:
    """智能数据提取服务"""
    
    def __init__(self):
        self.llm_service = llm_service
        self.template_service = template_service
    
    async def extract_data_from_document(self, document_content: str, 
                                       template_id: str, 
                                       workflow_id: str) -> Dict[str, Any]:
        """
        从文档中提取数据
        
        Args:
            document_content: 文档内容
            template_id: 模板ID
            workflow_id: 工作流ID
            
        Returns:
            提取结果字典
        """
        try:
            # 获取模板信息
            templates = self.template_service.get_available_templates()
            template_info = None
            
            for template in templates:
                if template["template_id"] == template_id:
                    template_info = template
                    break
            
            if not template_info:
                raise ValueError(f"未找到模板: {template_id}")
            
            field_definitions = template_info["field_definitions"]
            
            logger.info(f"[{workflow_id}] 开始提取数据，字段数量: {len(field_definitions)}")
            
            # 构建智能提取prompt
            extraction_result = await self._extract_with_llm(
                document_content, field_definitions, workflow_id
            )
            
            # 保存提取结果
            if extraction_result["success"]:
                file_path = self.template_service.save_extracted_keys(
                    template_id, extraction_result["data"], workflow_id
                )
                extraction_result["file_path"] = file_path
            
            return extraction_result
            
        except Exception as e:
            logger.error(f"[{workflow_id}] 数据提取失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "data": {},
                "extraction_results": []
            }
    
    async def _extract_with_llm(self, document_content: str, 
                              field_definitions: List[Dict[str, Any]], 
                              workflow_id: str) -> Dict[str, Any]:
        """
        使用LLM进行智能提取
        
        Args:
            document_content: 文档内容
            field_definitions: 字段定义列表
            workflow_id: 工作流ID
            
        Returns:
            提取结果
        """
        try:
            # 构建字段描述
            field_descriptions = []
            field_mapping = {}
            
            for field_def in field_definitions:
                field_name = field_def["field_name"]
                description = field_def.get("description", "")
                example = field_def.get("example", "")
                format_type = field_def.get("format", "普通文本")
                is_required = field_def.get("must", False)
                
                field_descriptions.append(f"""
{field_name}:
- 描述: {description}
- 示例: {example}
- 格式: {format_type}
- 必填: {'是' if is_required else '否'}""")
                
                field_mapping[field_name] = field_def
            
            # 构建JSON模板
            json_template = {}
            for field_def in field_definitions:
                field_name = field_def["field_name"]
                field_type = field_def.get("type", "string")
                
                if field_type == "array" or "列表" in field_def.get("format", ""):
                    json_template[field_name] = []
                elif field_type == "object" or "表格" in field_def.get("format", ""):
                    json_template[field_name] = {}
                else:
                    json_template[field_name] = "提取的值"
            
            # 构建提取prompt
            extract_prompt = f"""
你是一个专业的文档信息提取助手。请从以下文档中提取指定的字段信息。

**提取字段定义:**
{''.join(field_descriptions)}

**文档内容:**
{document_content}

**提取要求:**
1. 仔细阅读文档内容，准确提取每个字段的信息
2. 如果某个字段在文档中找不到明确信息，请返回"未找到"
3. 对于表格类数据，请按照示例格式提取
4. 保持提取信息的准确性和完整性
5. 严格按照以下JSON格式返回结果，不要添加任何其他文本

**返回格式（必须是有效的JSON）:**
{json.dumps(json_template, ensure_ascii=False, indent=2)}
"""
            
            messages = [
                {
                    "role": "system", 
                    "content": "你是一个专业的信息提取助手，能够准确从文档中提取指定字段的信息，并严格按照JSON格式返回结果。只返回JSON，不要添加任何解释或其他文本。"
                },
                {"role": "user", "content": extract_prompt}
            ]
            
            # 调用LLM
            response = await self.llm_service.call_llm(messages)
            
            if not response.success:
                return {
                    "success": False,
                    "error": f"LLM调用失败: {response.error}",
                    "data": {},
                    "extraction_results": []
                }
            
            # 解析响应
            return await self._parse_extraction_response(
                response.content, field_definitions, workflow_id
            )
            
        except Exception as e:
            logger.error(f"[{workflow_id}] LLM提取异常: {e}")
            return {
                "success": False,
                "error": str(e),
                "data": {},
                "extraction_results": []
            }
    
    async def _parse_extraction_response(self, response_content: str, 
                                       field_definitions: List[Dict[str, Any]], 
                                       workflow_id: str) -> Dict[str, Any]:
        """
        解析LLM的提取响应
        
        Args:
            response_content: LLM响应内容
            field_definitions: 字段定义列表
            workflow_id: 工作流ID
            
        Returns:
            解析后的提取结果
        """
        try:
            # 清理响应内容
            content = response_content.strip()
            
            # 处理可能的```json标记
            if "```json" in content:
                start = content.find("```json") + 7
                end = content.find("```", start)
                if end != -1:
                    content = content[start:end].strip()
            elif "```" in content:
                # 处理只有```的情况
                parts = content.split("```")
                if len(parts) >= 3:
                    content = parts[1].strip()
            
            # 解析JSON
            extracted_data = json.loads(content)
            logger.info(f"[{workflow_id}] 成功解析提取结果")
            
            # 转换为标准格式
            extraction_results = []
            processed_data = {}
            
            for field_def in field_definitions:
                field_name = field_def["field_name"]
                
                if field_name in extracted_data:
                    raw_value = extracted_data[field_name]
                    
                    # 处理不同类型的字段
                    processed_value = self._process_field_value(
                        raw_value, field_def, workflow_id
                    )
                    
                    processed_data[field_name] = processed_value
                else:
                    processed_value = "未找到"
                    processed_data[field_name] = processed_value
                
                # 计算置信度
                confidence = self._calculate_confidence(processed_value, field_def)
                
                # 创建提取结果记录
                extraction_result = {
                    "field_name": field_name,
                    "field_value": processed_value,
                    "confidence": confidence,
                    "source_location": None,
                    "extraction_method": "llm_intelligent",
                    "is_verified": False,
                    "needs_human_input": field_def.get("must", False) and processed_value == "未找到",
                    "field_definition": field_def
                }
                
                extraction_results.append(extraction_result)
                logger.info(f"[{workflow_id}] 字段 {field_name} 提取完成: {str(processed_value)[:50]}...")
            
            return {
                "success": True,
                "data": processed_data,
                "extraction_results": extraction_results,
                "timestamp": datetime.now().isoformat()
            }
            
        except json.JSONDecodeError as e:
            logger.error(f"[{workflow_id}] JSON解析失败: {e}")
            logger.error(f"[{workflow_id}] 原始响应: {response_content[:500]}...")
            
            # 降级到简单提取
            return await self._fallback_extraction(
                response_content, field_definitions, workflow_id
            )
            
        except Exception as e:
            logger.error(f"[{workflow_id}] 响应解析异常: {e}")
            return {
                "success": False,
                "error": str(e),
                "data": {},
                "extraction_results": []
            }
    
    def _process_field_value(self, raw_value: Any, field_def: Dict[str, Any], 
                           workflow_id: str) -> str:
        """
        处理字段值，转换为合适的格式
        
        Args:
            raw_value: 原始值
            field_def: 字段定义
            workflow_id: 工作流ID
            
        Returns:
            处理后的字符串值
        """
        try:
            field_name = field_def["field_name"]
            field_format = field_def.get("format", "普通文本")
            
            if raw_value is None or raw_value == "":
                return "未找到"
            
            # 处理列表类型
            if isinstance(raw_value, list):
                if "表格" in field_format or field_name == "attend":
                    # 表格格式处理
                    formatted_items = []
                    for item in raw_value:
                        if isinstance(item, dict):
                            formatted_items.append(
                                "; ".join([f"{k}: {v}" for k, v in item.items()])
                            )
                        else:
                            formatted_items.append(str(item))
                    return "\n".join(formatted_items)
                else:
                    return "; ".join([str(item) for item in raw_value])
            
            # 处理字典类型
            elif isinstance(raw_value, dict):
                formatted_items = []
                for key, value in raw_value.items():
                    if isinstance(value, list):
                        value_str = "; ".join([str(v) for v in value])
                    else:
                        value_str = str(value)
                    formatted_items.append(f"{key}: {value_str}")
                return "\n".join(formatted_items)
            
            # 处理普通值
            else:
                return str(raw_value)
                
        except Exception as e:
            logger.error(f"[{workflow_id}] 字段值处理异常: {e}")
            return str(raw_value) if raw_value is not None else "未找到"
    
    def _calculate_confidence(self, value: str, field_def: Dict[str, Any]) -> float:
        """
        计算提取置信度
        
        Args:
            value: 提取的值
            field_def: 字段定义
            
        Returns:
            置信度分数 (0.0-1.0)
        """
        if value == "未找到" or not value.strip():
            return 0.0
        
        # 基础置信度
        confidence = 0.7
        
        # 根据字段类型调整
        if field_def.get("must", False):
            # 必填字段提高置信度要求
            confidence = 0.8
        
        # 根据内容长度调整
        if len(value) > 10:
            confidence += 0.1
        
        # 根据格式匹配调整
        field_format = field_def.get("format", "")
        if "时间" in field_format and any(char in value for char in "年月日时分"):
            confidence += 0.1
        elif "地点" in field_format and any(char in value for char in "室厅楼层"):
            confidence += 0.1
        
        return min(confidence, 1.0)
    
    async def _fallback_extraction(self, response_content: str, 
                                 field_definitions: List[Dict[str, Any]], 
                                 workflow_id: str) -> Dict[str, Any]:
        """
        降级提取方法
        
        Args:
            response_content: 响应内容
            field_definitions: 字段定义
            workflow_id: 工作流ID
            
        Returns:
            降级提取结果
        """
        logger.info(f"[{workflow_id}] 使用降级提取方法")
        
        extraction_results = []
        processed_data = {}
        
        for field_def in field_definitions:
            field_name = field_def["field_name"]
            
            # 简单的关键词匹配提取
            extracted_value = "未找到"
            confidence = 0.0
            
            # 这里可以实现简单的正则表达式或关键词匹配逻辑
            # 暂时返回未找到
            
            processed_data[field_name] = extracted_value
            
            extraction_result = {
                "field_name": field_name,
                "field_value": extracted_value,
                "confidence": confidence,
                "source_location": None,
                "extraction_method": "fallback",
                "is_verified": False,
                "needs_human_input": True,
                "field_definition": field_def
            }
            
            extraction_results.append(extraction_result)
        
        return {
            "success": True,
            "data": processed_data,
            "extraction_results": extraction_results,
            "timestamp": datetime.now().isoformat(),
            "note": "使用降级提取方法"
        }


# 全局数据提取服务实例
data_extraction_service = DataExtractionService()
