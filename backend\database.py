"""
数据库连接和会话管理模块
"""

import logging
from typing import Generator
from sqlalchemy import create_engine, MetaData
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from config import settings

# 配置日志
logger = logging.getLogger(__name__)

# 创建数据库引擎
engine = create_engine(
    settings.get_database_url(),
    echo=settings.db_echo,
    pool_pre_ping=True,
    pool_recycle=300,
    # 对于SQLite，使用StaticPool
    poolclass=StaticPool if settings.get_database_url().startswith("sqlite") else None,
    connect_args={"check_same_thread": False} if settings.get_database_url().startswith("sqlite") else {}
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建元数据对象
metadata = MetaData()


def get_db() -> Generator[Session, None, None]:
    """
    获取数据库会话
    
    Yields:
        Session: 数据库会话对象
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"数据库会话错误: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def create_tables() -> None:
    """
    创建所有数据库表
    """
    try:
        # 导入所有模型以确保它们被注册
        from models.core import Base
        from models import user, template, data, report, agent, dialogue

        Base.metadata.create_all(bind=engine)
        logger.info("数据库表创建成功")
    except Exception as e:
        logger.error(f"数据库表创建失败: {e}")
        raise


def drop_tables() -> None:
    """
    删除所有数据库表
    """
    try:
        from models.core import Base
        Base.metadata.drop_all(bind=engine)
        logger.info("数据库表删除成功")
    except Exception as e:
        logger.error(f"数据库表删除失败: {e}")
        raise


def check_database_connection() -> bool:
    """
    检查数据库连接是否正常
    
    Returns:
        bool: 连接是否正常
    """
    try:
        with engine.connect() as connection:
            connection.execute("SELECT 1")
        logger.info("数据库连接正常")
        return True
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return False


def get_database_info() -> dict:
    """
    获取数据库信息
    
    Returns:
        dict: 数据库信息
    """
    try:
        with engine.connect() as connection:
            result = connection.execute("SELECT version()")
            version = result.fetchone()[0] if result.rowcount > 0 else "Unknown"
            
        return {
            "url": settings.get_database_url(),
            "version": version,
            "echo": settings.db_echo,
            "connected": True
        }
    except Exception as e:
        logger.error(f"获取数据库信息失败: {e}")
        return {
            "url": settings.get_database_url(),
            "version": "Unknown",
            "echo": settings.db_echo,
            "connected": False,
            "error": str(e)
        }


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.engine = engine
        self.SessionLocal = SessionLocal
        
    def get_session(self) -> Session:
        """获取数据库会话"""
        return SessionLocal()
        
    def create_all_tables(self) -> None:
        """创建所有表"""
        create_tables()
        
    def drop_all_tables(self) -> None:
        """删除所有表"""
        drop_tables()
        
    def check_connection(self) -> bool:
        """检查连接"""
        return check_database_connection()
        
    def get_info(self) -> dict:
        """获取数据库信息"""
        return get_database_info()


# 创建全局数据库管理器实例
db_manager = DatabaseManager()
