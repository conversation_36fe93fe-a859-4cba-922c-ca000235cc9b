:root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color: #333;
  background-color: #ffffff;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  width: 100vw;
}

/* Ant Design会处理大部分样式，这里只保留必要的全局样式 */

#root {
  width: 100vw;
  min-height: 100vh;
}

/* 确保Ant Design组件正常显示 */
.ant-layout {
  width: 100%;
  min-height: 100vh;
}

/* 移除默认的按钮样式，让Ant Design接管 */
button {
  all: unset;
}
