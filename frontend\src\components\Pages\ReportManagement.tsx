/**
 * 报告管理页面
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Statistic,
  Progress,
  Tooltip,
  Tabs,
  List,
  Avatar
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  FileTextOutlined,
  EyeOutlined,
  DownloadOutlined,
  ShareAltOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SyncOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { TabPane } = Tabs;

interface GeneratedReport {
  id: string;
  name: string;
  templateId: string;
  templateName: string;
  dataId: string;
  dataName: string;
  status: 'generating' | 'completed' | 'failed' | 'reviewing';
  progress: number;
  quality: number;
  createdAt: Date;
  updatedAt: Date;
  fileSize: number;
  downloadCount: number;
  creator: string;
}

interface ReportHistory {
  id: string;
  reportId: string;
  action: string;
  description: string;
  timestamp: Date;
  user: string;
}

const ReportManagement: React.FC = () => {
  const [reports, setReports] = useState<GeneratedReport[]>([]);
  const [reportHistory, setReportHistory] = useState<ReportHistory[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingReport, setEditingReport] = useState<GeneratedReport | null>(null);
  const [activeTab, setActiveTab] = useState('reports');
  const [form] = Form.useForm();

  // 模拟数据
  useEffect(() => {
    const mockReports: GeneratedReport[] = [
      {
        id: '1',
        name: '数字科技中心2024年第1次会议纪要',
        templateId: '1',
        templateName: '数字科技中心会议纪要',
        dataId: '1',
        dataName: '2024年1月15日会议数据',
        status: 'completed',
        progress: 100,
        quality: 95,
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-01-15'),
        fileSize: 45600,
        downloadCount: 12,
        creator: '张三'
      },
      {
        id: '2',
        name: '项目预审会议纪要',
        templateId: '2',
        templateName: '项目预审会纪要',
        dataId: '2',
        dataName: '项目预审会数据',
        status: 'reviewing',
        progress: 85,
        quality: 88,
        createdAt: new Date('2024-01-12'),
        updatedAt: new Date('2024-01-13'),
        fileSize: 38400,
        downloadCount: 5,
        creator: '李四'
      },
      {
        id: '3',
        name: '技术评审报告',
        templateId: '3',
        templateName: '技术评审报告',
        dataId: '3',
        dataName: '技术评审数据',
        status: 'generating',
        progress: 45,
        quality: 0,
        createdAt: new Date('2024-01-10'),
        updatedAt: new Date('2024-01-10'),
        fileSize: 0,
        downloadCount: 0,
        creator: '王五'
      },
      {
        id: '4',
        name: '失败的报告生成',
        templateId: '1',
        templateName: '数字科技中心会议纪要',
        dataId: '1',
        dataName: '2024年1月15日会议数据',
        status: 'failed',
        progress: 30,
        quality: 0,
        createdAt: new Date('2024-01-08'),
        updatedAt: new Date('2024-01-08'),
        fileSize: 0,
        downloadCount: 0,
        creator: '赵六'
      }
    ];

    const mockHistory: ReportHistory[] = [
      {
        id: '1',
        reportId: '1',
        action: 'created',
        description: '创建报告生成任务',
        timestamp: new Date('2024-01-15 09:00:00'),
        user: '张三'
      },
      {
        id: '2',
        reportId: '1',
        action: 'completed',
        description: '报告生成完成',
        timestamp: new Date('2024-01-15 09:15:00'),
        user: '系统'
      },
      {
        id: '3',
        reportId: '1',
        action: 'downloaded',
        description: '报告被下载',
        timestamp: new Date('2024-01-15 10:30:00'),
        user: '张三'
      },
      {
        id: '4',
        reportId: '2',
        action: 'created',
        description: '创建报告生成任务',
        timestamp: new Date('2024-01-12 14:00:00'),
        user: '李四'
      },
      {
        id: '5',
        reportId: '2',
        action: 'reviewing',
        description: '报告进入人工审核阶段',
        timestamp: new Date('2024-01-13 10:00:00'),
        user: '系统'
      }
    ];

    setReports(mockReports);
    setReportHistory(mockHistory);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'generating':
        return <SyncOutlined spin style={{ color: '#1890ff' }} />;
      case 'completed':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'failed':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'reviewing':
        return <ClockCircleOutlined style={{ color: '#faad14' }} />;
      default:
        return null;
    }
  };

  const getStatusText = (status: string) => {
    const statusMap = {
      generating: '生成中',
      completed: '已完成',
      failed: '生成失败',
      reviewing: '审核中'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  const getStatusColor = (status: string) => {
    const colorMap = {
      generating: 'blue',
      completed: 'green',
      failed: 'red',
      reviewing: 'orange'
    };
    return colorMap[status as keyof typeof colorMap] || 'default';
  };

  const columns = [
    {
      title: '报告名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: GeneratedReport) => (
        <Space>
          <FileTextOutlined style={{ color: '#1890ff' }} />
          <span style={{ fontWeight: 500 }}>{text}</span>
        </Space>
      ),
    },
    {
      title: '模板',
      dataIndex: 'templateName',
      key: 'templateName',
      render: (templateName: string) => <Tag color="blue">{templateName}</Tag>,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: GeneratedReport) => (
        <Space>
          {getStatusIcon(status)}
          <Tag color={getStatusColor(status)}>{getStatusText(status)}</Tag>
          {status === 'generating' && (
            <Progress
              percent={record.progress}
              size="small"
              style={{ width: '80px' }}
              showInfo={false}
            />
          )}
        </Space>
      ),
    },
    {
      title: '质量评分',
      dataIndex: 'quality',
      key: 'quality',
      render: (quality: number, record: GeneratedReport) => {
        if (record.status !== 'completed') return '-';
        return (
          <Tooltip title={`质量评分: ${quality}分`}>
            <Progress
              percent={quality}
              size="small"
              style={{ width: '60px' }}
              strokeColor={quality >= 90 ? '#52c41a' : quality >= 70 ? '#faad14' : '#ff4d4f'}
            />
          </Tooltip>
        );
      },
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      key: 'fileSize',
      render: (size: number) => {
        if (size === 0) return '-';
        return <Text type="secondary">{(size / 1024).toFixed(1)} KB</Text>;
      },
    },
    {
      title: '下载次数',
      dataIndex: 'downloadCount',
      key: 'downloadCount',
      render: (count: number) => <Text type="secondary">{count} 次</Text>,
    },
    {
      title: '创建者',
      dataIndex: 'creator',
      key: 'creator',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: Date) => date.toLocaleDateString('zh-CN'),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: GeneratedReport) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewReport(record)}
          >
            查看
          </Button>
          {record.status === 'completed' && (
            <>
              <Button
                type="text"
                icon={<DownloadOutlined />}
                onClick={() => handleDownloadReport(record)}
              >
                下载
              </Button>
              <Button
                type="text"
                icon={<ShareAltOutlined />}
                onClick={() => handleShareReport(record)}
              >
                分享
              </Button>
            </>
          )}
          {record.status === 'failed' && (
            <Button
              type="text"
              icon={<SyncOutlined />}
              onClick={() => handleRetryGeneration(record)}
            >
              重试
            </Button>
          )}
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditReport(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个报告吗？"
            onConfirm={() => handleDeleteReport(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleViewReport = (report: GeneratedReport) => {
    message.info(`查看报告: ${report.name}`);
  };

  const handleDownloadReport = (report: GeneratedReport) => {
    // 更新下载次数
    setReports(prev => prev.map(r =>
      r.id === report.id
        ? { ...r, downloadCount: r.downloadCount + 1 }
        : r
    ));
    message.success(`下载报告: ${report.name}`);
  };

  const handleShareReport = (report: GeneratedReport) => {
    message.success(`分享报告: ${report.name}`);
  };

  const handleRetryGeneration = (report: GeneratedReport) => {
    setReports(prev => prev.map(r =>
      r.id === report.id
        ? { ...r, status: 'generating', progress: 0, updatedAt: new Date() }
        : r
    ));
    message.success('重新开始生成报告');
  };

  const handleEditReport = (report: GeneratedReport) => {
    setEditingReport(report);
    form.setFieldsValue(report);
    setModalVisible(true);
  };

  const handleDeleteReport = (id: string) => {
    setReports(prev => prev.filter(r => r.id !== id));
    message.success('报告删除成功');
  };

  const handleAddReport = () => {
    setEditingReport(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();

      if (editingReport) {
        // 编辑报告
        setReports(prev => prev.map(r =>
          r.id === editingReport.id
            ? { ...r, ...values, updatedAt: new Date() }
            : r
        ));
        message.success('报告更新成功');
      } else {
        // 新增报告
        const newReport: GeneratedReport = {
          id: Date.now().toString(),
          ...values,
          status: 'generating',
          progress: 0,
          quality: 0,
          createdAt: new Date(),
          updatedAt: new Date(),
          fileSize: 0,
          downloadCount: 0,
          creator: '当前用户'
        };
        setReports(prev => [...prev, newReport]);
        message.success('报告创建成功');
      }

      setModalVisible(false);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleModalCancel = () => {
    setModalVisible(false);
  };

  // 统计数据
  const stats = {
    total: reports.length,
    completed: reports.filter(r => r.status === 'completed').length,
    generating: reports.filter(r => r.status === 'generating').length,
    failed: reports.filter(r => r.status === 'failed').length,
    totalDownloads: reports.reduce((sum, r) => sum + r.downloadCount, 0)
  };

  return (
    <div>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={5}>
          <Card>
            <Statistic title="总报告数" value={stats.total} />
          </Card>
        </Col>
        <Col span={5}>
          <Card>
            <Statistic title="已完成" value={stats.completed} />
          </Card>
        </Col>
        <Col span={5}>
          <Card>
            <Statistic title="生成中" value={stats.generating} />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic title="失败" value={stats.failed} />
          </Card>
        </Col>
        <Col span={5}>
          <Card>
            <Statistic title="总下载量" value={stats.totalDownloads} />
          </Card>
        </Col>
      </Row>

      {/* 报告管理标签页 */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <span>
                <FileTextOutlined />
                报告列表
              </span>
            }
            key="reports"
          >
            <div style={{ marginBottom: '16px' }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddReport}
              >
                新增报告
              </Button>
            </div>

            <Table
              columns={columns}
              dataSource={reports}
              rowKey="id"
              loading={loading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个报告`,
              }}
            />
          </TabPane>

          <TabPane
            tab={
              <span>
                <ClockCircleOutlined />
                操作历史
              </span>
            }
            key="history"
          >
            <List
              dataSource={reportHistory}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<ClockCircleOutlined />} />}
                    title={
                      <Space>
                        <Text strong>{item.action}</Text>
                        <Text type="secondary">by {item.user}</Text>
                      </Space>
                    }
                    description={
                      <div>
                        <div>{item.description}</div>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {item.timestamp.toLocaleString('zh-CN')}
                        </Text>
                      </div>
                    }
                  />
                </List.Item>
              )}
              pagination={{
                pageSize: 10,
                showSizeChanger: false,
                showQuickJumper: true,
              }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 新增/编辑报告弹窗 */}
      <Modal
        title={editingReport ? '编辑报告' : '新增报告'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            status: 'generating'
          }}
        >
          <Form.Item
            name="name"
            label="报告名称"
            rules={[{ required: true, message: '请输入报告名称' }]}
          >
            <Input placeholder="请输入报告名称" />
          </Form.Item>

          <Form.Item
            name="templateId"
            label="使用模板"
            rules={[{ required: true, message: '请选择模板' }]}
          >
            <Select placeholder="请选择模板">
              <Select.Option value="1">数字科技中心会议纪要</Select.Option>
              <Select.Option value="2">项目预审会纪要</Select.Option>
              <Select.Option value="3">技术评审报告</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="dataId"
            label="数据源"
            rules={[{ required: true, message: '请选择数据源' }]}
          >
            <Select placeholder="请选择数据源">
              <Select.Option value="1">2024年1月15日会议数据</Select.Option>
              <Select.Option value="2">项目预审会数据</Select.Option>
              <Select.Option value="3">技术评审数据</Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ReportManagement;
