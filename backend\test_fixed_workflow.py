#!/usr/bin/env python3
"""
测试修复后的工作流程
"""

import requests
import tempfile
import os
import json
import time

def test_fixed_workflow():
    """测试修复后的工作流程"""
    
    # 创建测试文档
    test_content = """
    项目进度会议纪要
    
    会议时间：2025年1月20日 上午10:00-11:30
    会议地点：会议室B201
    主持人：李项目经理
    参会人员：张工程师、王设计师、陈测试员、刘产品经理
    
    会议议题：
    1. 第一季度项目进展回顾
    2. 当前遇到的技术挑战
    3. 下一阶段工作计划
    
    讨论内容：
    1. 项目整体进度符合预期，完成度达到75%
    2. 数据库性能优化需要额外关注
    3. 用户界面设计需要进一步完善
    
    决议事项：
    1. 张工程师负责数据库优化，预计2周完成
    2. 王设计师优化UI设计，下周五前提交方案
    3. 陈测试员制定详细测试计划
    4. 下次会议时间：2025年1月27日 上午10:00
    
    会议记录人：刘产品经理
    """
    
    print("🚀 开始测试修复后的工作流程...")
    
    # 第1步: 启动工作流
    print("\n📝 第1步: 启动工作流...")
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(test_content)
        temp_file_path = f.name
    
    try:
        with open(temp_file_path, 'rb') as f:
            files = {'files': ('project_meeting.txt', f, 'text/plain')}
            data = {'text_input': '请帮我生成会议纪要'}
            
            response = requests.post(
                'http://localhost:8000/api/workflow/langgraph/start',
                files=files,
                data=data
            )
        
        print(f"启动状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            workflow_id = result.get('workflow_id')
            print(f"工作流ID: {workflow_id}")
            print(f"工作流状态: {result.get('status')}")
            print(f"消息: {result.get('message', '')[:100]}...")
            
            requires_user_action = result.get('data', {}).get('requires_user_action', False)
            print(f"需要用户操作: {requires_user_action}")
            
            if requires_user_action:
                print("\n✅ 工作流正确暂停，等待用户确认模板")
                
                # 第2步: 确认模板
                print("\n📋 第2步: 确认推荐的模板...")
                time.sleep(2)  # 等待2秒
                
                confirm_response = requests.post(
                    f'http://localhost:8000/api/workflow/langgraph/resume/{workflow_id}',
                    json={'action': 'confirm'}
                )
                
                print(f"模板确认状态码: {confirm_response.status_code}")
                if confirm_response.status_code == 200:
                    confirm_result = confirm_response.json()
                    print(f"确认状态: {confirm_result.get('status')}")
                    
                    # 第3步: 等待数据提取完成
                    print("\n🔍 第3步: 等待数据提取完成...")
                    
                    # 等待数据提取完成
                    for i in range(15):  # 最多等待15次，每次2秒
                        time.sleep(2)
                        print(f"  检查状态 ({i+1}/15)...")
                        
                        status_response = requests.get(
                            f'http://localhost:8000/api/workflow/langgraph/status/{workflow_id}'
                        )
                        
                        if status_response.status_code == 200:
                            status_data = status_response.json()
                            current_stage = status_data.get('current_stage')
                            pending_interactions = status_data.get('pending_interactions', [])
                            extracted_data = status_data.get('extracted_data', {})
                            extraction_results = status_data.get('extraction_results', [])
                            requires_user_action = status_data.get('requires_user_action', False)
                            
                            print(f"    当前阶段: {current_stage}")
                            print(f"    待处理交互: {pending_interactions}")
                            print(f"    需要用户操作: {requires_user_action}")
                            print(f"    提取数据数量: {len(extracted_data)}")
                            print(f"    提取结果数量: {len(extraction_results)}")
                            
                            # 如果需要用户操作，说明数据提取完成
                            if requires_user_action and pending_interactions:
                                print(f"\n🎉 数据提取完成！需要用户确认数据")
                                print(f"待处理交互类型: {pending_interactions}")
                                
                                # 显示提取的数据
                                if extraction_results:
                                    print("\n📊 提取的数据 (前5个):")
                                    for result in extraction_results[:5]:
                                        field_name = result.get('field_name', 'unknown')
                                        field_value = result.get('field_value', 'N/A')
                                        confidence = result.get('confidence', 0)
                                        print(f"  {field_name}: {field_value} (置信度: {confidence:.2f})")
                                
                                # 第4步: 确认数据
                                print("\n✅ 第4步: 确认提取的数据...")
                                data_confirm_response = requests.post(
                                    f'http://localhost:8000/api/workflow/langgraph/resume/{workflow_id}',
                                    json={'action': 'confirm'}
                                )
                                
                                if data_confirm_response.status_code == 200:
                                    print("✅ 数据确认成功！工作流应该继续执行...")
                                    
                                    # 等待报告生成完成
                                    print("\n📄 第5步: 等待报告生成...")
                                    for j in range(10):  # 等待报告生成
                                        time.sleep(3)
                                        print(f"  检查报告生成状态 ({j+1}/10)...")
                                        
                                        final_status_response = requests.get(
                                            f'http://localhost:8000/api/workflow/langgraph/status/{workflow_id}'
                                        )
                                        
                                        if final_status_response.status_code == 200:
                                            final_status = final_status_response.json()
                                            final_stage = final_status.get('current_stage')
                                            final_pending = final_status.get('pending_interactions', [])
                                            
                                            print(f"    最终阶段: {final_stage}")
                                            print(f"    最终待处理交互: {final_pending}")
                                            
                                            if not final_pending:
                                                print("🎉 工作流完全完成！")
                                                break
                                    else:
                                        print("⚠️ 报告生成等待超时")
                                else:
                                    print(f"❌ 数据确认失败: {data_confirm_response.status_code}")
                                
                                break
                            
                            # 如果没有待处理交互但有提取数据，可能已经完成
                            elif extracted_data or extraction_results:
                                print(f"\n✅ 数据提取完成，无需用户确认")
                                break
                        else:
                            print(f"    ❌ 状态检查失败: {status_response.status_code}")
                    else:
                        print("\n⚠️ 等待超时，数据提取可能仍在进行中")
                        
                else:
                    print(f"❌ 模板确认失败: {confirm_response.status_code}")
                    print(confirm_response.text)
            else:
                print("❌ 工作流没有正确暂停等待用户输入")
        else:
            print(f"❌ 启动工作流失败: {response.status_code}")
            print(response.text)
            
    finally:
        # 清理临时文件
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
    
    print("\n✅ 修复后的工作流测试完成")

if __name__ == "__main__":
    test_fixed_workflow()
