[project]
name = "backend"
version = "0.1.0"
description = "超限报告智能生成工具后端服务"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "alembic>=1.13.0",
    "celery>=5.3.0",
    "fastapi>=0.110.0",
    "httpx>=0.27.0",
    "langgraph>=0.1.0",
    "psycopg2-binary>=2.9.0",
    "pydantic>=2.6.0",
    "pydantic-settings>=2.0.0",
    "pytest>=8.0.0",
    "python-dotenv>=1.0.0",
    "python-docx>=1.1.0",
    "python-multipart>=0.0.6",
    "redis>=5.0.0",
    "sqlalchemy>=2.0.0",
    "uvicorn[standard]>=0.29.0",
    "fastapi-cors>=0.0.6",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "aiofiles>=23.0.0",
    "pillow>=10.0.0",
    "dashscope>=1.17.0",
    "langchain>=0.1.0",
    "langchain-community>=0.0.10",
    "openai>=1.0.0",
]

[project.optional-dependencies]
dev = [
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.0.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["."]

[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app"]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["test"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
