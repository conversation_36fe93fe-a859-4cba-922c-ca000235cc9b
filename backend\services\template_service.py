#!/usr/bin/env python3
"""
模板管理服务
负责动态读取和管理模板定义、关键字定义等文件
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any
from pathlib import Path
import re

logger = logging.getLogger(__name__)

class TemplateService:
    """模板管理服务"""
    
    def __init__(self, templates_dir: str = "templates"):
        """
        初始化模板服务

        Args:
            templates_dir: 模板文件夹路径
        """
        # 处理相对路径
        if not os.path.isabs(templates_dir):
            # 如果是相对路径，相对于当前工作目录
            current_dir = Path.cwd()
            self.templates_dir = current_dir / templates_dir
        else:
            self.templates_dir = Path(templates_dir)

        self.keysdefinition_dir = self.templates_dir / "keysdefinition"
        self.keysextracted_dir = self.templates_dir / "keysextracted"
        self.wordtemplates_dir = self.templates_dir / "wordtemplates"

        # 确保目录存在
        self._ensure_directories()
    
    def _ensure_directories(self):
        """确保所有必要的目录存在"""
        for dir_path in [self.keysdefinition_dir, self.keysextracted_dir, self.wordtemplates_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def get_available_templates(self) -> List[Dict[str, Any]]:
        """
        获取所有可用的模板列表
        
        Returns:
            模板信息列表
        """
        templates = []
        
        # 扫描关键字定义文件
        for key_def_file in self.keysdefinition_dir.glob("*.md"):
            template_info = self._parse_template_from_key_definition(key_def_file)
            if template_info:
                templates.append(template_info)
        
        return templates
    
    def _parse_template_from_key_definition(self, key_def_file: Path) -> Optional[Dict[str, Any]]:
        """
        从关键字定义文件解析模板信息
        
        Args:
            key_def_file: 关键字定义文件路径
            
        Returns:
            模板信息字典
        """
        try:
            # 查找对应的Word模板文件
            template_name = key_def_file.stem.replace("key_definitions", "template")
            word_template = None
            
            # 在wordtemplates目录中查找匹配的模板文件
            for template_file in self.wordtemplates_dir.glob("*.docx"):
                if "会议纪要" in template_file.name or "meeting" in template_file.name.lower():
                    word_template = template_file
                    break
            
            if not word_template:
                logger.warning(f"未找到对应的Word模板文件: {key_def_file}")
                return None
            
            # 解析关键字定义
            field_definitions = self.parse_key_definitions(key_def_file)
            
            template_info = {
                "template_id": key_def_file.stem,
                "template_name": "会议纪要模板",  # 可以从文件内容中解析
                "template_path": str(word_template),
                "key_definitions_path": str(key_def_file),
                "field_definitions": field_definitions,
                "placeholder_count": len(field_definitions),
                "description": "动态加载的会议纪要模板"
            }
            
            return template_info
            
        except Exception as e:
            logger.error(f"解析模板信息失败: {key_def_file}, 错误: {e}")
            return None
    
    def parse_key_definitions(self, key_def_file: Path) -> List[Dict[str, Any]]:
        """
        解析关键字定义文件
        
        Args:
            key_def_file: 关键字定义文件路径
            
        Returns:
            字段定义列表
        """
        field_definitions = []
        
        try:
            with open(key_def_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 按字段分割内容
            field_sections = re.split(r'\n(?=# \w+)', content)
            
            for section in field_sections:
                if not section.strip():
                    continue
                
                field_def = self._parse_field_section(section)
                if field_def:
                    field_definitions.append(field_def)
            
            logger.info(f"成功解析 {len(field_definitions)} 个字段定义")
            return field_definitions
            
        except Exception as e:
            logger.error(f"解析关键字定义文件失败: {e}")
            return []
    
    def _parse_field_section(self, section: str) -> Optional[Dict[str, Any]]:
        """
        解析单个字段定义段落

        Args:
            section: 字段定义段落文本

        Returns:
            字段定义字典
        """
        lines = section.strip().split('\n')
        if not lines:
            return None

        # 提取字段名（第一行的#后面的内容）
        field_name_match = re.match(r'^#\s*(\w+)', lines[0])
        if not field_name_match:
            return None

        field_name = field_name_match.group(1)

        # 解析字段属性
        field_def = {
            "field_name": field_name,
            "description": "",
            "example": "",
            "format": "普通文本",
            "must": False,
            "type": "string",
            "default": "",
            "length": "",
            "source": ""
        }

        for line in lines[1:]:
            line = line.strip()
            if not line:
                continue

            # 解析属性行（支持两种格式：key:value 和 - key: value）
            attr_match = re.match(r'^(?:-\s*)?(\w+):\s*(.*)', line)
            if attr_match:
                key = attr_match.group(1)
                value = attr_match.group(2).strip()

                if key in field_def:
                    field_def[key] = value

        # 处理特殊属性
        if 'must' in field_def:
            field_def['must'] = field_def['must'].lower() in ['true', 'yes', '是', '必填']

        # 根据字段名推断类型
        if field_name in ['attend', 'detail']:
            field_def['type'] = 'array'

        return field_def
    
    def save_extracted_keys(self, template_id: str, extracted_data: Dict[str, Any], 
                           workflow_id: str) -> str:
        """
        保存提取的关键信息到文件
        
        Args:
            template_id: 模板ID
            extracted_data: 提取的数据
            workflow_id: 工作流ID
            
        Returns:
            保存的文件路径
        """
        try:
            # 生成文件名
            filename = f"{workflow_id}_{template_id}_extracted.md"
            file_path = self.keysextracted_dir / filename
            
            # 生成Markdown格式的内容
            content = self._format_extracted_data_to_markdown(extracted_data)
            
            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info(f"提取结果已保存到: {file_path}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"保存提取结果失败: {e}")
            raise
    
    def _format_extracted_data_to_markdown(self, extracted_data: Dict[str, Any]) -> str:
        """
        将提取的数据格式化为Markdown格式
        
        Args:
            extracted_data: 提取的数据
            
        Returns:
            Markdown格式的内容
        """
        content_lines = []
        
        for field_name, field_value in extracted_data.items():
            content_lines.append(f"# {field_name}")
            
            if isinstance(field_value, list):
                # 处理列表类型的数据
                for item in field_value:
                    if isinstance(item, dict):
                        for key, value in item.items():
                            content_lines.append(f"## {key}")
                            content_lines.append(str(value))
                    else:
                        content_lines.append(f"- {item}")
            else:
                content_lines.append(str(field_value))
            
            content_lines.append("")  # 空行分隔
        
        return '\n'.join(content_lines)
    
    def load_extracted_keys(self, file_path: str) -> Dict[str, Any]:
        """
        从文件加载提取的关键信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            提取的数据字典
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return self._parse_extracted_markdown(content)
            
        except Exception as e:
            logger.error(f"加载提取结果失败: {e}")
            return {}
    
    def _parse_extracted_markdown(self, content: str) -> Dict[str, Any]:
        """
        解析Markdown格式的提取结果
        
        Args:
            content: Markdown内容
            
        Returns:
            解析后的数据字典
        """
        data = {}
        current_field = None
        current_value = []
        
        for line in content.split('\n'):
            line = line.strip()
            
            if line.startswith('# '):
                # 保存之前的字段
                if current_field and current_value:
                    data[current_field] = '\n'.join(current_value).strip()
                
                # 开始新字段
                current_field = line[2:].strip()
                current_value = []
            elif current_field and line:
                current_value.append(line)
        
        # 保存最后一个字段
        if current_field and current_value:
            data[current_field] = '\n'.join(current_value).strip()
        
        return data


# 全局模板服务实例
template_service = TemplateService()
