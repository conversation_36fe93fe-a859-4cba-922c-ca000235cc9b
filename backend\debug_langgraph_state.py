#!/usr/bin/env python3
"""
调试LangGraph状态存储
直接检查LangGraph的检查点存储
"""

import asyncio
from agents.report_generation_graph import ReportGenerationGraph

async def debug_langgraph_state():
    """调试LangGraph状态存储"""
    
    print("🔍 调试LangGraph状态存储...")
    
    # 创建工作流图实例
    report_graph = ReportGenerationGraph()
    
    # 测试的工作流ID
    workflow_id = "2c226c88-d720-494d-8a56-f43cd555d891"
    
    print(f"检查工作流ID: {workflow_id}")
    
    try:
        # 创建配置
        config = {"configurable": {"thread_id": workflow_id}}
        
        # 获取当前状态
        current_state = await report_graph.workflow_graph.aget_state(config)
        
        print(f"状态对象类型: {type(current_state)}")
        print(f"状态对象属性: {dir(current_state)}")
        
        if current_state:
            print(f"状态值: {current_state.values}")
            print(f"下一步: {current_state.next}")
            print(f"配置: {current_state.config}")
            print(f"元数据: {current_state.metadata}")
            
            # 检查状态值的内容
            if current_state.values:
                state_values = current_state.values
                print(f"\n📊 状态详细信息:")
                print(f"  workflow_id: {state_values.get('workflow_id')}")
                print(f"  current_stage: {state_values.get('current_stage')}")
                print(f"  next_action: {state_values.get('next_action')}")
                print(f"  pending_interactions: {state_values.get('pending_interactions', [])}")
                print(f"  human_interactions数量: {len(state_values.get('human_interactions', []))}")
                print(f"  extraction_results数量: {len(state_values.get('extraction_results', []))}")
                print(f"  extracted_data数量: {len(state_values.get('extracted_data', {}))}")
                print(f"  errors: {state_values.get('errors', [])}")
                
                # 显示人机交互详情
                human_interactions = state_values.get('human_interactions', [])
                if human_interactions:
                    print(f"\n🤝 人机交互详情:")
                    for i, interaction in enumerate(human_interactions):
                        print(f"  交互 {i+1}:")
                        print(f"    类型: {interaction.interaction_type}")
                        print(f"    字段: {interaction.field_name}")
                        print(f"    完成状态: {interaction.is_completed}")
                        print(f"    时间戳: {interaction.timestamp}")
                
                # 显示提取结果
                extraction_results = state_values.get('extraction_results', [])
                if extraction_results:
                    print(f"\n📋 提取结果 (前5个):")
                    for i, result in enumerate(extraction_results[:5]):
                        print(f"  结果 {i+1}:")
                        print(f"    字段: {result.get('field_name', 'unknown')}")
                        print(f"    值: {result.get('field_value', 'N/A')}")
                        print(f"    置信度: {result.get('confidence', 0):.2f}")
            else:
                print("❌ 状态值为空")
        else:
            print("❌ 没有找到状态")
            
        # 检查检查点存储器中的所有线程
        print(f"\n🗂️ 检查点存储器信息:")
        checkpointer = report_graph.workflow_checkpointer
        print(f"检查点存储器类型: {type(checkpointer)}")
        
        # 如果是InMemorySaver，检查内部存储
        if hasattr(checkpointer, 'storage'):
            storage = checkpointer.storage
            print(f"存储的线程数量: {len(storage)}")
            print(f"存储的线程ID: {list(storage.keys())}")
            
            if workflow_id in storage:
                thread_data = storage[workflow_id]
                print(f"线程 {workflow_id} 的检查点数量: {len(thread_data)}")
                
                # 显示最新的检查点
                if thread_data:
                    latest_checkpoint = max(thread_data.items())[1]
                    print(f"最新检查点: {latest_checkpoint}")
            else:
                print(f"❌ 线程 {workflow_id} 不在存储中")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_langgraph_state())
