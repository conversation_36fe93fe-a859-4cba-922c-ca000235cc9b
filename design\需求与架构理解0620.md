# 需求与架构理解文档（0620版）

## 一、业务目标与核心场景
- 服务对象：广州地铁设计院内部用户。
- 核心目标：提升设计报告编写效率，减少排版和信息整理的人工工作量，规范报告内容。
- 主要痛点：报告排版耗时长、编辑工作量大、信息来源杂乱，筛选整理时间长。

## 二、核心功能与优先级
1. 关键信息自动提取
2. 报告信息合成
3. 模板管理
4. 结果管理
5. 模板智能识别
> 初步阶段重点：仅支持doc/docx文件，严格基于模板格式生成报告，避免重新排版。

## 三、模板与数据结构
- 支持多种报告模板，模板中包含结构化占位符（如：{{字段名}}）。
- 模板可通过AI自动识别、人工编辑生成并上传。
- 每个模板配套结构化描述（如JSON Schema），包含字段名称、类型、是否重复、示例等。
- 字段类型：单一字段、重复字段（暂不考虑可选、嵌套字段）。

## 四、操作流程
1. 用户登录
2. 选择报告类型
3. 上传碎片化信息或文字
4. 系统根据模板自动提取关键信息
5. 实时反馈提取内容，用户可修改
6. 信息确认，系统校验信息完整性（可用默认值补全）
7. 合成报告，用户下载

## 五、AI与智能化
- AI应用场景：信息提取、信息合成、信息修改、文档检查（错别字为主，后续扩展敏感词、格式规范等）。
- 技术要求：生成内容严格符合模板格式，暂不考虑AI自动生成排版。

## 六、用户与权限
- 当前阶段不做多角色鉴权，后续逐步完善。

## 七、界面与交互
- 平台类型：网页端，无移动端需求。
- 支持格式：文字片段、docx、doc。
- 交互方式：类似大模型网页端（如Cursor），以对话式交互为主。

## 八、部署与运维
- 部署环境：云端服务器。
- 安全与权限：后续考虑文档生成授权机制。

## 九、系统集成与用户规模
- 第三方集成：暂不考虑，未来对接微信。
- 用户规模：日活10人以内，当前仅小范围测试。

## 十、模板标注行业建议
- 推荐采用"文档内占位符+结构化JSON Schema+可视化标注工具"三结合方式，兼容AI与人工，便于后续扩展和维护。
- 可先实现基础的"{{字段名}}"占位符识别，后续逐步完善可视化标注和AI自动标注能力。

## 十一、后续建议
- 明确模板字段命名规范、类型定义、示例数据。
- 设计模板标注工具原型界面，确定字段标注流程。
- 梳理典型报告模板和样例数据，便于开发和测试。 