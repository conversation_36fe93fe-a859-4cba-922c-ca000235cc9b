#!/usr/bin/env python3
"""
文档处理服务测试脚本
"""

import os
from pathlib import Path

def create_test_documents():
    """创建测试文档"""
    test_dir = Path("test/documents")
    test_dir.mkdir(exist_ok=True)
    
    # 创建测试txt文件
    txt_content = """会议纪要

会议主题：数字化转型项目进展讨论
会议时间：2024年7月4日 14:00-16:00
会议地点：总部大楼A座会议室
主持人：张总经理

参会人员：
- 李技术总监
- 王项目经理
- 赵财务经理
- 钱人事经理

会议内容：

一、项目进展汇报
1. 系统开发完成度：75%
2. 核心功能模块已完成
3. 预计完成时间：2024年8月31日

二、存在问题
1. 数据迁移遇到技术难题
2. 用户培训计划需要调整
3. 预算执行需要优化

三、下一步计划
1. 加强技术攻关
2. 制定详细培训方案
3. 优化预算分配

四、决议事项
1. 批准增加技术人员2名
2. 调整项目里程碑
3. 加强质量控制

下次会议时间：2024年8月1日 14:00"""
    
    txt_file = test_dir / "test_meeting.txt"
    with open(txt_file, 'w', encoding='utf-8') as f:
        f.write(txt_content)
    
    print(f"✅ 创建测试txt文件: {txt_file}")
    
    # 创建测试docx文件（使用python-docx）
    try:
        from docx import Document
        
        doc = Document()
        
        # 添加标题
        doc.add_heading('项目报告', 0)
        
        # 添加基本信息
        doc.add_heading('项目基本信息', level=1)
        p = doc.add_paragraph()
        p.add_run('项目名称：').bold = True
        p.add_run('智能报告生成系统')
        
        p = doc.add_paragraph()
        p.add_run('项目负责人：').bold = True
        p.add_run('李明')
        
        p = doc.add_paragraph()
        p.add_run('开始时间：').bold = True
        p.add_run('2024年6月1日')
        
        p = doc.add_paragraph()
        p.add_run('预计完成时间：').bold = True
        p.add_run('2024年8月31日')
        
        # 添加项目描述
        doc.add_heading('项目描述', level=1)
        doc.add_paragraph('本项目旨在开发一个基于AI的智能报告生成系统，能够自动从各种文档中抽取关键信息并生成标准化报告。')
        
        # 添加功能列表
        doc.add_heading('主要功能', level=1)
        doc.add_paragraph('1. 文档解析和信息抽取', style='List Number')
        doc.add_paragraph('2. 模板管理和字段定义', style='List Number')
        doc.add_paragraph('3. 智能报告生成', style='List Number')
        doc.add_paragraph('4. 质量验证和人工审核', style='List Number')
        
        # 添加表格
        doc.add_heading('项目进度表', level=1)
        table = doc.add_table(rows=1, cols=4)
        table.style = 'Table Grid'
        
        # 表头
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '阶段'
        hdr_cells[1].text = '任务'
        hdr_cells[2].text = '完成度'
        hdr_cells[3].text = '负责人'
        
        # 添加数据行
        progress_data = [
            ('需求分析', '需求调研和分析', '100%', '张三'),
            ('系统设计', '架构设计和数据库设计', '90%', '李四'),
            ('开发实现', '核心功能开发', '75%', '王五'),
            ('测试验证', '功能测试和性能测试', '30%', '赵六')
        ]
        
        for stage, task, progress, owner in progress_data:
            row_cells = table.add_row().cells
            row_cells[0].text = stage
            row_cells[1].text = task
            row_cells[2].text = progress
            row_cells[3].text = owner
        
        # 保存文档
        docx_file = test_dir / "test_project_report.docx"
        doc.save(docx_file)
        print(f"✅ 创建测试docx文件: {docx_file}")
        
    except ImportError:
        print("❌ python-docx未安装，无法创建测试docx文件")
    
    return test_dir


def test_document_parsing():
    """测试文档解析功能"""
    print("🔍 测试文档解析功能...")
    
    try:
        from services.document_service import document_service
        
        # 创建测试文档
        test_dir = create_test_documents()
        
        # 测试txt文件解析
        txt_file = test_dir / "test_meeting.txt"
        if txt_file.exists():
            print(f"\n📄 测试txt文件解析: {txt_file}")
            result = document_service.parse_document(txt_file)
            
            if result.success:
                print(f"✅ 解析成功")
                print(f"📝 文档类型: {result.document_type.value}")
                print(f"📊 段落数量: {len(result.paragraphs)}")
                print(f"📏 文本长度: {len(result.raw_text)}")
                print(f"🔧 元数据: {result.metadata}")
                print(f"📖 前100字符: {result.raw_text[:100]}...")
            else:
                print(f"❌ 解析失败: {result.error}")
        
        # 测试docx文件解析
        docx_file = test_dir / "test_project_report.docx"
        if docx_file.exists():
            print(f"\n📄 测试docx文件解析: {docx_file}")
            result = document_service.parse_document(docx_file)
            
            if result.success:
                print(f"✅ 解析成功")
                print(f"📝 文档类型: {result.document_type.value}")
                print(f"📊 段落数量: {len(result.paragraphs)}")
                print(f"📋 表格数量: {len(result.tables)}")
                print(f"📏 文本长度: {len(result.raw_text)}")
                print(f"🔧 元数据: {result.metadata}")
                print(f"📖 前200字符: {result.raw_text[:200]}...")
                
                # 显示表格信息
                if result.tables:
                    print(f"\n📋 表格详情:")
                    for i, table in enumerate(result.tables):
                        print(f"  表格{i+1}: {table['row_count']}行 x {table['col_count']}列")
                        print(f"  内容预览: {table['text'][:100]}...")
            else:
                print(f"❌ 解析失败: {result.error}")
        
        # 测试不支持的文件类型
        print(f"\n🚫 测试不支持的文件类型...")
        fake_file = test_dir / "test.pdf"
        result = document_service.parse_document(fake_file)
        if not result.success:
            print(f"✅ 正确识别不支持的文件类型: {result.error}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文档解析测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_document_integration():
    """测试文档处理与LLM服务集成"""
    print("\n🔍 测试文档处理与LLM服务集成...")
    
    try:
        from services.document_service import document_service
        from services.llm_service import llm_service, ExtractionRequest
        
        # 创建测试文档
        test_dir = create_test_documents()
        txt_file = test_dir / "test_meeting.txt"
        
        if not txt_file.exists():
            print("❌ 测试文档不存在")
            return False
        
        # 解析文档
        print("📄 解析文档...")
        doc_result = document_service.parse_document(txt_file)
        
        if not doc_result.success:
            print(f"❌ 文档解析失败: {doc_result.error}")
            return False
        
        print(f"✅ 文档解析成功，文本长度: {len(doc_result.raw_text)}")
        
        # 使用LLM抽取信息
        print("🤖 使用LLM抽取信息...")
        
        # 定义要抽取的字段
        fields_to_extract = [
            {
                "field_name": "会议主题",
                "description": "会议讨论的主要议题",
                "example": "数字化转型项目进展讨论",
                "format": "普通文本"
            },
            {
                "field_name": "会议时间",
                "description": "会议举行的具体时间",
                "example": "2024年7月4日 14:00-16:00",
                "format": "日期时间"
            },
            {
                "field_name": "主持人",
                "description": "会议的主持人姓名",
                "example": "张总经理",
                "format": "人名"
            },
            {
                "field_name": "项目完成度",
                "description": "项目当前的完成百分比",
                "example": "75%",
                "format": "百分比"
            }
        ]
        
        extraction_results = {}
        
        for field in fields_to_extract:
            print(f"  📋 抽取字段: {field['field_name']}")
            
            request = ExtractionRequest(
                field_name=field["field_name"],
                field_description=field["description"],
                field_example=field["example"],
                field_format=field["format"],
                source_text=doc_result.raw_text
            )
            
            response = await llm_service.extract_field_info(request)
            
            if response.success:
                extracted_value = response.content.strip()
                extraction_results[field["field_name"]] = extracted_value
                print(f"    ✅ 抽取成功: {extracted_value}")
            else:
                print(f"    ❌ 抽取失败: {response.error}")
                extraction_results[field["field_name"]] = ""
        
        print(f"\n📊 完整抽取结果:")
        for field_name, value in extraction_results.items():
            print(f"  {field_name}: {value}")
        
        # 计算成功率
        success_count = len([v for v in extraction_results.values() if v])
        total_count = len(extraction_results)
        success_rate = success_count / total_count if total_count > 0 else 0
        
        print(f"\n🎯 抽取成功率: {success_rate:.2%} ({success_count}/{total_count})")
        
        return success_rate > 0.5  # 50%以上认为成功
        
    except Exception as e:
        print(f"❌ 集成测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("🚀 开始文档处理服务测试\n")
    
    tests = [
        ("文档解析功能", test_document_parsing),
        ("文档与LLM集成", test_document_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"{'='*60}")
        try:
            if test_func.__name__ == 'test_document_integration':
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 发生异常: {e}")
            results.append((test_name, False))
    
    print(f"\n{'='*60}")
    print("📊 文档处理测试结果汇总:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    print(f"\n{'='*60}")
    if all_passed:
        print("🎉 所有文档处理测试通过！")
        print("✅ 文档处理能力已准备就绪")
        print("🚀 可以开始构建完整的文档处理工作流")
    else:
        print("❌ 部分测试失败，需要进一步优化")
    
    return all_passed


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
