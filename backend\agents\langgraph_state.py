"""
LangGraph状态定义
定义整个智能体工作流的状态结构
"""

from typing import Dict, List, Any, Optional, Literal, TypedDict
from datetime import datetime
from uuid import UUID


class UserInput(TypedDict):
    """用户输入信息"""
    text: str  # 用户输入的文本
    files: List[Dict[str, Any]]  # 上传的文件信息
    session_id: str  # 会话ID
    user_id: str  # 用户ID
    timestamp: datetime  # 输入时间


class IntentionResult(TypedDict):
    """意图识别结果"""
    intent_type: Literal["text_generation", "information_query", "table_creation", "other"]
    confidence: float  # 置信度 0-1
    sub_intent: Optional[str]  # 子意图
    parameters: Dict[str, Any]  # 意图参数
    reasoning: str  # 识别理由


class TemplateInfo(TypedDict):
    """模板信息"""
    template_id: Optional[UUID]
    template_name: str
    template_path: str
    key_definitions_path: str
    placeholder_count: int
    field_definitions: List[Dict[str, Any]]  # 字段定义列表


class ExtractionResult(TypedDict):
    """数据提取结果"""
    field_name: str
    field_value: str
    confidence: float  # 置信度 0-1
    source_location: Optional[str]  # 原文位置
    extraction_method: str  # 提取方法
    is_verified: bool  # 是否已验证
    needs_human_input: bool  # 是否需要人工输入


class ValidationResult(TypedDict):
    """验证结果"""
    field_name: str
    is_valid: bool
    validation_score: float  # 验证分数 0-1
    error_message: Optional[str]
    suggestions: List[str]  # 改进建议


class HumanInteraction(TypedDict):
    """人工交互信息"""
    interaction_type: Literal["confirm", "supplement", "correct", "approve"]
    field_name: Optional[str]
    original_value: Optional[str]
    user_input: Optional[str]
    user_choice: Optional[str]
    timestamp: datetime
    is_completed: bool


class ReportGeneration(TypedDict):
    """报告生成信息"""
    report_id: Optional[UUID]
    report_name: str
    output_path: Optional[str]
    generation_method: str
    quality_score: Optional[float]
    generation_time: Optional[float]
    status: Literal["pending", "generating", "completed", "failed"]


class WorkflowState(TypedDict):
    """LangGraph主状态 - 整个工作流的状态容器"""
    
    # 基础信息
    workflow_id: str
    session_id: str
    user_id: str
    current_stage: str
    created_at: datetime
    updated_at: datetime
    
    # 用户输入
    user_input: UserInput
    
    # 意图识别
    intention_result: Optional[IntentionResult]
    
    # 模板信息
    template_info: Optional[TemplateInfo]
    
    # 数据提取
    extraction_results: List[ExtractionResult]
    extraction_stats: Dict[str, Any]  # 提取统计信息
    
    # 数据验证
    validation_results: List[ValidationResult]
    validation_stats: Dict[str, Any]  # 验证统计信息
    
    # 人工交互
    human_interactions: List[HumanInteraction]
    pending_interactions: List[str]  # 待处理的交互类型
    
    # 报告生成
    report_generation: Optional[ReportGeneration]
    
    # 错误处理
    errors: List[Dict[str, Any]]
    warnings: List[Dict[str, Any]]
    
    # 流程控制
    next_action: Optional[str]  # 下一步动作
    retry_count: int  # 重试次数
    max_retries: int  # 最大重试次数
    
    # 元数据
    metadata: Dict[str, Any]  # 额外的元数据信息


class NodeResult(TypedDict):
    """节点执行结果"""
    node_name: str
    success: bool
    message: str
    data: Dict[str, Any]
    execution_time: float
    next_node: Optional[str]  # 建议的下一个节点


# 状态更新辅助函数
def create_initial_state(
    user_input: UserInput,
    workflow_id: str,
    max_retries: int = 3
) -> WorkflowState:
    """创建初始状态"""
    now = datetime.now()
    
    return WorkflowState(
        workflow_id=workflow_id,
        session_id=user_input["session_id"],
        user_id=user_input["user_id"],
        current_stage="intention_analysis",
        created_at=now,
        updated_at=now,
        user_input=user_input,
        intention_result=None,
        template_info=None,
        extraction_results=[],
        extraction_stats={},
        validation_results=[],
        validation_stats={},
        human_interactions=[],
        pending_interactions=[],
        report_generation=None,
        errors=[],
        warnings=[],
        next_action=None,
        retry_count=0,
        max_retries=max_retries,
        metadata={}
    )


def update_state_stage(state: WorkflowState, new_stage: str) -> WorkflowState:
    """更新状态阶段"""
    state["current_stage"] = new_stage
    state["updated_at"] = datetime.now()
    return state


def add_error(state: WorkflowState, error_type: str, message: str, details: Dict[str, Any] = None) -> WorkflowState:
    """添加错误信息"""
    error_info = {
        "type": error_type,
        "message": message,
        "timestamp": datetime.now(),
        "stage": state["current_stage"],
        "details": details or {}
    }
    state["errors"].append(error_info)
    return state


def add_human_interaction(
    state: WorkflowState,
    interaction_type: str,
    field_name: str = None,
    original_value: str = None,
    interaction_data: Dict[str, Any] = None
) -> WorkflowState:
    """添加人工交互请求"""
    interaction = HumanInteraction(
        interaction_type=interaction_type,
        field_name=field_name,
        original_value=original_value,
        user_input=None,
        user_choice=None,
        timestamp=datetime.now(),
        is_completed=False
    )

    # 如果提供了额外的交互数据，添加到状态中
    if interaction_data:
        if "interaction_details" not in state:
            state["interaction_details"] = {}
        state["interaction_details"][interaction_type] = interaction_data

    state["human_interactions"].append(interaction)
    state["pending_interactions"].append(interaction_type)
    return state
