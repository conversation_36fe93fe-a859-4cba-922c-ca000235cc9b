// Simple test component without complex dependencies
import React from 'react';

const SimpleTest: React.FC = () => {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ color: '#1890ff' }}>🚀 Frontend is Working!</h1>
      <p>If you can see this message, React is properly rendering.</p>
      <div style={{ 
        background: '#f0f2f5', 
        padding: '16px', 
        borderRadius: '8px',
        marginTop: '16px'
      }}>
        <h3>System Status:</h3>
        <ul>
          <li>✅ React: Working</li>
          <li>✅ TypeScript: Working</li>
          <li>✅ Vite: Working</li>
          <li>⏳ Ant Design: Testing...</li>
        </ul>
      </div>
      <button 
        style={{
          background: '#1890ff',
          color: 'white',
          border: 'none',
          padding: '8px 16px',
          borderRadius: '4px',
          cursor: 'pointer',
          marginTop: '16px'
        }}
        onClick={() => alert('Button works!')}
      >
        Test Button
      </button>
    </div>
  );
};

export default SimpleTest;