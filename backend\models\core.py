"""
核心数据模型基类
"""

"""
数据模型核心模块
定义基础模型类和通用字段
"""

import uuid
from datetime import datetime
from typing import Any, Dict, Optional
from sqlalchemy import Column, String, DateTime, Boolean, Text, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Session, declarative_base

# 创建基础模型类
Base = declarative_base()


class BaseModel(Base):
    """
    基础模型类，包含所有模型的通用字段

    Attributes:
        id: 主键，UUID类型
        created_at: 创建时间
        updated_at: 更新时间
        created_by: 创建者
        is_active: 是否激活
    """

    __abstract__ = True

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, comment="主键ID")
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    updated_at = Column(
        DateTime,
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        nullable=False,
        comment="更新时间"
    )
    created_by = Column(String(100), nullable=True, comment="创建者")
    is_active = Column(Boolean, default=True, nullable=False, comment="是否激活")

    def to_dict(self) -> Dict[str, Any]:
        """
        将模型转换为字典

        Returns:
            Dict[str, Any]: 模型字典表示
        """
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                value = value.isoformat()
            elif isinstance(value, uuid.UUID):
                value = str(value)
            result[column.name] = value
        return result

    def update_from_dict(self, data: Dict[str, Any]) -> None:
        """
        从字典更新模型属性

        Args:
            data: 包含更新数据的字典
        """
        for key, value in data.items():
            if hasattr(self, key) and key not in ['id', 'created_at']:
                setattr(self, key, value)
        self.updated_at = datetime.utcnow()

    @classmethod
    def create(cls, db: Session, **kwargs) -> "BaseModel":
        """
        创建新实例

        Args:
            db: 数据库会话
            **kwargs: 创建参数

        Returns:
            BaseModel: 创建的实例
        """
        instance = cls(**kwargs)
        db.add(instance)
        db.commit()
        db.refresh(instance)
        return instance

    def save(self, db: Session) -> "BaseModel":
        """
        保存实例

        Args:
            db: 数据库会话

        Returns:
            BaseModel: 保存后的实例
        """
        db.add(self)
        db.commit()
        db.refresh(self)
        return self

    def delete(self, db: Session, soft_delete: bool = True) -> None:
        """
        删除实例

        Args:
            db: 数据库会话
            soft_delete: 是否软删除
        """
        if soft_delete:
            self.is_active = False
            self.updated_at = datetime.utcnow()
            db.commit()
        else:
            db.delete(self)
            db.commit()

    def __repr__(self) -> str:
        """字符串表示"""
        return f"<{self.__class__.__name__}(id={self.id})>"


def drop_tables():
    """删除所有表"""
    Base.metadata.drop_all(bind=engine)