"""
智能体相关数据模型
支持LangGraph智能体会话管理和工具调用
"""

from sqlalchemy import Column, String, Text, Integer, Boolean, ForeignKey, DateTime
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from .core import BaseModel


class AgentSession(BaseModel):
    """智能体会话表"""

    __tablename__ = "agent_sessions"

    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True, comment="用户ID")
    session_name = Column(String(255), nullable=True, comment="会话名称")
    session_type = Column(String(50), nullable=False, default="report_generation", comment="会话类型")
    status = Column(String(50), nullable=False, default="active", comment="会话状态")
    current_agent = Column(String(100), nullable=True, comment="当前活跃的智能体")
    current_stage = Column(String(100), nullable=True, comment="当前处理阶段")
    context_data = Column(JSONB, nullable=True, comment="会话上下文数据")
    completed_at = Column(DateTime, nullable=True, comment="完成时间")

    # 关联关系
    user = relationship("User", back_populates="agent_sessions")
    conversations = relationship("ConversationHistory", back_populates="session", cascade="all, delete-orphan")
    tool_calls = relationship("ToolCall", back_populates="session", cascade="all, delete-orphan")
    generation_tasks = relationship("GenerationTask", back_populates="session")
    extracted_data = relationship("ExtractedData", back_populates="session")
    reports = relationship("Report", back_populates="session")

    def __repr__(self):
        return f"<AgentSession(id={self.id}, type='{self.session_type}', status='{self.status}')>"


class ConversationHistory(BaseModel):
    """对话历史表"""

    __tablename__ = "conversation_history"

    session_id = Column(UUID(as_uuid=True), ForeignKey("agent_sessions.id"), nullable=False, comment="会话ID")
    message_type = Column(String(50), nullable=False, comment="消息类型")
    sender = Column(String(100), nullable=True, comment="发送者标识")
    content = Column(Text, nullable=False, comment="消息内容")
    message_metadata = Column(JSONB, nullable=True, comment="消息元数据")
    tool_calls = Column(JSONB, nullable=True, comment="工具调用信息")
    attachments = Column(JSONB, nullable=True, comment="附件信息")

    # 关联关系
    session = relationship("AgentSession", back_populates="conversations")

    def __repr__(self):
        return f"<ConversationHistory(id={self.id}, type='{self.message_type}', sender='{self.sender}')>"


class ToolCall(BaseModel):
    """工具调用记录表"""

    __tablename__ = "tool_calls"

    session_id = Column(UUID(as_uuid=True), ForeignKey("agent_sessions.id"), nullable=False, comment="会话ID")
    conversation_id = Column(UUID(as_uuid=True), ForeignKey("conversation_history.id"), nullable=True, comment="对话ID")
    tool_name = Column(String(100), nullable=False, comment="工具名称")
    tool_input = Column(JSONB, nullable=False, comment="工具输入参数")
    tool_output = Column(JSONB, nullable=True, comment="工具输出结果")
    status = Column(String(50), nullable=False, default="pending", comment="执行状态")
    error_message = Column(Text, nullable=True, comment="错误信息")
    execution_time = Column(Integer, nullable=True, comment="执行时间(毫秒)")
    completed_at = Column(DateTime, nullable=True, comment="完成时间")

    # 关联关系
    session = relationship("AgentSession", back_populates="tool_calls")
    conversation = relationship("ConversationHistory")

    def __repr__(self):
        return f"<ToolCall(id={self.id}, tool='{self.tool_name}', status='{self.status}')>"


class KeyDefinition(BaseModel):
    """关键信息定义表"""

    __tablename__ = "key_definitions"

    template_id = Column(UUID(as_uuid=True), ForeignKey("templates.id"), nullable=False, comment="模板ID")
    field_name = Column(String(255), nullable=False, comment="字段名称")
    parent_field = Column(String(255), nullable=True, comment="父字段名称")
    field_level = Column(Integer, nullable=False, default=1, comment="字段层级")
    field_path = Column(String(500), nullable=False, comment="完整字段路径")

    # 7个核心属性
    description = Column(Text, nullable=True, comment="字段描述，用于嵌入prompt")
    example = Column(Text, nullable=True, comment="字段样例")
    format = Column(String(50), nullable=False, default="普通文本", comment="字段格式")
    default_value = Column(Text, nullable=True, comment="默认值")
    length = Column(Integer, nullable=True, comment="字段长度限制")
    source = Column(String(50), nullable=False, default="上传内容", comment="信息来源")
    must = Column(Boolean, nullable=False, default=False, comment="是否必须")

    field_order = Column(Integer, nullable=False, default=0, comment="字段顺序")

    # 关联关系
    template = relationship("Template", back_populates="key_definitions")

    def __repr__(self):
        return f"<KeyDefinition(id={self.id}, path='{self.field_path}', must={self.must})>"
