"""
报告生成LangGraph工作流
基于LangGraph实现的智能报告生成工作流
"""

import logging
from typing import Dict, Any, Literal
from uuid import uuid4
from datetime import datetime

from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from .dialogue_agent import BaseDialogueAgent
from .langgraph_state import WorkflowState, UserInput, create_initial_state
from .langgraph_nodes import LangGraphNodes
from langgraph.checkpoint.memory import InMemorySaver

logger = logging.getLogger(__name__)


class ReportGenerationGraph(BaseDialogueAgent):
    """报告生成工作流图"""

    def __init__(self):
        system_prompt = """你是一个专业的报告生成助手。你的主要任务是：
1. 分析用户意图，识别报告生成需求
2. 选择合适的报告模板
3. 从文档中提取关键信息
4. 生成高质量的结构化报告
5. 在需要时寻求人工协助和确认

请始终保持专业、准确和高效。"""

        # 调用父类初始化，获得对话持久化能力
        super().__init__(
            agent_name="ReportGenerationGraph",
            system_prompt=system_prompt
        )

        # 创建内存检查点存储器（用于测试）
        self.workflow_checkpointer = InMemorySaver()

        # 保留原有的工作流图
        self.workflow_graph = None
        self._build_workflow_graph()
    
    def _build_workflow_graph(self):
        """构建LangGraph工作流"""

        # 创建状态图
        workflow = StateGraph(WorkflowState)
        
        # 添加节点
        workflow.add_node("intention_analysis", LangGraphNodes.intention_analysis_node)
        workflow.add_node("conversation_response", LangGraphNodes.conversation_response_node)
        workflow.add_node("non_text_generation_response", LangGraphNodes.non_text_generation_response_node)
        workflow.add_node("template_identification", LangGraphNodes.template_identification_node)
        workflow.add_node("template_parsing", LangGraphNodes.template_parsing_node)
        workflow.add_node("data_extraction", LangGraphNodes.data_extraction_node)
        workflow.add_node("data_validation", self._data_validation_node)
        workflow.add_node("generate_report", self._report_generation_node)
        workflow.add_node("human_interaction", LangGraphNodes.human_interaction_node)
        workflow.add_node("error_handling", LangGraphNodes.error_handling_node)
        
        # 设置入口点
        workflow.set_entry_point("intention_analysis")
        
        # 添加条件边
        workflow.add_conditional_edges(
            "intention_analysis",
            self._route_after_intention,
            {
                "text_generation": "template_identification",
                "conversation": "conversation_response",
                "other": "non_text_generation_response",
                "error": "error_handling"
            }
        )
        
        workflow.add_conditional_edges(
            "template_identification",
            self._route_after_template_identification,
            {
                "template_parsing": "template_parsing",
                "human_interaction": "human_interaction",
                "error": "error_handling"
            }
        )
        
        workflow.add_conditional_edges(
            "template_parsing",
            self._route_after_template_parsing,
            {
                "data_extraction": "data_extraction",
                "error": "error_handling"
            }
        )
        
        workflow.add_conditional_edges(
            "data_extraction",
            self._route_after_data_extraction,
            {
                "data_validation": "data_validation",
                "human_interaction": "human_interaction",
                "error": "error_handling"
            }
        )
        
        workflow.add_conditional_edges(
            "data_validation",
            self._route_after_data_validation,
            {
                "generate_report": "generate_report",
                "human_interaction": "human_interaction",
                "data_extraction": "data_extraction",  # 重新提取
                "error": "error_handling"
            }
        )
        
        workflow.add_conditional_edges(
            "generate_report",
            self._route_after_report_generation,
            {
                "end": END,
                "human_interaction": "human_interaction",
                "error": "error_handling"
            }
        )
        
        workflow.add_conditional_edges(
            "human_interaction",
            self._route_after_human_interaction,
            {
                "data_extraction": "data_extraction",
                "data_validation": "data_validation",
                "generate_report": "generate_report",
                "template_identification": "template_identification",
                "template_parsing": "template_parsing",
                "wait": END,  # 暂停等待用户输入
                "error": "error_handling"
            }
        )
        
        workflow.add_conditional_edges(
            "error_handling",
            self._route_after_error_handling,
            {
                "intention_analysis": "intention_analysis",
                "human_interaction": "human_interaction",
                "end": END
            }
        )

        # 添加对话响应节点和非文本生成响应节点的结束边
        workflow.add_edge("conversation_response", END)
        workflow.add_edge("non_text_generation_response", END)

        # 编译图
        self.workflow_graph = workflow.compile(checkpointer=self.workflow_checkpointer)
        logger.info("报告生成工作流图构建完成")
    
    # 路由函数
    def _route_after_intention(self, state: WorkflowState) -> str:
        """意图识别后的路由"""
        if state.get("next_action") == "error_handling":
            return "error"

        intention_result = state.get("intention_result")
        if intention_result:
            intent_type = intention_result.get("intent_type")
            if intent_type == "text_generation":
                return "text_generation"
            elif intent_type == "conversation":
                return "conversation"
            else:
                return "other"
        return "error"
    
    def _route_after_template_identification(self, state: WorkflowState) -> str:
        """模板识别后的路由"""
        next_action = state.get("next_action")
        if next_action == "template_parsing":
            return "template_parsing"
        elif next_action == "human_interaction":
            return "human_interaction"
        else:
            return "error"
    
    def _route_after_template_parsing(self, state: WorkflowState) -> str:
        """模板解析后的路由"""
        next_action = state.get("next_action")
        if next_action == "data_extraction":
            return "data_extraction"
        else:
            return "error"
    
    def _route_after_data_extraction(self, state: WorkflowState) -> str:
        """数据提取后的路由"""
        next_action = state.get("next_action")
        if next_action == "data_validation":
            return "data_validation"
        elif next_action == "human_interaction":
            return "human_interaction"
        else:
            return "error"
    
    def _route_after_data_validation(self, state: WorkflowState) -> str:
        """数据验证后的路由"""
        next_action = state.get("next_action")
        if next_action == "generate_report":
            return "generate_report"
        elif next_action == "human_interaction":
            return "human_interaction"
        elif next_action == "data_extraction":
            return "data_extraction"
        else:
            return "error"
    
    def _route_after_report_generation(self, state: WorkflowState) -> str:
        """报告生成后的路由"""
        next_action = state.get("next_action")
        if next_action == "end":
            return "end"
        elif next_action == "human_interaction":
            return "human_interaction"
        else:
            return "error"
    
    def _route_after_human_interaction(self, state: WorkflowState) -> str:
        """人工交互后的路由"""
        next_action = state.get("next_action")
        if next_action == "wait_for_user":
            return "wait"
        elif next_action in ["data_extraction", "data_validation", "generate_report", "template_identification", "template_parsing"]:
            return next_action
        else:
            return "error"
    
    def _route_after_error_handling(self, state: WorkflowState) -> str:
        """错误处理后的路由"""
        next_action = state.get("next_action")
        if next_action == "intention_analysis":
            return "intention_analysis"
        elif next_action == "human_interaction":
            return "human_interaction"
        else:
            return "end"
    
    # 节点实现
    async def _data_validation_node(self, state: WorkflowState) -> WorkflowState:
        """数据验证节点"""
        logger.info(f"[{state['workflow_id']}] 开始数据验证")
        
        try:
            from .langgraph_state import update_state_stage, add_error, add_human_interaction
            
            state = update_state_stage(state, "data_validation")
            
            extraction_results = state["extraction_results"]
            validation_results = []
            
            # 简化的验证逻辑
            for result in extraction_results:
                field_name = result["field_name"]
                field_value = result["field_value"]
                confidence = result["confidence"]
                
                # 基本验证规则
                is_valid = True
                validation_score = confidence
                error_message = None
                suggestions = []
                
                if field_value == "未找到" or not field_value.strip():
                    is_valid = False
                    validation_score = 0.0
                    error_message = "字段值为空或未找到"
                    suggestions.append("请检查原文档或手动补充该字段")
                elif confidence < 0.5:
                    is_valid = False
                    validation_score = confidence
                    error_message = "提取置信度过低"
                    suggestions.append("建议人工确认该字段的准确性")
                
                validation_result = {
                    "field_name": field_name,
                    "is_valid": is_valid,
                    "validation_score": validation_score,
                    "error_message": error_message,
                    "suggestions": suggestions
                }
                
                validation_results.append(validation_result)
            
            state["validation_results"] = validation_results
            
            # 统计验证结果
            total_fields = len(validation_results)
            valid_fields = len([r for r in validation_results if r["is_valid"]])
            validation_rate = valid_fields / total_fields if total_fields > 0 else 0
            
            state["validation_stats"] = {
                "total_fields": total_fields,
                "valid_fields": valid_fields,
                "validation_rate": validation_rate,
                "avg_validation_score": sum(r["validation_score"] for r in validation_results) / len(validation_results) if validation_results else 0
            }
            
            # 决定下一步动作
            if validation_rate < 0.8:  # 验证通过率低于80%
                state = add_human_interaction(state, "correct", "validation_failed")
                state["next_action"] = "human_interaction"
            else:
                state["next_action"] = "generate_report"
            
            logger.info(f"[{state['workflow_id']}] 数据验证完成，通过率: {validation_rate:.2%}")
            
        except Exception as e:
            logger.error(f"[{state['workflow_id']}] 数据验证异常: {e}")
            state = add_error(state, "data_validation_exception", str(e))
            state["next_action"] = "error_handling"
        
        return state
    
    async def _report_generation_node(self, state: WorkflowState) -> WorkflowState:
        """报告生成节点"""
        logger.info(f"[{state['workflow_id']}] 开始报告生成")
        
        try:
            from .langgraph_state import update_state_stage, add_error
            
            state = update_state_stage(state, "report_generation")
            
            # 实际的报告生成逻辑
            import os
            import time
            start_time = time.time()

            # 确保输出目录存在
            output_dir = "outputs"
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            report_name = f"会议纪要_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            # Try to create both .docx and .txt versions
            output_path_docx = os.path.join(output_dir, f"{report_name}.docx")
            output_path_txt = os.path.join(output_dir, f"{report_name}.txt")

            # 调用报告生成工具
            try:
                from agents.tools.report_generation import ReportGenerationTool

                report_tool = ReportGenerationTool()

                # 准备数据映射
                extraction_results = state.get("extraction_results", [])
                data_mapping = {}
                for result in extraction_results:
                    field_name = result.get("field_name")
                    field_value = result.get("field_value", result.get("extracted_value", ""))
                    if field_name:
                        data_mapping[field_name] = field_value if field_value and field_value != "未找到" else "[待补充]"

                # 获取模板路径
                template_info = state.get("template_info", {})
                template_path = template_info.get("template_path", "")

                # Check if template exists
                if not template_path or not os.path.exists(template_path):
                    # Create a simple text report if no template is available
                    logger.warning(f"[{state['workflow_id']}] 模板文件不存在或未指定，生成文本报告: {template_path}")
                    self._create_simple_text_report(output_path_txt, extraction_results, state)
                    generation_time = time.time() - start_time
                    report_generation = {
                        "report_id": str(uuid4()),
                        "report_name": report_name,
                        "output_path": output_path_txt,
                        "generation_method": "text_only",
                        "quality_score": 0.7,
                        "generation_time": generation_time,
                        "status": "completed_text",
                        "data_mapping": data_mapping,
                        "fields_filled": len([v for v in data_mapping.values() if v and v != "[待补充]"])
                    }
                else:
                    # 生成报告
                    generation_result = await report_tool.execute({
                        "template_path": template_path,
                        "extracted_data": extraction_results,
                        "output_path": output_path_docx,
                        "data_mapping": data_mapping
                    })

                    if generation_result.get("success", False):
                        generation_time = time.time() - start_time
                        report_generation = {
                            "report_id": str(uuid4()),
                            "report_name": report_name,
                            "output_path": output_path_docx,
                            "generation_method": "template_filling",
                            "quality_score": generation_result.get("data", {}).get("quality_metrics", {}).get("overall_score", 0.8),
                            "generation_time": generation_time,
                            "status": "completed",
                            "data_mapping": data_mapping,
                            "fields_filled": len([v for v in data_mapping.values() if v and v != "[待补充]"])
                        }

                        logger.info(f"[{state['workflow_id']}] 报告生成成功: {output_path_docx}")
                    else:
                        # 生成失败，创建简单的文本报告
                        self._create_simple_text_report(output_path_txt, extraction_results, state)
                        generation_time = time.time() - start_time
                        report_generation = {
                            "report_id": str(uuid4()),
                            "report_name": report_name,
                            "output_path": output_path_txt,
                            "generation_method": "simple_text",
                            "quality_score": 0.6,
                            "generation_time": generation_time,
                            "status": "completed_with_fallback",
                            "error_message": generation_result.get("error", "未知错误")
                        }

                        logger.warning(f"[{state['workflow_id']}] 报告生成降级为文本格式: {generation_result.get('error', '未知错误')}")

            except Exception as e:
                # 异常处理，创建简单的文本报告
                logger.error(f"[{state['workflow_id']}] 报告生成工具异常: {e}")
                self._create_simple_text_report(output_path_txt, state.get("extraction_results", []), state)
                generation_time = time.time() - start_time
                report_generation = {
                    "report_id": str(uuid4()),
                    "report_name": report_name,
                    "output_path": output_path_txt,
                    "generation_method": "fallback_text",
                    "quality_score": 0.5,
                    "generation_time": generation_time,
                    "status": "completed_with_error",
                    "error_message": str(e)
                }

            state["report_generation"] = report_generation
            state["next_action"] = "end"
            
        except Exception as e:
            logger.error(f"[{state['workflow_id']}] 报告生成异常: {e}")
            state = add_error(state, "report_generation_exception", str(e))
            state["next_action"] = "error_handling"
        
        return state

    def _create_simple_text_report(self, output_path: str, extraction_results: list, state: dict):
        """创建简单的文本报告作为备选方案"""
        try:
            with open(output_path.replace('.docx', '.txt'), 'w', encoding='utf-8') as f:
                f.write("会议纪要\n")
                f.write("=" * 50 + "\n\n")

                # 写入提取的字段信息
                for result in extraction_results:
                    field_name = result.get("field_name", "")
                    field_value = result.get("field_value", "")
                    if field_name and field_value != "未找到":
                        f.write(f"{field_name}: {field_value}\n")

                f.write("\n" + "=" * 50 + "\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("生成方式: 智能体自动生成\n")

        except Exception as e:
            logger.error(f"创建简单文本报告失败: {e}")

    # 公共接口
    async def run_workflow(self, user_input: UserInput) -> Dict[str, Any]:
        """运行工作流"""
        workflow_id = str(uuid4())
        
        # 创建初始状态
        initial_state = create_initial_state(user_input, workflow_id)
        
        try:
            # 运行工作流
            config = {"configurable": {"thread_id": workflow_id}}
            
            final_state = None
            async for state in self.workflow_graph.astream(initial_state, config=config):
                final_state = state
                # 正确解析状态结构
                if state:
                    current_node = list(state.keys())[0] if state else "unknown"
                    node_state = list(state.values())[0] if state else {}
                    current_stage = node_state.get('current_stage', current_node)
                    logger.info(f"[{workflow_id}] 当前节点: {current_node}, 阶段: {current_stage}")
                else:
                    logger.info(f"[{workflow_id}] 状态为空")
            
            return {
                "workflow_id": workflow_id,
                "status": "completed" if final_state else "failed",
                "final_state": final_state,
                "errors": final_state.get("errors", []) if final_state else [],
                "report_generation": final_state.get("report_generation") if final_state else None
            }
            
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"[{workflow_id}] 工作流执行异常: {e}")
            logger.error(f"[{workflow_id}] 详细错误信息: {error_details}")
            return {
                "workflow_id": workflow_id,
                "status": "failed",
                "error": str(e),
                "error_details": error_details,
                "final_state": None
            }
    
    async def resume_workflow(self, workflow_id: str, user_response: Dict[str, Any]) -> Dict[str, Any]:
        """恢复暂停的工作流（处理人工交互）"""
        logger.info(f"[{workflow_id}] 开始恢复工作流，用户响应: {user_response}")
        try:
            config = {"configurable": {"thread_id": workflow_id}}
            
            # 获取当前状态
            current_state = await self.workflow_graph.aget_state(config)
            if not current_state:
                return {"error": "工作流状态未找到"}

            logger.info(f"[{workflow_id}] 当前状态类型: {type(current_state)}")
            logger.info(f"[{workflow_id}] current_state.values类型: {type(current_state.values)}")
            logger.info(f"[{workflow_id}] template_info在values中: {'template_info' in current_state.values}")

            if 'template_info' in current_state.values:
                template_info = current_state.values['template_info']
                logger.info(f"[{workflow_id}] template_info类型: {type(template_info)}")
                logger.info(f"[{workflow_id}] template_info内容: {template_info}")
            else:
                logger.error(f"[{workflow_id}] template_info不在current_state.values中")
                logger.info(f"[{workflow_id}] current_state.values的键: {list(current_state.values.keys())}")

            # 更新状态以包含用户响应
            updated_state = current_state.values.copy()  # 使用copy避免修改原状态

            # 确保关键状态信息被保留
            if "template_info" not in updated_state and "template_info" in current_state.values:
                updated_state["template_info"] = current_state.values["template_info"]
                logger.info(f"[{workflow_id}] 手动保留template_info到更新状态中")

            # 处理用户响应
            action = user_response.get("action")
            logger.info(f"[{workflow_id}] 处理用户响应: {action}")

            if action == "confirm":
                # 用户确认使用推荐模板
                logger.info(f"[{workflow_id}] 用户确认使用推荐模板")

                # 清除待处理的交互
                updated_state["pending_interactions"] = []
                updated_state["next_action"] = "template_parsing"
                updated_state["current_stage"] = "template_parsing"

                # 添加用户确认记录
                if "human_interactions" not in updated_state:
                    updated_state["human_interactions"] = []

                updated_state["human_interactions"].append({
                    "interaction_type": "template_confirmation",
                    "user_choice": "confirm",
                    "timestamp": datetime.now().isoformat(),
                    "is_completed": True
                })

            elif action == "supplement":
                # 用户补充了数据
                field_updates = user_response.get("field_updates", {})
                for field_name, new_value in field_updates.items():
                    # 更新提取结果
                    for result in updated_state["extraction_results"]:
                        if result["field_name"] == field_name:
                            result["field_value"] = new_value
                            result["confidence"] = 1.0  # 人工输入的置信度设为1.0
                            result["is_verified"] = True
                            result["needs_human_input"] = False

                # 清除待处理的交互
                updated_state["pending_interactions"] = []
                updated_state["next_action"] = "data_validation"

            elif action == "cancel":
                # 用户取消操作
                logger.info(f"[{workflow_id}] 用户取消操作")
                updated_state["current_stage"] = "cancelled"
                updated_state["next_action"] = "end"
                return {
                    "workflow_id": workflow_id,
                    "status": "cancelled",
                    "final_state": updated_state
                }
            
            # 更新状态到图中
            logger.info(f"[{workflow_id}] 更新状态: current_stage={updated_state.get('current_stage')}, next_action={updated_state.get('next_action')}")
            await self.workflow_graph.aupdate_state(config, updated_state, as_node="human_interaction")

            # 继续执行工作流
            final_state = None
            step_count = 0
            logger.info(f"[{workflow_id}] 开始恢复执行工作流...")

            try:
                async for state in self.workflow_graph.astream(None, config=config):
                    step_count += 1
                    final_state = state
                    current_stage = list(state.keys())[0] if state else "unknown"
                    state_values = list(state.values())[0] if state else {}

                    logger.info(f"[{workflow_id}] 步骤 {step_count}: 节点={current_stage}")
                    logger.info(f"[{workflow_id}] 状态: current_stage={state_values.get('current_stage', 'unknown')}, next_action={state_values.get('next_action', 'unknown')}")

                    # 检查是否有错误
                    if state_values.get('errors'):
                        logger.error(f"[{workflow_id}] 发现错误: {state_values['errors']}")

                    # 检查是否需要人机交互
                    if state_values.get('pending_interactions'):
                        logger.info(f"[{workflow_id}] 需要人机交互: {state_values['pending_interactions']}")
                        break

                logger.info(f"[{workflow_id}] 工作流恢复执行完成，总共执行了 {step_count} 步")

                # 确保最终状态被保存
                if final_state:
                    logger.info(f"[{workflow_id}] 保存最终状态到检查点")
                    # 获取最新的完整状态
                    current_state = await self.workflow_graph.aget_state(config)
                    if current_state and current_state.values:
                        logger.info(f"[{workflow_id}] 最终状态已保存，包含 {len(current_state.values)} 个字段")
                    else:
                        logger.warning(f"[{workflow_id}] 最终状态保存可能失败")

            except Exception as stream_error:
                logger.error(f"[{workflow_id}] 工作流执行异常: {stream_error}")
                import traceback
                logger.error(f"[{workflow_id}] 执行异常详情: {traceback.format_exc()}")

            return {
                "workflow_id": workflow_id,
                "status": "resumed",
                "final_state": final_state
            }
            
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"[{workflow_id}] 工作流恢复异常: {e}")
            logger.error(f"[{workflow_id}] 详细错误信息: {error_details}")
            return {
                "workflow_id": workflow_id,
                "status": "failed",
                "error": str(e),
                "error_details": error_details
            }
