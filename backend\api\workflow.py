"""
端到端工作流API
"""

from typing import List, Optional, Dict, Any
from pathlib import Path
import uuid
import logging
from datetime import datetime

from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from pydantic import BaseModel

from services.workflow_service import workflow_service
from agents.report_generation_graph import ReportGenerationGraph
from agents.langgraph_state import UserInput
from services.document_service import document_service
from config import settings

router = APIRouter(prefix="/api/workflow", tags=["工作流"])
logger = logging.getLogger(__name__)

# 确保上传目录存在
UPLOAD_DIR = Path(settings.upload_dir)
UPLOAD_DIR.mkdir(exist_ok=True)

# 初始化LangGraph工作流
report_graph = ReportGenerationGraph()


class TemplateField(BaseModel):
    """模板字段定义"""
    field_name: str
    description: str
    example: str
    format: str


class WorkflowRequest(BaseModel):
    """工作流请求"""
    file_id: str
    template_fields: List[TemplateField]
    report_template: Optional[str] = None


@router.post("/process-document")
async def process_document_workflow(
    file: UploadFile = File(...),
    template_fields: str = Form(...),  # JSON字符串
    report_template: Optional[str] = Form(None)
):
    """
    端到端文档处理工作流
    上传文档 → 解析 → 信息抽取 → 报告生成
    """
    import json
    import uuid
    
    # 解析模板字段
    try:
        fields_data = json.loads(template_fields)
        template_fields_list = [
            {
                "field_name": field["field_name"],
                "description": field["description"],
                "example": field["example"],
                "format": field["format"]
            }
            for field in fields_data
        ]
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="模板字段格式错误")
    
    # 检查文件类型
    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in {'.docx', '.txt'}:
        raise HTTPException(
            status_code=400,
            detail=f"不支持的文件类型: {file_ext}"
        )
    
    # 保存上传文件
    file_id = str(uuid.uuid4())
    safe_filename = f"{file_id}_{file.filename}"
    file_path = UPLOAD_DIR / safe_filename
    
    content = await file.read()
    with open(file_path, 'wb') as f:
        f.write(content)
    
    try:
        # 执行端到端工作流
        result = await workflow_service.process_document_to_report(
            file_path=str(file_path),
            template_fields=template_fields_list,
            report_template=report_template
        )
        
        return {
            "workflow_id": result.workflow_id,
            "status": result.status,
            "file_info": result.file_info,
            "extracted_data": result.extracted_data,
            "generated_report": result.generated_report,
            "confidence_scores": result.confidence_scores,
            "validation_results": result.validation_results,
            "processing_time": result.processing_time,
            "error_message": result.error_message
        }
        
    except Exception as e:
        # 清理上传的文件
        file_path.unlink(missing_ok=True)
        raise HTTPException(status_code=500, detail=f"工作流处理失败: {str(e)}")


@router.post("/process-uploaded-file")
async def process_uploaded_file_workflow(request: WorkflowRequest):
    """
    处理已上传文件的工作流
    """
    # 查找已上传的文件
    matching_files = list(UPLOAD_DIR.glob(f"{request.file_id}_*"))
    
    if not matching_files:
        raise HTTPException(status_code=404, detail="文件不存在")
    
    file_path = matching_files[0]
    
    # 转换模板字段格式
    template_fields_list = [
        {
            "field_name": field.field_name,
            "description": field.description,
            "example": field.example,
            "format": field.format
        }
        for field in request.template_fields
    ]
    
    try:
        # 执行工作流
        result = await workflow_service.process_document_to_report(
            file_path=str(file_path),
            template_fields=template_fields_list,
            report_template=request.report_template
        )
        
        return {
            "workflow_id": result.workflow_id,
            "status": result.status,
            "file_info": result.file_info,
            "extracted_data": result.extracted_data,
            "generated_report": result.generated_report,
            "confidence_scores": result.confidence_scores,
            "validation_results": result.validation_results,
            "processing_time": result.processing_time,
            "error_message": result.error_message
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"工作流处理失败: {str(e)}")


@router.get("/status/{workflow_id}")
async def get_workflow_status(workflow_id: str):
    """
    获取工作流状态
    """
    status = workflow_service.get_workflow_status(workflow_id)
    
    if not status:
        raise HTTPException(status_code=404, detail="工作流不存在")
    
    return status


@router.get("/demo-fields")
async def get_demo_template_fields():
    """
    获取演示用的模板字段定义
    """
    return {
        "meeting_fields": [
            {
                "field_name": "会议主题",
                "description": "会议讨论的主要议题",
                "example": "数字化转型项目进展讨论",
                "format": "普通文本"
            },
            {
                "field_name": "会议时间",
                "description": "会议举行的具体时间",
                "example": "2024年7月4日 14:00-16:00",
                "format": "日期时间"
            },
            {
                "field_name": "主持人",
                "description": "会议的主持人姓名",
                "example": "张总经理",
                "format": "人名"
            },
            {
                "field_name": "参会人数",
                "description": "参加会议的总人数",
                "example": "5人",
                "format": "数字"
            }
        ],
        "project_fields": [
            {
                "field_name": "项目名称",
                "description": "项目的正式名称",
                "example": "智能报告生成系统",
                "format": "普通文本"
            },
            {
                "field_name": "项目负责人",
                "description": "项目的主要负责人姓名",
                "example": "李明",
                "format": "人名"
            },
            {
                "field_name": "完成进度",
                "description": "项目当前的完成百分比",
                "example": "75%",
                "format": "百分比"
            },
            {
                "field_name": "预计完成时间",
                "description": "项目预计的完成日期",
                "example": "2024年8月31日",
                "format": "日期"
            }
        ]
    }


@router.post("/langgraph/start")
async def start_langgraph_workflow(
    text_input: str = Form(...),
    files: Optional[List[UploadFile]] = File(None)
):
    """
    启动LangGraph智能体工作流
    支持文本输入和文件上传
    """
    try:
        # 处理上传的文件
        file_info_list = []
        if files:
            for file in files:
                # 检查文件是否有效
                if file and hasattr(file, 'filename') and file.filename:
                    # 保存文件
                    file_id = str(uuid.uuid4())
                    safe_filename = f"{file_id}_{file.filename}"
                    file_path = UPLOAD_DIR / safe_filename

                    content = await file.read()
                    with open(file_path, 'wb') as f:
                        f.write(content)

                    # 解析文件内容
                    file_content = ""
                    if file.filename.endswith('.txt'):
                        file_content = content.decode('utf-8', errors='ignore')
                    elif file.filename.endswith('.docx'):
                        # 调用document_service解析DOCX文件
                        try:
                            from services.document_service import document_service
                            doc_result = document_service.parse_document(file_path)
                            if doc_result.success:
                                file_content = doc_result.raw_text
                            else:
                                file_content = f"[DOCX解析失败: {file.filename}]"
                                logger.warning(f"DOCX解析失败: {doc_result.error}")
                        except Exception as e:
                            logger.error(f"DOCX文件解析失败: {e}")
                            file_content = f"[DOCX解析异常: {file.filename}]"

                    file_info = {
                        "filename": file.filename,
                        "file_path": str(file_path),
                        "content": file_content,
                        "size": len(content)
                    }
                    file_info_list.append(file_info)

        # 构建用户输入
        user_input = UserInput(
            text=text_input,
            files=file_info_list,
            session_id=str(uuid.uuid4()),
            user_id="default_user",  # 这里应该从认证中获取
            timestamp=datetime.now()
        )

        # 运行LangGraph工作流
        result = await report_graph.run_workflow(user_input)

        # 提取对话响应内容
        response_content = _extract_conversation_response(result)

        return {
            "success": True,
            "workflow_id": result["workflow_id"],
            "status": result["status"],
            "message": response_content,  # 直接返回对话内容
            "data": {
                "final_state": result.get("final_state"),
                "errors": result.get("errors", []),
                "report_generation": result.get("report_generation"),
                "requires_user_action": _check_requires_user_action(result)
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"LangGraph工作流启动失败: {str(e)}")


def _extract_conversation_response(workflow_result: Dict[str, Any]) -> str:
    """从工作流结果中提取对话响应内容"""
    try:
        # 检查workflow_result是否为None
        if not workflow_result:
            logger.error("工作流结果为None")
            return "抱歉，处理您的请求时出现了问题。"

        final_state = workflow_result.get("final_state")
        if not final_state:
            return "抱歉，处理您的请求时出现了问题。"

        # 获取最后一个状态节点
        last_node_key = list(final_state.keys())[-1] if final_state else None
        if not last_node_key:
            return "抱歉，处理您的请求时出现了问题。"

        last_state = final_state[last_node_key]

        # 尝试从assistant_responses中获取响应
        assistant_responses = last_state.get("assistant_responses", [])
        if assistant_responses:
            return assistant_responses[-1].get("content", "处理完成。")

        # 尝试从intention_result中获取响应
        intention_result = last_state.get("intention_result", {})
        if intention_result.get("user_response"):
            return intention_result["user_response"]

        # 默认响应
        return "好的！我可以帮您生成会议纪要。请上传相关的会议文档或提供会议内容，我将为您选择合适的模板并生成纪要。"

    except Exception as e:
        logger.error(f"提取对话响应失败: {e}")
        return "抱歉，处理您的请求时出现了问题。"


def _check_requires_user_action(workflow_result: Dict[str, Any]) -> bool:
    """检查是否需要用户进一步操作"""
    try:
        final_state = workflow_result.get("final_state")
        if not final_state:
            return False

        last_node_key = list(final_state.keys())[-1] if final_state else None
        if not last_node_key:
            return False

        last_state = final_state[last_node_key]

        # 检查是否有待处理的交互
        pending_interactions = last_state.get("pending_interactions", [])
        if pending_interactions:
            return True

        # 检查assistant_responses中的标记
        assistant_responses = last_state.get("assistant_responses", [])
        if assistant_responses:
            return assistant_responses[-1].get("requires_user_action", False)

        return False

    except Exception as e:
        logger.error(f"检查用户操作需求失败: {e}")
        return False


@router.post("/langgraph/resume/{workflow_id}")
async def resume_langgraph_workflow(
    workflow_id: str,
    user_response: Dict[str, Any]
):
    """
    恢复暂停的LangGraph工作流
    处理人工交互响应
    """
    try:
        print(f"🔧 API: 收到工作流恢复请求 - workflow_id: {workflow_id}, user_response: {user_response}")
        logger.info(f"API: 收到工作流恢复请求 - workflow_id: {workflow_id}, user_response: {user_response}")
        result = await report_graph.resume_workflow(workflow_id, user_response)
        print(f"🔧 API: 工作流恢复结果 - {result.get('status', 'unknown')}")
        logger.info(f"API: 工作流恢复结果 - {result.get('status', 'unknown')}")

        if "error" in result:
            raise HTTPException(status_code=404, detail=result["error"])

        return {
            "success": True,
            "workflow_id": workflow_id,
            "status": result["status"],
            "message": "工作流恢复成功",
            "data": {
                "final_state": result.get("final_state")
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"工作流恢复失败: {str(e)}")


@router.get("/langgraph/status/{workflow_id}")
async def get_workflow_status(workflow_id: str):
    """
    获取工作流状态
    """
    try:
        # 首先尝试从持久化存储中获取状态
        from services.workflow_state_service import workflow_state_service

        persistent_state = workflow_state_service.get_workflow_state(workflow_id)
        if persistent_state:
            logger.info(f"从持久化存储获取工作流状态: {workflow_id}")
            return {
                "success": True,
                **persistent_state
            }

        # 如果持久化存储中没有，尝试从LangGraph获取
        config = {"configurable": {"thread_id": workflow_id}}
        state = await report_graph.graph.aget_state(config)

        if not state or not state.values:
            raise HTTPException(status_code=404, detail="工作流未找到")

        current_state = state.values

        # 构建完整的状态响应
        response = {
            "success": True,
            "workflow_id": workflow_id,
            "current_stage": current_state.get("current_stage"),
            "next_action": current_state.get("next_action"),
            "pending_interactions": current_state.get("pending_interactions", []),
            "human_interactions": current_state.get("human_interactions", []),
            "extraction_stats": current_state.get("extraction_stats", {}),
            "validation_stats": current_state.get("validation_stats", {}),
            "extracted_data": current_state.get("extracted_data", {}),
            "extraction_results": current_state.get("extraction_results", []),
            "template_info": current_state.get("template_info"),
            "errors": current_state.get("errors", []),
            "warnings": current_state.get("warnings", []),
            "created_at": current_state.get("created_at"),
            "updated_at": current_state.get("updated_at"),
            "requires_user_action": len(current_state.get("pending_interactions", [])) > 0
        }

        return response

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取工作流状态失败: {str(e)}")
