services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: report_gen_postgres
    environment:
      POSTGRES_DB: report_gen
      POSTGRES_USER: report_user
      POSTGRES_PASSWORD: report_pass
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - report_gen_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U report_user -d report_gen"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: report_gen_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - report_gen_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 后端服务 (开发环境)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: report_gen_backend
    environment:
      - DATABASE_URL=**************************************************/report_gen
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=development
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - backend_uploads:/app/uploads
    networks:
      - report_gen_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    command: uvicorn app:app --host 0.0.0.0 --port 8000 --reload

  # 前端服务 (开发环境)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: report_gen_frontend
    environment:
      - VITE_API_BASE_URL=http://localhost:8000
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - report_gen_network
    depends_on:
      - backend
    restart: unless-stopped
    command: npm run dev -- --host 0.0.0.0 --port 3000

  # Celery Worker (后台任务处理)
  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: report_gen_celery
    environment:
      - DATABASE_URL=**************************************************/report_gen
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=development
    volumes:
      - ./backend:/app
      - backend_uploads:/app/uploads
    networks:
      - report_gen_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    command: celery -A app.celery worker --loglevel=info

  # pgAdmin (数据库管理工具)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: report_gen_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - report_gen_network
    depends_on:
      - postgres
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_uploads:
    driver: local
  pgadmin_data:
    driver: local

networks:
  report_gen_network:
    driver: bridge
    name: report_gen_network
