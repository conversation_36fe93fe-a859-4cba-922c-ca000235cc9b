"""
信息抽取智能体
负责从文档中抽取指定字段的信息
"""

import json
from typing import Dict, List, Any
from agents.dialogue_agent import BaseDialogueAgent, AgentState
from agents.base_agent import AgentResult
from services.llm_service import ExtractionRequest, llm_service


class ExtractionAgent(BaseDialogueAgent):
    """信息抽取智能体"""
    
    def __init__(self):
        system_prompt = """你是一个专业的信息抽取助手。你的主要任务是：
1. 从用户提供的文档中准确抽取指定字段的信息
2. 根据字段描述和示例进行精确匹配
3. 评估抽取结果的置信度
4. 在不确定时主动寻求人工帮助

请始终保持准确性和专业性。"""

        super().__init__(
            agent_name="ExtractionAgent",
            system_prompt=system_prompt
        )
    
    async def process(self, state: AgentState) -> AgentState:
        """
        执行信息抽取流程
        
        Args:
            state: 当前状态
            
        Returns:
            AgentState: 更新后的状态
        """
        self.update_stage(state, "信息抽取阶段")
        
        try:
            # 获取文档内容和模板字段
            document_content = state.document_info.get("content", "")
            template_fields = state.template_info.get("fields", [])
            
            if not document_content:
                self.log_error("文档内容为空", state)
                return state
            
            if not template_fields:
                self.log_error("模板字段为空", state)
                return state
            
            self.log_info(f"开始抽取 {len(template_fields)} 个字段")
            
            # 逐个抽取字段
            extraction_results = {}
            confidence_scores = {}
            
            for field in template_fields:
                field_name = field.get("field_name")
                if not field_name:
                    continue
                
                self.log_info(f"抽取字段: {field_name}")
                
                # 构建抽取请求
                request = ExtractionRequest(
                    field_name=field_name,
                    field_description=field.get("description", ""),
                    field_example=field.get("example", ""),
                    field_format=field.get("format", "普通文本"),
                    source_text=document_content
                )
                
                # 执行抽取
                result = await self._extract_single_field(request)
                
                if result.success:
                    extraction_results[field_name] = result.data.get("value", "")
                    confidence_scores[field_name] = result.data.get("confidence", 0.0)
                    self.log_info(f"字段 {field_name} 抽取成功，置信度: {confidence_scores[field_name]:.2f}")
                else:
                    extraction_results[field_name] = ""
                    confidence_scores[field_name] = 0.0
                    self.log_error(f"字段 {field_name} 抽取失败: {result.message}", state)
            
            # 更新状态
            state.extracted_data.update(extraction_results)
            state.confidence_scores.update(confidence_scores)
            
            # 统计抽取结果
            total_fields = len(template_fields)
            extracted_fields = len([v for v in extraction_results.values() if v and v != "未找到"])
            success_rate = extracted_fields / total_fields if total_fields > 0 else 0
            
            self.log_info(f"抽取完成: {extracted_fields}/{total_fields} 个字段，成功率: {success_rate:.2%}")
            
            # 记录抽取统计
            state.generation_results["extraction_stats"] = {
                "total_fields": total_fields,
                "extracted_fields": extracted_fields,
                "success_rate": success_rate,
                "avg_confidence": sum(confidence_scores.values()) / len(confidence_scores) if confidence_scores else 0
            }
            
        except Exception as e:
            self.log_error(f"信息抽取过程异常: {e}", state)
        
        return state
    
    async def _extract_single_field(self, request: ExtractionRequest) -> AgentResult:
        """
        抽取单个字段
        
        Args:
            request: 抽取请求
            
        Returns:
            AgentResult: 抽取结果
        """
        try:
            # 调用LLM进行抽取
            llm_response = await llm_service.extract_field_info(request)
            
            if not llm_response.success:
                return AgentResult(
                    success=False,
                    message=f"LLM调用失败: {llm_response.error}",
                    data={}
                )
            
            # 解析抽取结果
            extracted_value = llm_response.content.strip()
            
            # 计算置信度（基于响应质量和内容长度）
            confidence = self._calculate_confidence(extracted_value, request)
            
            return AgentResult(
                success=True,
                message="抽取成功",
                data={
                    "value": extracted_value,
                    "confidence": confidence,
                    "usage": llm_response.usage
                }
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                message=f"抽取异常: {e}",
                data={}
            )
    
    def _calculate_confidence(self, extracted_value: str, request: ExtractionRequest) -> float:
        """
        计算抽取置信度
        
        Args:
            extracted_value: 抽取的值
            request: 抽取请求
            
        Returns:
            float: 置信度分数 (0-1)
        """
        if not extracted_value or extracted_value == "未找到":
            return 0.0
        
        confidence = 0.5  # 基础置信度
        
        # 基于内容长度调整
        if len(extracted_value) > 5:
            confidence += 0.2
        
        # 基于格式匹配调整
        field_format = request.field_format.lower()
        if "日期" in field_format or "时间" in field_format:
            # 检查是否包含日期时间格式
            import re
            date_patterns = [
                r'\d{4}[-/年]\d{1,2}[-/月]\d{1,2}',
                r'\d{1,2}[-/月]\d{1,2}[-/日]',
                r'\d{4}年\d{1,2}月\d{1,2}日'
            ]
            if any(re.search(pattern, extracted_value) for pattern in date_patterns):
                confidence += 0.2
        
        elif "数字" in field_format or "金额" in field_format:
            # 检查是否包含数字
            import re
            if re.search(r'\d+', extracted_value):
                confidence += 0.2
        
        elif "人名" in field_format:
            # 检查是否为合理的人名长度
            if 2 <= len(extracted_value) <= 10:
                confidence += 0.2
        
        # 基于示例相似度调整
        if request.field_example and len(request.field_example) > 0:
            # 简单的相似度检查（可以后续优化为更复杂的算法）
            example_len = len(request.field_example)
            value_len = len(extracted_value)
            if abs(example_len - value_len) / max(example_len, value_len) < 0.5:
                confidence += 0.1
        
        return min(confidence, 1.0)
    
    async def validate_extraction_results(self, state: AgentState) -> AgentState:
        """
        验证抽取结果
        
        Args:
            state: 当前状态
            
        Returns:
            AgentState: 更新后的状态
        """
        self.update_stage(state, "抽取结果验证")
        
        try:
            document_content = state.document_info.get("content", "")
            template_fields = state.template_info.get("fields", [])
            extracted_data = state.extracted_data
            
            validation_results = {}
            
            for field in template_fields:
                field_name = field.get("field_name")
                if field_name not in extracted_data:
                    continue
                
                field_value = extracted_data[field_name]
                field_format = field.get("format", "普通文本")
                
                # 调用LLM验证
                llm_response = await llm_service.validate_extracted_data(
                    field_name=field_name,
                    field_value=field_value,
                    field_format=field_format,
                    original_text=document_content
                )
                
                if llm_response.success:
                    try:
                        # 解析验证结果
                        validation_data = json.loads(llm_response.content)
                        validation_results[field_name] = validation_data
                        
                        # 更新置信度
                        if validation_data.get("is_valid", False):
                            new_confidence = validation_data.get("confidence", 0.5)
                            state.confidence_scores[field_name] = new_confidence
                        else:
                            state.confidence_scores[field_name] = 0.1
                            
                    except json.JSONDecodeError:
                        validation_results[field_name] = {
                            "is_valid": False,
                            "confidence": 0.1,
                            "reason": "验证结果解析失败"
                        }
                else:
                    validation_results[field_name] = {
                        "is_valid": False,
                        "confidence": 0.1,
                        "reason": f"验证调用失败: {llm_response.error}"
                    }
            
            state.validation_results.update(validation_results)
            
            # 统计验证结果
            valid_count = sum(1 for v in validation_results.values() if v.get("is_valid", False))
            total_count = len(validation_results)
            validation_rate = valid_count / total_count if total_count > 0 else 0
            
            self.log_info(f"验证完成: {valid_count}/{total_count} 个字段通过验证，通过率: {validation_rate:.2%}")
            
        except Exception as e:
            self.log_error(f"验证过程异常: {e}", state)
        
        return state
