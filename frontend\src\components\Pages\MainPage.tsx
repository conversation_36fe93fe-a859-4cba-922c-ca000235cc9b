/**
 * 主页面组件
 * 智能体对话界面和报告生成工作流
 */

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { 
  Card, 
  Input, 
  Button, 
  Space, 
  Typography, 
  Upload, 
  message, 
  List,
  Avatar,
  Divider,
  Tag,
  Spin,
  Alert
} from 'antd';
import {
  SendOutlined,
  PaperClipOutlined,
  RobotOutlined,
  UserOutlined,
  FileTextOutlined,
  LoadingOutlined
} from '@ant-design/icons';

const { TextArea } = Input;
const { Text, Title } = Typography;
const { Dragger } = Upload;

interface Message {
  id: string;
  type: 'user' | 'agent' | 'system';
  content: string;
  timestamp: Date;
  files?: Array<{
    name: string;
    url: string;
    type: string;
  }>;
  status?: 'sending' | 'sent' | 'error';
  requiresInteraction?: boolean;
  interactionData?: {
    workflowId: string;
    interactionType: string;
    templates?: Array<{
      template_id: string;
      template_name: string;
      description: string;
    }>;
    options?: Array<{
      value: string;
      label: string;
    }>;
    // 数据确认相关字段
    extractionResults?: Array<{
      field_name: string;
      field_value: string;
      confidence: number;
      source_location?: string;
      extraction_method?: string;
    }>;
    extractedData?: Record<string, any>;
    extractionStats?: {
      total_fields: number;
      extracted_fields: number;
      success_rate: number;
      avg_confidence: number;
    };
  };
}

const MainPage: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);
  const [processedInteractions, setProcessedInteractions] = useState<Set<string>>(new Set());
  const [activeWorkflowId, setActiveWorkflowId] = useState<string | null>(null);
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 工作流状态轮询
  const startWorkflowPolling = useCallback((workflowId: string) => {
    console.log('开始轮询工作流状态:', workflowId);

    // 清除现有轮询
    if (pollingInterval) {
      clearInterval(pollingInterval);
    }

    setActiveWorkflowId(workflowId);

    const interval = setInterval(async () => {
      try {
        console.log('轮询工作流状态:', workflowId);
        const response = await fetch(`http://localhost:8000/api/workflow/langgraph/status/${workflowId}`);

        if (response.ok) {
          const statusData = await response.json();
          console.log('轮询状态结果:', statusData);

          // 检查是否需要用户交互
          if (statusData.requires_user_action && statusData.pending_interactions && statusData.pending_interactions.length > 0) {
            const pendingInteractionType = statusData.pending_interactions[0];
            const humanInteractions = statusData.human_interactions || [];
            const pendingInteraction = humanInteractions.find(hi => !hi.is_completed);

            console.log('发现待处理交互:', pendingInteractionType, pendingInteraction);

            // 处理数据确认
            if (pendingInteraction && (pendingInteraction.interaction_type === 'supplement' || pendingInteractionType === 'supplement')) {
              console.log('显示数据确认界面');

              const dataConfirmationMessage: Message = {
                id: Date.now().toString(),
                type: 'agent',
                content: '✅ 数据提取完成！请确认以下提取的信息：',
                timestamp: new Date(),
                requiresInteraction: true,
                interactionData: {
                  workflowId: workflowId,
                  interactionType: 'data_confirmation',
                  extractionResults: statusData.extraction_results || [],
                  extractedData: statusData.extracted_data || {},
                  extractionStats: statusData.extraction_stats || {}
                }
              };

              setMessages(prev => {
                // 避免重复添加相同的数据确认消息
                const hasDataConfirmation = prev.some(msg =>
                  msg.interactionData?.interactionType === 'data_confirmation' &&
                  msg.interactionData?.workflowId === workflowId
                );

                if (!hasDataConfirmation) {
                  return [...prev, dataConfirmationMessage];
                }
                return prev;
              });

              // 停止轮询，等待用户操作
              stopWorkflowPolling();
            }
          }
          // 如果工作流完成，停止轮询
          else if (!statusData.requires_user_action) {
            console.log('工作流完成，停止轮询');
            stopWorkflowPolling();

            // 添加完成消息
            const completionMessage: Message = {
              id: Date.now().toString(),
              type: 'agent',
              content: '🎉 报告生成完成！',
              timestamp: new Date()
            };

            setMessages(prev => [...prev, completionMessage]);
          }
        }
      } catch (error) {
        console.error('轮询工作流状态失败:', error);
      }
    }, 3000); // 每3秒轮询一次

    setPollingInterval(interval);
  }, [pollingInterval]);

  const stopWorkflowPolling = useCallback(() => {
    console.log('停止轮询工作流状态');
    if (pollingInterval) {
      clearInterval(pollingInterval);
      setPollingInterval(null);
    }
    setActiveWorkflowId(null);
  }, [pollingInterval]);

  // 组件卸载时清理轮询
  useEffect(() => {
    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }
    };
  }, [pollingInterval]);

  // 初始化欢迎消息
  useEffect(() => {
    const welcomeMessage: Message = {
      id: 'welcome',
      type: 'agent',
      content: '您好！我是报告智能生成助手。我可以帮助您：\n\n1. 📄 分析和处理文档\n2. 🎯 识别合适的报告模板\n3. 📊 提取关键信息\n4. 📝 生成结构化报告\n\n请上传您的文档或直接告诉我您需要什么帮助！',
      timestamp: new Date()
    };
    setMessages([welcomeMessage]);
  }, []);

  const handleSendMessage = async () => {
    if (!inputText.trim() && uploadedFiles.length === 0) {
      message.warning('请输入消息或上传文件');
      return;
    }

    // 创建用户消息
    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputText || '上传了文件',
      timestamp: new Date(),
      files: uploadedFiles.map(file => ({
        name: file.name,
        url: file.url || '',
        type: file.type
      })),
      status: 'sent'
    };

    setMessages(prev => [...prev, userMessage]);
    const currentInputText = inputText;
    const currentFiles = [...uploadedFiles];
    setInputText('');
    setUploadedFiles([]);
    setIsLoading(true);

    try {
      // 调用后端LangGraph工作流API
      const formData = new FormData();
      formData.append('text_input', currentInputText);

      // 添加文件（只有当文件存在且有效时）
      currentFiles.forEach((file, index) => {
        if (file && file.size > 0) {
          formData.append('files', file);
        }
      });

      console.log('发送请求到:', 'http://localhost:8000/api/workflow/langgraph/start');
      console.log('请求数据:', { text: currentInputText, files: currentFiles.length });
      console.log('FormData内容:');
      for (let [key, value] of formData.entries()) {
        console.log(key, value);
      }

      const response = await fetch('http://localhost:8000/api/workflow/langgraph/start', {
        method: 'POST',
        body: formData,
        cache: 'no-cache',
      });

      console.log('响应状态:', response.status, response.statusText);
      console.log('响应头:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.error('响应错误内容:', errorText);
        throw new Error(`HTTP error! status: ${response.status}, content: ${errorText}`);
      }

      const result = await response.json();
      console.log('解析后的JSON响应:', result);

      // 处理响应
      let agentResponseContent = '';

      console.log('API响应:', result); // 添加调试日志

      if (result.success) {
        // 修复：API返回的是平坦结构，不是嵌套结构
        const workflowState = result.data || result; // 兼容不同的响应格式
        const requiresUserAction = workflowState?.requires_user_action;
        console.log('Workflow State:', workflowState); // 添加调试日志
        console.log('Requires User Action:', requiresUserAction); // 添加调试日志

        if (workflowState) {
          // 检查是否需要用户交互
          if (requiresUserAction && workflowState.pending_interactions && workflowState.pending_interactions.length > 0) {
            const pendingInteractionType = workflowState.pending_interactions[0];
            const humanInteractions = workflowState.human_interactions || [];

            // 找到最后一个未完成的交互
            const pendingInteraction = humanInteractions.find(hi => !hi.is_completed);

            console.log('Pending Interaction Type:', pendingInteractionType); // 调试日志
            console.log('Pending Interaction:', pendingInteraction); // 调试日志

            if (pendingInteraction) {
              // 处理模板确认
              if (pendingInteraction.interaction_type === 'confirm' && pendingInteraction.field_name === 'template_confirmation') {
                agentResponseContent = '我识别到您需要生成会议纪要。系统找到了以下匹配的模板：';

                const interactionMessage: Message = {
                  id: Date.now().toString(),
                  type: 'agent',
                  content: agentResponseContent,
                  timestamp: new Date(),
                  requiresInteraction: true,
                  interactionData: {
                    workflowId: result.workflow_id,
                    interactionType: 'template_confirmation',
                    templates: pendingInteraction.original_value?.templates || [],
                    options: pendingInteraction.original_value?.options || []
                  }
                };

                setMessages(prev => [...prev, interactionMessage]);
                setInputText('');
                setUploadedFiles([]);
                setIsLoading(false);
                return;
              }
              // 处理数据确认
              else if (pendingInteraction.interaction_type === 'supplement' || pendingInteractionType === 'supplement') {
                agentResponseContent = '✅ 数据提取完成！请确认以下提取的信息：';

                const interactionMessage: Message = {
                  id: Date.now().toString(),
                  type: 'agent',
                  content: agentResponseContent,
                  timestamp: new Date(),
                  requiresInteraction: true,
                  interactionData: {
                    workflowId: result.workflow_id,
                    interactionType: 'data_confirmation',
                    extractionResults: workflowState.extraction_results || [],
                    extractedData: workflowState.extracted_data || {},
                    extractionStats: workflowState.extraction_stats || {}
                  }
                };

                setMessages(prev => [...prev, interactionMessage]);
                setInputText('');
                setUploadedFiles([]);
                setIsLoading(false);
                return;
              }
            }
          }

          // 正常响应处理
          // 检查助手响应
          if (workflowState.assistant_responses && workflowState.assistant_responses.length > 0) {
            const lastResponse = workflowState.assistant_responses[workflowState.assistant_responses.length - 1];
            agentResponseContent = lastResponse.content;
          }
          // 检查意图识别的用户响应
          else if (workflowState.intention_result?.user_response) {
            agentResponseContent = workflowState.intention_result.user_response;
          }
          // 检查是否是拒绝状态
          else if (workflowState.status === 'completed_with_rejection') {
            agentResponseContent = '抱歉，我只能帮助您生成报告。如果您需要生成报告，请告诉我具体的报告类型并上传相关文档。';
          }
          // 检查当前阶段
          else if (workflowState.current_stage === 'data_extraction' && workflowState.extraction_results) {
            agentResponseContent = `✅ 数据提取完成！成功提取了 ${workflowState.extraction_results.length} 个字段，平均置信度 ${((workflowState.extraction_stats?.avg_confidence || 0) * 100).toFixed(1)}%。`;
          }
          // 默认响应
          else {
            agentResponseContent = '我已经收到您的请求，正在处理中...';
          }
        } else {
          agentResponseContent = '处理完成，但未获取到响应内容。';
        }
      } else {
        agentResponseContent = '处理请求时出现问题，请重试。';
      }

      const agentResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'agent',
        content: agentResponseContent,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, agentResponse]);
      setIsLoading(false);

    } catch (error) {
      console.error('发送消息失败:', error);

      let errorMessage = '抱歉，处理您的请求时出现了问题。请检查网络连接后重试。';

      // 根据错误类型提供更具体的错误信息
      if (error instanceof Error) {
        if (error.message.includes('Failed to fetch')) {
          errorMessage = '无法连接到服务器，请检查网络连接或稍后重试。';
        } else if (error.message.includes('HTTP error')) {
          errorMessage = `服务器响应错误：${error.message}`;
        } else {
          errorMessage = `处理请求时出错：${error.message}`;
        }
      }

      const errorResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'agent',
        content: errorMessage,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorResponse]);
      setIsLoading(false);
      message.error('发送失败，请重试');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 基础网络测试函数
  const handleTestNetwork = async () => {
    console.log('开始基础网络测试...');

    const testMessage: Message = {
      id: Date.now().toString(),
      type: 'agent',
      content: '开始网络连接测试...',
      timestamp: new Date()
    };
    setMessages(prev => [...prev, testMessage]);

    // 测试1: 基本的GET请求到后端根路径
    try {
      console.log('测试1: GET请求到后端根路径');
      const response1 = await fetch('http://localhost:8000/');
      console.log('测试1结果:', response1.status, response1.statusText);

      if (response1.ok) {
        const result1 = await response1.json();
        console.log('测试1响应:', result1);

        const successMessage: Message = {
          id: Date.now().toString(),
          type: 'agent',
          content: `✅ 测试1成功: 后端根路径可访问\n状态: ${response1.status}\n响应: ${JSON.stringify(result1, null, 2)}`,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, successMessage]);
      } else {
        throw new Error(`HTTP ${response1.status}: ${response1.statusText}`);
      }
    } catch (error) {
      console.error('测试1失败:', error);
      const errorMessage: Message = {
        id: Date.now().toString(),
        type: 'agent',
        content: `❌ 测试1失败: ${error}`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
      return; // 如果基本连接都失败，不继续后续测试
    }

    // 测试2: POST请求到API端点 (使用fetch)
    try {
      console.log('测试2: POST请求到API端点 (fetch)');
      const formData = new FormData();
      formData.append('text_input', '测试消息');

      console.log('请求配置:', {
        url: 'http://localhost:8000/api/workflow/langgraph/start',
        method: 'POST',
        body: formData
      });

      const response2 = await fetch('http://localhost:8000/api/workflow/langgraph/start', {
        method: 'POST',
        body: formData,
        cache: 'no-cache',
      });

      console.log('测试2结果:', response2.status, response2.statusText);
      console.log('响应头:', Object.fromEntries(response2.headers.entries()));

      if (response2.ok) {
        const result2 = await response2.json();
        console.log('测试2响应:', result2);

        const successMessage: Message = {
          id: Date.now().toString(),
          type: 'agent',
          content: `✅ 测试2成功: API端点可访问\n状态: ${response2.status}\n响应: ${JSON.stringify(result2, null, 2)}`,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, successMessage]);
      } else {
        const errorText = await response2.text();
        throw new Error(`HTTP ${response2.status}: ${errorText}`);
      }
    } catch (error) {
      console.error('测试2失败:', error);
      const errorMessage: Message = {
        id: Date.now().toString(),
        type: 'agent',
        content: `❌ 测试2失败: ${error}`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    }

    // 测试3: 使用XMLHttpRequest
    try {
      console.log('测试3: POST请求到API端点 (XMLHttpRequest)');

      const xhr = new XMLHttpRequest();
      const formData = new FormData();
      formData.append('text_input', '测试消息XMLHttpRequest');

      const xhrPromise = new Promise((resolve, reject) => {
        xhr.onreadystatechange = function() {
          if (xhr.readyState === 4) {
            if (xhr.status === 200) {
              resolve(JSON.parse(xhr.responseText));
            } else {
              reject(new Error(`HTTP ${xhr.status}: ${xhr.responseText}`));
            }
          }
        };

        xhr.onerror = function() {
          reject(new Error('网络错误'));
        };
      });

      xhr.open('POST', 'http://localhost:8000/api/workflow/langgraph/start', true);
      xhr.send(formData);

      const result3 = await xhrPromise;
      console.log('测试3响应:', result3);

      const successMessage: Message = {
        id: Date.now().toString(),
        type: 'agent',
        content: `✅ 测试3成功: XMLHttpRequest可用\n响应: ${JSON.stringify(result3, null, 2)}`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, successMessage]);

    } catch (error) {
      console.error('测试3失败:', error);
      const errorMessage: Message = {
        id: Date.now().toString(),
        type: 'agent',
        content: `❌ 测试3失败: ${error}`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    }
  };

  const uploadProps = {
    name: 'file',
    multiple: true,
    accept: '.docx,.txt,.pdf',
    beforeUpload: (file: any) => {
      const isValidType = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
                         file.type === 'text/plain' ||
                         file.type === 'application/pdf';
      
      if (!isValidType) {
        message.error('只支持 DOCX、TXT、PDF 格式的文件');
        return false;
      }

      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('文件大小不能超过 10MB');
        return false;
      }

      setUploadedFiles(prev => [...prev, file]);
      return false; // 阻止自动上传
    },
    onRemove: (file: any) => {
      setUploadedFiles(prev => prev.filter(f => f.uid !== file.uid));
    },
    fileList: uploadedFiles
  };

  // 处理用户交互响应
  const handleUserInteraction = async (workflowId: string, userChoice: string, additionalData?: any) => {
    // 标记这个交互已被处理
    setProcessedInteractions(prev => new Set(prev).add(workflowId));
    setIsLoading(true);

    try {
      // 根据用户选择显示不同的系统消息
      let systemMessageContent = '';
      switch (userChoice) {
        case 'confirm':
          systemMessageContent = '✅ 已确认使用推荐模板，正在生成报告...';
          break;
        case 'select_other':
          systemMessageContent = '📋 您选择了其他模板，请联系管理员配置更多模板选项';
          break;
        case 'cancel':
          systemMessageContent = '❌ 已取消报告生成';
          break;
        default:
          systemMessageContent = `✅ 已选择: ${userChoice}`;
      }

      const systemMessage: Message = {
        id: Date.now().toString(),
        type: 'system',
        content: systemMessageContent,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, systemMessage]);

      // 如果是取消操作，不需要调用后端API
      if (userChoice === 'cancel') {
        setIsLoading(false);
        return;
      }

      // 如果是选择其他模板，暂时不支持，显示提示信息
      if (userChoice === 'select_other') {
        const infoMessage: Message = {
          id: (Date.now() + 1).toString(),
          type: 'agent',
          content: '抱歉，目前系统只支持推荐的模板。如需使用其他模板，请联系管理员添加更多模板选项。您可以重新开始并选择确认使用推荐模板。',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, infoMessage]);
        setIsLoading(false);
        return;
      }

      // 只有确认选择时才调用后端API继续工作流
      const response = await fetch(`http://localhost:8000/api/workflow/langgraph/resume/${workflowId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: userChoice,
          ...additionalData
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('Resume workflow result:', result);

      // 如果是模板确认，启动状态轮询
      if (userChoice === 'confirm' && result.status === 'resumed') {
        console.log('模板确认成功，启动状态轮询');
        startWorkflowPolling(workflowId);
      }

      // 处理工作流继续的响应
      if (result.success && result.data?.final_state) {
        const finalState = result.data.final_state;
        const lastNodeKey = Object.keys(finalState).pop();
        const lastState = lastNodeKey ? finalState[lastNodeKey] : null;

        if (lastState) {
          let responseContent = '工作流已继续执行...';

          if (lastState.assistant_responses && lastState.assistant_responses.length > 0) {
            const lastResponse = lastState.assistant_responses[lastState.assistant_responses.length - 1];
            responseContent = lastResponse.content;
          } else if (lastState.intention_result?.user_response) {
            responseContent = lastState.intention_result.user_response;
          }

          const agentMessage: Message = {
            id: (Date.now() + 1).toString(),
            type: 'agent',
            content: responseContent,
            timestamp: new Date()
          };
          setMessages(prev => [...prev, agentMessage]);
        }
      }

    } catch (error) {
      console.error('Resume workflow failed:', error);
      const errorMessage: Message = {
        id: Date.now().toString(),
        type: 'agent',
        content: `❌ 处理交互失败: ${error}`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const renderMessage = (message: Message) => {
    const isUser = message.type === 'user';
    const isSystem = message.type === 'system';

    return (
      <div
        key={message.id}
        style={{
          display: 'flex',
          justifyContent: isUser ? 'flex-end' : 'flex-start',
          marginBottom: '16px'
        }}
      >
        <div style={{
          display: 'flex',
          flexDirection: isUser ? 'row-reverse' : 'row',
          alignItems: 'flex-start',
          maxWidth: '80%'
        }}>
          <Avatar
            icon={isUser ? <UserOutlined /> : <RobotOutlined />}
            style={{
              backgroundColor: isUser ? '#1890ff' : '#52c41a',
              margin: isUser ? '0 0 0 8px' : '0 8px 0 0'
            }}
          />
          
          <div style={{
            background: isUser ? '#1890ff' : '#f6f6f6',
            color: isUser ? '#fff' : '#000',
            padding: '12px 16px',
            borderRadius: '12px',
            borderTopLeftRadius: isUser ? '12px' : '4px',
            borderTopRightRadius: isUser ? '4px' : '12px',
            wordBreak: 'break-word',
            whiteSpace: 'pre-wrap'
          }}>
            <div>{message.content}</div>

            {message.files && message.files.length > 0 && (
              <div style={{ marginTop: '8px' }}>
                {message.files.map((file, index) => (
                  <Tag key={index} icon={<FileTextOutlined />} style={{ margin: '2px' }}>
                    {file.name}
                  </Tag>
                ))}
              </div>
            )}

            {/* 交互界面 */}
            {message.requiresInteraction && message.interactionData && (
              <div style={{ marginTop: '12px' }}>
                {message.interactionData.interactionType === 'template_confirmation' && (
                  <div>
                    {message.interactionData.templates && message.interactionData.templates.length > 0 && (
                      <div style={{ marginBottom: '8px' }}>
                        <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>推荐模板:</div>
                        {message.interactionData.templates.map((template, index) => (
                          <div key={index} style={{
                            background: 'rgba(255,255,255,0.1)',
                            padding: '8px',
                            borderRadius: '4px',
                            marginBottom: '4px'
                          }}>
                            <div style={{ fontWeight: 'bold' }}>{template.template_name}</div>
                            <div style={{ fontSize: '12px', opacity: 0.8 }}>{template.description}</div>
                          </div>
                        ))}
                      </div>
                    )}

                    {message.interactionData.options && (
                      <Space wrap>
                        {message.interactionData.options.map((option, index) => {
                          const isProcessed = processedInteractions.has(message.interactionData!.workflowId);
                          return (
                            <Button
                              key={index}
                              type={option.value === 'confirm' ? 'primary' : 'default'}
                              size="small"
                              disabled={isProcessed}
                              onClick={() => handleUserInteraction(
                                message.interactionData!.workflowId,
                                option.value
                              )}
                              style={{
                                color: isUser ? '#fff' : undefined,
                                borderColor: isUser ? 'rgba(255,255,255,0.5)' : undefined
                              }}
                            >
                              {option.label}
                            </Button>
                          );
                        })}
                      </Space>
                    )}
                  </div>
                )}

                {/* 数据确认界面 */}
                {message.interactionData.interactionType === 'data_confirmation' && (
                  <div>
                    {/* 提取统计信息 */}
                    {message.interactionData.extractionStats && (
                      <div style={{
                        background: 'rgba(255,255,255,0.1)',
                        padding: '12px',
                        borderRadius: '6px',
                        marginBottom: '12px'
                      }}>
                        <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>📊 提取统计</div>
                        <div style={{ fontSize: '14px' }}>
                          <div>总字段数: {message.interactionData.extractionStats.total_fields}</div>
                          <div>成功提取: {message.interactionData.extractionStats.extracted_fields}</div>
                          <div>平均置信度: {((message.interactionData.extractionStats.avg_confidence || 0) * 100).toFixed(1)}%</div>
                        </div>
                      </div>
                    )}

                    {/* 提取结果展示 */}
                    {message.interactionData.extractionResults && message.interactionData.extractionResults.length > 0 && (
                      <div style={{ marginBottom: '12px' }}>
                        <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>📋 提取结果:</div>
                        <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                          {message.interactionData.extractionResults.map((result, index) => (
                            <div key={index} style={{
                              background: 'rgba(255,255,255,0.05)',
                              padding: '8px',
                              borderRadius: '4px',
                              marginBottom: '6px',
                              border: '1px solid rgba(255,255,255,0.1)'
                            }}>
                              <div style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                marginBottom: '4px'
                              }}>
                                <span style={{ fontWeight: 'bold', fontSize: '13px' }}>
                                  {result.field_name}
                                </span>
                                <span style={{
                                  fontSize: '12px',
                                  padding: '2px 6px',
                                  borderRadius: '3px',
                                  background: result.confidence > 0.8 ? 'rgba(82, 196, 26, 0.2)' :
                                             result.confidence > 0.6 ? 'rgba(250, 173, 20, 0.2)' : 'rgba(245, 34, 45, 0.2)',
                                  color: result.confidence > 0.8 ? '#52c41a' :
                                         result.confidence > 0.6 ? '#faad14' : '#f5222d'
                                }}>
                                  {(result.confidence * 100).toFixed(1)}%
                                </span>
                              </div>
                              <div style={{ fontSize: '14px', wordBreak: 'break-word' }}>
                                {result.field_value}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* 确认按钮 */}
                    <Space wrap>
                      <Button
                        type="primary"
                        size="small"
                        disabled={processedInteractions.has(message.interactionData!.workflowId)}
                        onClick={() => handleUserInteraction(
                          message.interactionData!.workflowId,
                          'confirm'
                        )}
                        style={{
                          color: isUser ? '#fff' : undefined,
                          borderColor: isUser ? 'rgba(255,255,255,0.5)' : undefined
                        }}
                      >
                        ✅ 确认数据
                      </Button>
                      <Button
                        size="small"
                        disabled={processedInteractions.has(message.interactionData!.workflowId)}
                        onClick={() => handleUserInteraction(
                          message.interactionData!.workflowId,
                          'modify'
                        )}
                        style={{
                          color: isUser ? '#fff' : undefined,
                          borderColor: isUser ? 'rgba(255,255,255,0.5)' : undefined
                        }}
                      >
                        ✏️ 修改数据
                      </Button>
                    </Space>
                  </div>
                )}
              </div>
            )}
            
            <div style={{
              fontSize: '12px',
              opacity: 0.7,
              marginTop: '4px',
              textAlign: isUser ? 'right' : 'left'
            }}>
              {message.timestamp.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
              })}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 对话区域 */}
      <Card 
        style={{ 
          flex: 1, 
          marginBottom: '16px',
          display: 'flex',
          flexDirection: 'column'
        }}
        bodyStyle={{ 
          flex: 1, 
          padding: '16px',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <div style={{
          flex: 1,
          overflowY: 'auto',
          padding: '0 8px',
          marginBottom: '16px'
        }}>
          {messages.map(renderMessage)}
          {isLoading && (
            <div style={{ textAlign: 'center', padding: '16px' }}>
              <Spin indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />
              <div style={{ marginTop: '8px' }}>
                <Text type="secondary">智能体正在思考中...</Text>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* 文件上传区域 */}
        {uploadedFiles.length > 0 && (
          <div style={{ marginBottom: '12px' }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>已选择文件：</Text>
            <div style={{ marginTop: '4px' }}>
              {uploadedFiles.map((file, index) => (
                <Tag key={index} closable onClose={() => {
                  setUploadedFiles(prev => prev.filter((_, i) => i !== index));
                }}>
                  <FileTextOutlined style={{ marginRight: '4px' }} />
                  {file.name}
                </Tag>
              ))}
            </div>
          </div>
        )}

        {/* 输入区域 */}
        <div style={{ display: 'flex', gap: '8px', alignItems: 'flex-end' }}>
          <Upload {...uploadProps} showUploadList={false}>
            <Button icon={<PaperClipOutlined />} type="text" />
          </Upload>
          
          <TextArea
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="输入您的问题或需求..."
            autoSize={{ minRows: 1, maxRows: 4 }}
            style={{ flex: 1 }}
          />
          
          <Button
            type="primary"
            icon={<SendOutlined />}
            onClick={handleSendMessage}
            disabled={isLoading}
            style={{ height: '32px' }}
          >
            发送
          </Button>
          <Button
            onClick={handleTestNetwork}
            style={{ height: '32px', marginLeft: '8px' }}
          >
            测试连接
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default MainPage;
