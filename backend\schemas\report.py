"""
报告相关Pydantic schemas
"""

from datetime import datetime
from typing import Optional
from uuid import UUID
from pydantic import BaseModel, Field
from .common import BaseResponse


class ReportBase(BaseModel):
    """报告基础模型"""
    
    name: str = Field(..., description="报告名称")


class ReportCreate(ReportBase):
    """创建报告请求模型"""
    
    template_id: UUID = Field(..., description="模板ID")
    data_source_id: Optional[UUID] = Field(None, description="数据源ID")


class ReportResponse(ReportBase, BaseResponse):
    """报告响应模型"""
    
    template_id: UUID
    data_source_id: Optional[UUID] = None
    file_path: Optional[str] = None
    file_size: Optional[int] = None
    status: str = "draft"
    generation_time: Optional[int] = None


class ReportGenerationRequest(BaseModel):
    """报告生成请求模型"""
    
    template_id: UUID = Field(..., description="模板ID")
    data_source_id: Optional[UUID] = Field(None, description="数据源ID")
    report_name: str = Field(..., description="报告名称")
    custom_data: Optional[dict] = Field(None, description="自定义数据")


class ReportGenerationResponse(BaseModel):
    """报告生成响应模型"""
    
    report_id: UUID
    status: str = Field(..., description="生成状态")
    file_path: Optional[str] = None
    generation_time: Optional[int] = None
    message: str = Field(..., description="生成消息")
    
    class Config:
        from_attributes = True


class ReportListResponse(BaseModel):
    """报告列表响应模型"""

    id: UUID
    name: str
    template_id: UUID
    status: str = "draft"
    created_at: datetime
    generation_time: Optional[int] = None
    is_active: bool = True

    class Config:
        from_attributes = True
