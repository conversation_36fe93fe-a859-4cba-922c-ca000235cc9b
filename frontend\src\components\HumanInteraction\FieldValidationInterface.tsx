/**
 * 字段验证界面 - 人工确认抽取结果
 * 替代聊天式界面，提供结构化的字段验证体验
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Button,
  Space,
  Table,
  Input,
  Select,
  Form,
  Modal,
  Progress,
  Alert,
  Tag,
  Tooltip,
  Divider,
  Row,
  Col,
  Steps,
  Collapse,
  message,
  Spin,
} from 'antd';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  EditOutlined,
  EyeOutlined,
  QuestionCircleOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  ReloadOutlined,
  SaveOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Title, Paragraph, Text } = Typography;
const { TextArea } = Input;
const { Panel } = Collapse;

// 字段验证状态枚举
export enum FieldValidationStatus {
  PENDING = 'pending',
  VALIDATED = 'validated',
  REJECTED = 'rejected',
  MODIFIED = 'modified',
  SKIPPED = 'skipped'
}

// 抽取字段数据接口
export interface ExtractedField {
  field_name: string;
  field_description: string;
  field_example: string;
  extracted_value: string;
  confidence: number;
  extraction_method: string;
  original_context: string;
  is_required: boolean;
  validation_status: FieldValidationStatus;
  user_confirmed_value?: string;
  user_rejection_reason?: string;
  suggested_improvements?: string[];
}

// 验证会话接口
export interface ValidationSession {
  session_id: string;
  document_name: string;
  total_fields: number;
  validated_fields: number;
  rejected_fields: number;
  pending_fields: number;
  overall_progress: number;
  current_stage: string;
  extracted_fields: ExtractedField[];
  validation_start_time: Date;
  estimated_completion_time?: Date;
}

interface FieldValidationInterfaceProps {
  session?: ValidationSession;
  onFieldValidate: (fieldName: string, status: FieldValidationStatus, value?: string, reason?: string) => void;
  onSessionComplete: (validatedData: Record<string, string>) => void;
  onSessionCancel: () => void;
  isLoading?: boolean;
}

const FieldValidationInterface: React.FC<FieldValidationInterfaceProps> = ({
  session,
  onFieldValidate,
  onSessionComplete,
  onSessionCancel,
  isLoading = false
}) => {
  const [currentFieldIndex, setCurrentFieldIndex] = useState(0);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [contextModalVisible, setContextModalVisible] = useState(false);
  const [selectedFieldContext, setSelectedFieldContext] = useState<ExtractedField | null>(null);
  const [batchValidationMode, setBatchValidationMode] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    if (session && session.extracted_fields.length > 0) {
      // 找到第一个未验证的字段
      const nextPendingIndex = session.extracted_fields.findIndex(
        field => field.validation_status === FieldValidationStatus.PENDING
      );
      if (nextPendingIndex !== -1) {
        setCurrentFieldIndex(nextPendingIndex);
      }
    }
  }, [session]);

  if (isLoading || !session) {
    return (
      <div style={{ textAlign: 'center', padding: '80px 24px' }}>
        <Spin size="large" />
        <Title level={3} style={{ marginTop: '24px' }}>
          正在处理文档...
        </Title>
        <Paragraph type="secondary">
          正在使用AI抽取字段信息，请稍候...
        </Paragraph>
      </div>
    );
  }

  const currentField = session.extracted_fields[currentFieldIndex];
  const completedFields = session.extracted_fields.filter(
    field => field.validation_status !== FieldValidationStatus.PENDING
  );

  // 获取字段状态颜色
  const getFieldStatusColor = (status: FieldValidationStatus): string => {
    switch (status) {
      case FieldValidationStatus.VALIDATED: return '#52c41a';
      case FieldValidationStatus.REJECTED: return '#f5222d';
      case FieldValidationStatus.MODIFIED: return '#1890ff';
      case FieldValidationStatus.SKIPPED: return '#faad14';
      default: return '#d9d9d9';
    }
  };

  // 获取字段状态图标
  const getFieldStatusIcon = (status: FieldValidationStatus) => {
    switch (status) {
      case FieldValidationStatus.VALIDATED: return <CheckCircleOutlined />;
      case FieldValidationStatus.REJECTED: return <CloseCircleOutlined />;
      case FieldValidationStatus.MODIFIED: return <EditOutlined />;
      case FieldValidationStatus.SKIPPED: return <QuestionCircleOutlined />;
      default: return null;
    }
  };

  // 获取置信度颜色
  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.8) return '#52c41a';
    if (confidence >= 0.6) return '#faad14';
    return '#f5222d';
  };

  // 处理字段验证
  const handleFieldAction = (action: FieldValidationStatus, value?: string, reason?: string) => {
    if (!currentField) return;

    onFieldValidate(currentField.field_name, action, value, reason);
    
    // 移动到下一个未验证字段
    const nextIndex = findNextPendingField(currentFieldIndex + 1);
    if (nextIndex !== -1) {
      setCurrentFieldIndex(nextIndex);
    } else {
      // 所有字段已处理完成
      handleSessionComplete();
    }

    message.success(`字段 "${currentField.field_name}" 已${getActionText(action)}`);
  };

  // 查找下一个待验证字段
  const findNextPendingField = (startIndex: number): number => {
    for (let i = startIndex; i < session.extracted_fields.length; i++) {
      if (session.extracted_fields[i].validation_status === FieldValidationStatus.PENDING) {
        return i;
      }
    }
    return -1;
  };

  // 获取操作文本
  const getActionText = (action: FieldValidationStatus): string => {
    switch (action) {
      case FieldValidationStatus.VALIDATED: return '确认';
      case FieldValidationStatus.REJECTED: return '拒绝';
      case FieldValidationStatus.MODIFIED: return '修改';
      case FieldValidationStatus.SKIPPED: return '跳过';
      default: return '处理';
    }
  };

  // 显示原文上下文
  const showFieldContext = (field: ExtractedField) => {
    setSelectedFieldContext(field);
    setContextModalVisible(true);
  };

  // 编辑字段值
  const startEditField = (field: ExtractedField) => {
    setEditingField(field.field_name);
    form.setFieldsValue({
      field_value: field.user_confirmed_value || field.extracted_value,
      rejection_reason: field.user_rejection_reason || ''
    });
  };

  // 保存编辑
  const saveFieldEdit = async () => {
    try {
      const values = await form.validateFields();
      handleFieldAction(FieldValidationStatus.MODIFIED, values.field_value);
      setEditingField(null);
      form.resetFields();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 完成验证会话
  const handleSessionComplete = () => {
    const validatedData: Record<string, string> = {};
    
    session.extracted_fields.forEach(field => {
      if (field.validation_status === FieldValidationStatus.VALIDATED) {
        validatedData[field.field_name] = field.extracted_value;
      } else if (field.validation_status === FieldValidationStatus.MODIFIED) {
        validatedData[field.field_name] = field.user_confirmed_value || field.extracted_value;
      }
    });

    onSessionComplete(validatedData);
  };

  // 字段列表表格列配置
  const fieldColumns: ColumnsType<ExtractedField> = [
    {
      title: '字段名',
      dataIndex: 'field_name',
      key: 'field_name',
      width: 120,
      render: (text: string, record: ExtractedField) => (
        <Space>
          <Text strong={record === currentField}>{text}</Text>
          {record.is_required && <Tag color="red" size="small">必填</Tag>}
        </Space>
      )
    },
    {
      title: '抽取值',
      dataIndex: 'extracted_value',
      key: 'extracted_value',
      ellipsis: true,
      render: (text: string, record: ExtractedField) => (
        <Text ellipsis={{ tooltip: text }}>
          {record.user_confirmed_value || text || '未找到'}
        </Text>
      )
    },
    {
      title: '置信度',
      dataIndex: 'confidence',
      key: 'confidence',
      width: 90,
      render: (confidence: number) => (
        <Tag color={getConfidenceColor(confidence)}>
          {(confidence * 100).toFixed(0)}%
        </Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'validation_status',
      key: 'validation_status',
      width: 100,
      render: (status: FieldValidationStatus) => (
        <Tag 
          color={getFieldStatusColor(status)} 
          icon={getFieldStatusIcon(status)}
        >
          {getActionText(status)}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record: ExtractedField, index: number) => (
        <Space size="small">
          <Tooltip title="查看原文">
            <Button 
              type="text" 
              size="small" 
              icon={<EyeOutlined />}
              onClick={() => showFieldContext(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              type="text" 
              size="small" 
              icon={<EditOutlined />}
              onClick={() => startEditField(record)}
            />
          </Tooltip>
          <Tooltip title="跳转到此字段">
            <Button 
              type="text" 
              size="small"
              onClick={() => setCurrentFieldIndex(index)}
            >
              #{index + 1}
            </Button>
          </Tooltip>
        </Space>
      )
    }
  ];

  const progressPercent = Math.round((completedFields.length / session.total_fields) * 100);

  return (
    <div style={{ maxWidth: 1400, margin: '0 auto', padding: '24px' }}>
      {/* 页面标题和进度 */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>🔍 字段验证确认</Title>
        <Paragraph type="secondary">
          请逐一确认AI抽取的字段信息，您可以接受、修改或拒绝每个字段的结果。
        </Paragraph>
        
        {/* 整体进度 */}
        <Card size="small" style={{ marginBottom: '16px' }}>
          <Row gutter={[16, 16]} align="middle">
            <Col span={12}>
              <Progress 
                percent={progressPercent}
                status={progressPercent === 100 ? 'success' : 'active'}
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
              />
              <Text type="secondary">
                总体进度: {completedFields.length}/{session.total_fields} 个字段已处理
              </Text>
            </Col>
            <Col span={12} style={{ textAlign: 'right' }}>
              <Space>
                <Tag color="green">已确认: {session.validated_fields}</Tag>
                <Tag color="blue">已修改: {session.extracted_fields.filter(f => f.validation_status === FieldValidationStatus.MODIFIED).length}</Tag>
                <Tag color="red">已拒绝: {session.rejected_fields}</Tag>
                <Tag color="orange">已跳过: {session.extracted_fields.filter(f => f.validation_status === FieldValidationStatus.SKIPPED).length}</Tag>
              </Space>
            </Col>
          </Row>
        </Card>
      </div>

      <Row gutter={[24, 24]}>
        {/* 当前字段验证区域 */}
        <Col span={16}>
          {currentField ? (
            <Card
              title={
                <Space>
                  <Text>当前字段: {currentField.field_name}</Text>
                  <Tag color={currentField.is_required ? 'red' : 'blue'}>
                    {currentField.is_required ? '必填' : '可选'}
                  </Tag>
                  <Tag color={getConfidenceColor(currentField.confidence)}>
                    置信度 {(currentField.confidence * 100).toFixed(0)}%
                  </Tag>
                </Space>
              }
              extra={
                <Text type="secondary">
                  {currentFieldIndex + 1} / {session.total_fields}
                </Text>
              }
            >
              {/* 字段信息 */}
              <div style={{ marginBottom: '16px' }}>
                <Paragraph>
                  <Text strong>字段描述: </Text>
                  {currentField.field_description}
                </Paragraph>
                <Paragraph>
                  <Text strong>期望示例: </Text>
                  <Text code>{currentField.field_example}</Text>
                </Paragraph>
              </div>

              {/* 抽取结果展示 */}
              <Alert
                message="AI 抽取结果"
                description={
                  <div>
                    <div style={{ marginBottom: '8px' }}>
                      <Text strong>抽取值: </Text>
                      <Text 
                        style={{ 
                          fontSize: '16px',
                          padding: '4px 8px',
                          backgroundColor: '#f6ffff',
                          border: '1px solid #b7eb8f',
                          borderRadius: '4px'
                        }}
                      >
                        {currentField.extracted_value || '未找到相关信息'}
                      </Text>
                    </div>
                    <div>
                      <Text type="secondary">
                        抽取方法: {currentField.extraction_method} | 
                        置信度: {(currentField.confidence * 100).toFixed(1)}%
                      </Text>
                      <Button 
                        type="link" 
                        size="small"
                        icon={<EyeOutlined />}
                        onClick={() => showFieldContext(currentField)}
                      >
                        查看原文上下文
                      </Button>
                    </div>
                  </div>
                }
                type={currentField.confidence > 0.7 ? 'success' : currentField.confidence > 0.4 ? 'warning' : 'error'}
                style={{ marginBottom: '16px' }}
              />

              {/* 编辑表单 */}
              {editingField === currentField.field_name && (
                <Card size="small" style={{ marginBottom: '16px' }}>
                  <Form
                    form={form}
                    layout="vertical"
                  >
                    <Form.Item
                      label="修改后的值"
                      name="field_value"
                      rules={[{ required: currentField.is_required, message: '此字段为必填项' }]}
                    >
                      <TextArea rows={3} placeholder="输入正确的字段值..." />
                    </Form.Item>
                    <div style={{ textAlign: 'right' }}>
                      <Space>
                        <Button onClick={() => setEditingField(null)}>取消</Button>
                        <Button type="primary" onClick={saveFieldEdit}>保存修改</Button>
                      </Space>
                    </div>
                  </Form>
                </Card>
              )}

              {/* 操作按钮 */}
              <div style={{ textAlign: 'center', marginTop: '24px' }}>
                <Space size="middle">
                  <Button 
                    type="primary" 
                    size="large"
                    icon={<CheckCircleOutlined />}
                    onClick={() => handleFieldAction(FieldValidationStatus.VALIDATED)}
                    disabled={!currentField.extracted_value}
                  >
                    确认正确
                  </Button>
                  
                  <Button 
                    size="large"
                    icon={<EditOutlined />}
                    onClick={() => startEditField(currentField)}
                  >
                    修改内容
                  </Button>
                  
                  <Button 
                    size="large"
                    danger
                    icon={<CloseCircleOutlined />}
                    onClick={() => handleFieldAction(FieldValidationStatus.REJECTED, undefined, '信息不准确')}
                  >
                    拒绝结果
                  </Button>
                  
                  <Button 
                    size="large"
                    icon={<QuestionCircleOutlined />}
                    onClick={() => handleFieldAction(FieldValidationStatus.SKIPPED)}
                  >
                    暂时跳过
                  </Button>
                </Space>
              </div>

              {/* 改进建议 */}
              {currentField.suggested_improvements && currentField.suggested_improvements.length > 0 && (
                <Alert
                  message="改进建议"
                  description={
                    <ul>
                      {currentField.suggested_improvements.map((suggestion, index) => (
                        <li key={index}>{suggestion}</li>
                      ))}
                    </ul>
                  }
                  type="info"
                  icon={<InfoCircleOutlined />}
                  style={{ marginTop: '16px' }}
                />
              )}
            </Card>
          ) : (
            <Card>
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <CheckCircleOutlined style={{ fontSize: '48px', color: '#52c41a' }} />
                <Title level={3} style={{ marginTop: '16px' }}>
                  所有字段验证完成！
                </Title>
                <Paragraph>
                  您已完成所有字段的验证，可以继续生成报告了。
                </Paragraph>
                <Button type="primary" size="large" onClick={handleSessionComplete}>
                  完成验证，生成报告
                </Button>
              </div>
            </Card>
          )}
        </Col>

        {/* 字段列表侧边栏 */}
        <Col span={8}>
          <Card title="字段列表" size="small">
            <Table
              columns={fieldColumns}
              dataSource={session.extracted_fields}
              rowKey="field_name"
              size="small"
              pagination={false}
              scroll={{ y: 400 }}
              rowClassName={(record, index) => index === currentFieldIndex ? 'ant-table-row-selected' : ''}
            />
          </Card>

          {/* 操作面板 */}
          <Card title="批量操作" size="small" style={{ marginTop: '16px' }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button 
                block 
                icon={<SaveOutlined />}
                onClick={handleSessionComplete}
                disabled={completedFields.length === 0}
              >
                保存当前进度
              </Button>
              <Button 
                block 
                icon={<ReloadOutlined />}
                onClick={() => setCurrentFieldIndex(0)}
              >
                重新开始验证
              </Button>
              <Button 
                block 
                danger
                onClick={onSessionCancel}
              >
                取消验证
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 原文上下文模态框 */}
      <Modal
        title={`字段 "${selectedFieldContext?.field_name}" 的原文上下文`}
        open={contextModalVisible}
        onCancel={() => setContextModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setContextModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={700}
      >
        {selectedFieldContext && (
          <div>
            <Paragraph>
              <Text strong>抽取位置: </Text>
              原文档中的相关段落
            </Paragraph>
            <div style={{
              backgroundColor: '#f6f6f6',
              padding: '16px',
              borderRadius: '6px',
              marginBottom: '16px'
            }}>
              <Text style={{ lineHeight: '1.8' }}>
                {selectedFieldContext.original_context}
              </Text>
            </div>
            <Paragraph>
              <Text strong>抽取方法: </Text>
              {selectedFieldContext.extraction_method}
            </Paragraph>
            <Paragraph>
              <Text strong>置信度评估: </Text>
              <Tag color={getConfidenceColor(selectedFieldContext.confidence)}>
                {(selectedFieldContext.confidence * 100).toFixed(1)}%
              </Tag>
            </Paragraph>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default FieldValidationInterface;