"""
Pydantic schemas包
用于API请求和响应的数据验证
"""

from .template import TemplateCreate, TemplateUpdate, TemplateResponse, TemplateFieldResponse
from .data import DataSourceCreate, DataSourceResponse, ExtractedDataResponse
from .report import ReportCreate, ReportResponse
from .common import BaseResponse, PaginatedResponse

__all__ = [
    "TemplateCreate",
    "TemplateUpdate", 
    "TemplateResponse",
    "TemplateFieldResponse",
    "DataSourceCreate",
    "DataSourceResponse",
    "ExtractedDataResponse",
    "ReportCreate",
    "ReportResponse",
    "BaseResponse",
    "PaginatedResponse",
]
