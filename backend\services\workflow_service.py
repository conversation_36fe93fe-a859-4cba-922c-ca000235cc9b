"""
端到端工作流服务
实现从文档上传到报告生成的完整流程
"""

import uuid
import logging
from typing import Dict, List, Optional
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path

from services.document_service import document_service
from services.llm_service import llm_service, ExtractionRequest
from agents.extraction_agent import ExtractionAgent
from agents.base_agent import BaseAgentState

logger = logging.getLogger(__name__)


@dataclass
class WorkflowResult:
    """工作流结果数据类"""
    workflow_id: str
    status: str  # processing, completed, failed
    file_info: Dict
    extracted_data: Dict
    generated_report: str
    confidence_scores: Dict
    validation_results: Dict
    processing_time: float
    error_message: Optional[str] = None


class WorkflowService:
    """端到端工作流服务"""
    
    def __init__(self):
        self.extraction_agent = ExtractionAgent()
        self.active_workflows = {}  # 存储正在处理的工作流
    
    async def process_document_to_report(
        self, 
        file_path: str, 
        template_fields: List[Dict],
        report_template: Optional[str] = None
    ) -> WorkflowResult:
        """
        处理文档生成报告的完整流程
        
        Args:
            file_path: 文档文件路径
            template_fields: 模板字段定义
            report_template: 报告模板（可选）
            
        Returns:
            WorkflowResult: 工作流结果
        """
        workflow_id = str(uuid.uuid4())
        start_time = datetime.now()
        
        logger.info(f"开始工作流 {workflow_id}: {file_path}")
        
        try:
            # 第一步：文档解析
            logger.info(f"[{workflow_id}] 步骤1: 文档解析")
            doc_result = document_service.parse_document(file_path)
            
            if not doc_result.success:
                return WorkflowResult(
                    workflow_id=workflow_id,
                    status="failed",
                    file_info={},
                    extracted_data={},
                    generated_report="",
                    confidence_scores={},
                    validation_results={},
                    processing_time=0,
                    error_message=f"文档解析失败: {doc_result.error}"
                )
            
            file_info = {
                "filename": doc_result.file_name,
                "document_type": doc_result.document_type.value,
                "text_length": len(doc_result.raw_text),
                "paragraph_count": len(doc_result.paragraphs),
                "table_count": len(doc_result.tables)
            }
            
            # 第二步：信息抽取
            logger.info(f"[{workflow_id}] 步骤2: 信息抽取")
            extraction_result = await self._extract_information(
                workflow_id, doc_result.raw_text, template_fields
            )
            
            # 第三步：报告生成
            logger.info(f"[{workflow_id}] 步骤3: 报告生成")
            report_result = await self._generate_report(
                workflow_id, extraction_result, report_template
            )
            
            # 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = WorkflowResult(
                workflow_id=workflow_id,
                status="completed",
                file_info=file_info,
                extracted_data=extraction_result["extracted_data"],
                generated_report=report_result,
                confidence_scores=extraction_result["confidence_scores"],
                validation_results=extraction_result["validation_results"],
                processing_time=processing_time
            )
            
            logger.info(f"[{workflow_id}] 工作流完成，耗时: {processing_time:.2f}秒")
            return result
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"[{workflow_id}] 工作流失败: {e}")
            
            return WorkflowResult(
                workflow_id=workflow_id,
                status="failed",
                file_info=file_info if 'file_info' in locals() else {},
                extracted_data={},
                generated_report="",
                confidence_scores={},
                validation_results={},
                processing_time=processing_time,
                error_message=str(e)
            )
    
    async def _extract_information(
        self, 
        workflow_id: str, 
        document_text: str, 
        template_fields: List[Dict]
    ) -> Dict:
        """
        执行信息抽取
        
        Args:
            workflow_id: 工作流ID
            document_text: 文档文本
            template_fields: 模板字段
            
        Returns:
            Dict: 抽取结果
        """
        # 创建智能体状态
        state = BaseAgentState(
            workflow_id=workflow_id,
            current_stage="信息抽取"
        )
        
        state.document_info = {"content": document_text}
        state.template_info = {"fields": template_fields}
        
        # 执行抽取
        result_state = await self.extraction_agent.process(state)
        
        # 执行验证
        validated_state = await self.extraction_agent.validate_extraction_results(result_state)
        
        return {
            "extracted_data": validated_state.extracted_data,
            "confidence_scores": validated_state.confidence_scores,
            "validation_results": validated_state.validation_results,
            "extraction_stats": validated_state.generation_results.get("extraction_stats", {})
        }
    
    async def _generate_report(
        self, 
        workflow_id: str, 
        extraction_result: Dict, 
        report_template: Optional[str] = None
    ) -> str:
        """
        生成报告
        
        Args:
            workflow_id: 工作流ID
            extraction_result: 抽取结果
            report_template: 报告模板
            
        Returns:
            str: 生成的报告
        """
        extracted_data = extraction_result["extracted_data"]
        confidence_scores = extraction_result["confidence_scores"]
        
        # 如果没有提供模板，使用默认模板
        if not report_template:
            report_template = self._get_default_report_template()
        
        # 构建报告生成提示词
        prompt = f"""
请根据以下抽取的信息生成一份结构化报告：

**抽取的字段信息**：
{self._format_extracted_data(extracted_data, confidence_scores)}

**报告模板**：
{report_template}

**生成要求**：
1. 严格按照模板结构生成报告
2. 使用抽取的信息填充相应字段
3. 保持专业的语言风格
4. 确保信息的准确性和完整性
5. 如果某个字段信息不足，请标注"待补充"

请生成完整的报告内容：
"""
        
        messages = [
            {
                "role": "system",
                "content": "你是一个专业的报告生成助手，能够根据抽取的信息和模板生成高质量的结构化报告。"
            },
            {
                "role": "user",
                "content": prompt
            }
        ]
        
        # 调用LLM生成报告
        response = await llm_service.call_llm(messages)
        
        if response.success:
            return response.content
        else:
            logger.error(f"[{workflow_id}] 报告生成失败: {response.error}")
            return f"报告生成失败: {response.error}"
    
    def _format_extracted_data(self, extracted_data: Dict, confidence_scores: Dict) -> str:
        """格式化抽取数据用于报告生成"""
        formatted_lines = []
        
        for field_name, value in extracted_data.items():
            confidence = confidence_scores.get(field_name, 0.0)
            confidence_text = f"(置信度: {confidence:.2f})" if confidence > 0 else "(未验证)"
            formatted_lines.append(f"- {field_name}: {value} {confidence_text}")
        
        return "\n".join(formatted_lines)
    
    def _get_default_report_template(self) -> str:
        """获取默认报告模板"""
        return """
# 信息抽取报告

## 基本信息
- 文档类型: {document_type}
- 处理时间: {processing_time}
- 抽取字段数: {field_count}

## 抽取结果

{extracted_fields}

## 质量评估
- 整体置信度: {overall_confidence}
- 验证通过率: {validation_rate}

## 备注
本报告由AI自动生成，请人工核验关键信息的准确性。
"""

    def get_workflow_status(self, workflow_id: str) -> Optional[Dict]:
        """获取工作流状态"""
        return self.active_workflows.get(workflow_id)


# 全局工作流服务实例
workflow_service = WorkflowService()
