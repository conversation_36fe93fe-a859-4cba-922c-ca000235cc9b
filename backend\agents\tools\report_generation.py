"""
报告生成工具
负责基于模板和提取的数据生成最终的报告文档
"""

import os
import json
from typing import Dict, Any, List, Optional
from docx import Document
from docx.shared import Inches
from datetime import datetime
import re

from .base_tool import BaseTool, ToolResult
from services.llm_service import llm_service


class ReportGenerationTool(BaseTool):
    """报告生成工具"""
    
    def __init__(self):
        super().__init__("report_generation")
        self.prompt_template = self._load_prompt_template()
    
    def _load_prompt_template(self) -> str:
        """加载prompt模板"""
        try:
            prompt_path = os.path.join(
                os.path.dirname(__file__), 
                "..", "prompts", "report_generation.txt"
            )
            with open(prompt_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            self.log_error(f"加载prompt模板失败: {e}")
            return ""
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行报告生成
        
        Args:
            input_data: {
                "template_path": "模板文件路径",
                "extracted_data": "提取的数据",
                "output_path": "输出文件路径",
                "generation_options": "生成选项"
            }
        """
        try:
            # 验证输入
            required_fields = ["template_path", "extracted_data"]
            if not self.validate_input(input_data, required_fields):
                return self.create_result(False, error="输入数据验证失败")
            
            template_path = input_data["template_path"]
            extracted_data = input_data["extracted_data"]
            output_path = input_data.get("output_path", self._generate_output_path())
            
            # 验证模板文件
            if not os.path.exists(template_path):
                return self.create_result(False, error=f"模板文件不存在: {template_path}")
            
            # 准备数据映射
            data_mapping = self._prepare_data_mapping(extracted_data)
            
            # 生成报告内容
            generation_result = await self._generate_report_content(template_path, data_mapping, output_path)
            
            if not generation_result["success"]:
                return self.create_result(False, error=generation_result["error"])
            
            # 质量检查
            quality_metrics = await self._assess_report_quality(output_path, data_mapping)
            
            # 构建结果
            result_data = {
                "report_generation": {
                    "report_id": self._generate_report_id(),
                    "report_name": os.path.basename(output_path),
                    "output_path": output_path,
                    "generation_method": "template_filling",
                    "quality_score": quality_metrics["overall_score"],
                    "generation_time": generation_result["generation_time"],
                    "status": "completed"
                },
                "generation_details": {
                    "template_used": template_path,
                    "fields_filled": generation_result["fields_filled"],
                    "total_fields": generation_result["total_fields"],
                    "fill_rate": generation_result["fill_rate"]
                },
                "quality_metrics": quality_metrics,
                "next_action": "end" if quality_metrics["overall_score"] > 0.7 else "human_interaction"
            }
            
            self.log_info(f"报告生成完成: {output_path}")
            
            return self.create_result(True, result_data, message="报告生成成功")
            
        except Exception as e:
            self.log_error(f"报告生成执行失败: {e}")
            return self.create_result(False, error=str(e))
    
    def _generate_output_path(self) -> str:
        """生成输出文件路径"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"报告_{timestamp}.docx"
        
        # 确保输出目录存在
        output_dir = "outputs"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        return os.path.join(output_dir, filename)
    
    def _generate_report_id(self) -> str:
        """生成报告ID"""
        import uuid
        return str(uuid.uuid4())
    
    def _prepare_data_mapping(self, extracted_data: List[Dict[str, Any]]) -> Dict[str, str]:
        """准备数据映射"""
        data_mapping = {}
        
        for item in extracted_data:
            field_name = item.get("field_name", "")
            # Also check for "field_value" key which might be used in some parts of the system
            extracted_value = item.get("extracted_value", item.get("field_value", ""))
            
            # Skip empty field names
            if not field_name:
                continue
                
            # 处理不同的占位符格式
            placeholder_variants = [
                f"{{{{{field_name}}}}}",  # {{field_name}}
                f"{{{field_name}}}",      # {field_name}
                f"[{field_name}]",        # [field_name]
                f"<{field_name}>",        # <field_name>
            ]
            
            for placeholder in placeholder_variants:
                # Only add non-empty values or use a placeholder for empty values
                data_mapping[placeholder] = extracted_value if extracted_value and extracted_value != "未找到" else "[待补充]"
        
        return data_mapping
    
    async def _generate_report_content(self, template_path: str, data_mapping: Dict[str, str], 
                                     output_path: str) -> Dict[str, Any]:
        """生成报告内容"""
        try:
            start_time = datetime.now()
            
            # 加载模板文档
            doc = Document(template_path)
            
            fields_filled = 0
            total_fields = 0
            
            # 处理段落中的占位符
            for paragraph in doc.paragraphs:
                if paragraph.text:
                    original_text = paragraph.text
                    updated_text = original_text
                    
                    # 查找并替换占位符
                    for placeholder, value in data_mapping.items():
                        if placeholder in updated_text:
                            total_fields += 1
                            if value and value != "未找到":
                                updated_text = updated_text.replace(placeholder, value)
                                fields_filled += 1
                            else:
                                # 保留占位符或使用默认提示
                                updated_text = updated_text.replace(placeholder, "[待补充]")
                    
                    # 更新段落文本
                    if updated_text != original_text:
                        paragraph.text = updated_text
            
            # 处理表格中的占位符
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        if cell.text:
                            original_text = cell.text
                            updated_text = original_text
                            
                            for placeholder, value in data_mapping.items():
                                if placeholder in updated_text:
                                    total_fields += 1
                                    if value and value != "未找到":
                                        updated_text = updated_text.replace(placeholder, value)
                                        fields_filled += 1
                                    else:
                                        updated_text = updated_text.replace(placeholder, "[待补充]")
                            
                            if updated_text != original_text:
                                cell.text = updated_text
            
            # 保存生成的文档
            doc.save(output_path)
            
            end_time = datetime.now()
            generation_time = (end_time - start_time).total_seconds()
            
            fill_rate = fields_filled / total_fields if total_fields > 0 else 0
            
            return {
                "success": True,
                "fields_filled": fields_filled,
                "total_fields": total_fields,
                "fill_rate": round(fill_rate, 2),
                "generation_time": round(generation_time, 2)
            }
            
        except Exception as e:
            self.log_error(f"报告内容生成失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _assess_report_quality(self, output_path: str, data_mapping: Dict[str, str]) -> Dict[str, Any]:
        """评估报告质量"""
        try:
            # 读取生成的文档
            doc = Document(output_path)
            
            # 统计文档内容
            total_paragraphs = len([p for p in doc.paragraphs if p.text.strip()])
            total_tables = len(doc.tables)
            
            # 检查未填充的占位符
            unfilled_placeholders = 0
            total_content = ""
            
            for paragraph in doc.paragraphs:
                total_content += paragraph.text + "\n"
            
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        total_content += cell.text + " "
            
            # 统计待补充项
            unfilled_placeholders = total_content.count("[待补充]")
            
            # 计算各项评分
            accuracy_score = 0.9  # 基础准确性评分
            
            # 完整性评分
            total_mapped_fields = len(data_mapping)
            filled_fields = sum(1 for v in data_mapping.values() if v and v != "未找到")
            completeness_score = filled_fields / total_mapped_fields if total_mapped_fields > 0 else 0
            
            # 格式评分
            format_score = 0.95 if total_paragraphs > 0 else 0.5
            
            # 综合评分
            overall_score = (accuracy_score * 0.4 + completeness_score * 0.4 + format_score * 0.2)
            
            return {
                "accuracy_score": round(accuracy_score, 2),
                "completeness_score": round(completeness_score, 2),
                "format_score": round(format_score, 2),
                "overall_score": round(overall_score, 2),
                "document_stats": {
                    "total_paragraphs": total_paragraphs,
                    "total_tables": total_tables,
                    "unfilled_placeholders": unfilled_placeholders,
                    "filled_fields": filled_fields,
                    "total_fields": total_mapped_fields
                },
                "quality_issues": self._identify_quality_issues(unfilled_placeholders, completeness_score)
            }
            
        except Exception as e:
            self.log_error(f"质量评估失败: {e}")
            return {
                "accuracy_score": 0.0,
                "completeness_score": 0.0,
                "format_score": 0.0,
                "overall_score": 0.0,
                "document_stats": {},
                "quality_issues": [f"质量评估失败: {e}"]
            }
    
    def _identify_quality_issues(self, unfilled_placeholders: int, completeness_score: float) -> List[str]:
        """识别质量问题"""
        issues = []
        
        if unfilled_placeholders > 0:
            issues.append(f"存在 {unfilled_placeholders} 个未填充的占位符")
        
        if completeness_score < 0.8:
            issues.append("字段填充率较低，建议补充更多信息")
        
        if completeness_score < 0.5:
            issues.append("大量字段未填充，建议检查数据提取质量")
        
        return issues
    
    async def enhance_content_with_ai(self, content: str, field_name: str, context: str = "") -> str:
        """使用AI增强内容质量"""
        try:
            enhancement_prompt = f"""
请优化以下内容，使其更加专业和完整：

字段名称：{field_name}
原始内容：{content}
上下文：{context}

请返回优化后的内容，保持原意的同时提升表达质量。
"""
            
            llm_response = await llm_service.call_llm([
                {"role": "system", "content": "你是一个专业的文档编辑助手，擅长优化文档内容的表达质量。"},
                {"role": "user", "content": enhancement_prompt}
            ])
            
            if llm_response.success:
                enhanced_content = llm_response.content.strip()
                return enhanced_content if enhanced_content else content
            else:
                return content
                
        except Exception as e:
            self.log_error(f"内容增强失败: {e}")
            return content
    
    async def generate_missing_content(self, field_name: str, field_description: str, 
                                     context_data: Dict[str, str]) -> str:
        """为缺失字段生成内容"""
        try:
            context_info = "\n".join([f"{k}: {v}" for k, v in context_data.items() if v and v != "未找到"])
            
            generation_prompt = f"""
基于以下上下文信息，为字段"{field_name}"生成合适的内容：

字段描述：{field_description}
相关信息：
{context_info}

请生成符合字段要求的专业内容。
"""
            
            llm_response = await llm_service.call_llm([
                {"role": "system", "content": "你是一个专业的内容生成助手，能够基于上下文生成高质量的文档内容。"},
                {"role": "user", "content": generation_prompt}
            ])
            
            if llm_response.success:
                generated_content = llm_response.content.strip()
                return generated_content if generated_content else "[待补充]"
            else:
                return "[待补充]"
                
        except Exception as e:
            self.log_error(f"内容生成失败: {e}")
            return "[待补充]"
    
    def validate_output_file(self, output_path: str) -> Dict[str, Any]:
        """验证输出文件"""
        try:
            if not os.path.exists(output_path):
                return {"valid": False, "error": "输出文件不存在"}
            
            # 检查文件大小
            file_size = os.path.getsize(output_path)
            if file_size == 0:
                return {"valid": False, "error": "输出文件为空"}
            
            # 尝试打开文档验证格式
            try:
                doc = Document(output_path)
                paragraph_count = len([p for p in doc.paragraphs if p.text.strip()])
                
                if paragraph_count == 0:
                    return {"valid": False, "error": "文档内容为空"}
                
                return {
                    "valid": True,
                    "file_size": file_size,
                    "paragraph_count": paragraph_count
                }
                
            except Exception as e:
                return {"valid": False, "error": f"文档格式错误: {e}"}
                
        except Exception as e:
            return {"valid": False, "error": f"文件验证失败: {e}"}
    
    def create_report_summary(self, generation_result: Dict[str, Any]) -> Dict[str, Any]:
        """创建报告摘要"""
        report_info = generation_result.get("report_generation", {})
        quality_metrics = generation_result.get("quality_metrics", {})
        generation_details = generation_result.get("generation_details", {})
        
        return {
            "report_id": report_info.get("report_id"),
            "report_name": report_info.get("report_name"),
            "generation_time": report_info.get("generation_time"),
            "quality_score": quality_metrics.get("overall_score", 0),
            "completeness": f"{generation_details.get('fill_rate', 0):.1%}",
            "status": report_info.get("status", "unknown"),
            "file_path": report_info.get("output_path"),
            "issues": quality_metrics.get("quality_issues", [])
        }
