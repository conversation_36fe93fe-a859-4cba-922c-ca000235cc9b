"""
模板管理API路由
"""

from typing import List
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlalchemy.orm import Session
from database import get_db
from models.template import Template, TemplateField
from schemas.template import (
    TemplateCreate, TemplateUpdate, TemplateResponse, 
    TemplateListResponse, TemplateFieldResponse
)
from schemas.common import MessageResponse, PaginatedResponse

router = APIRouter(prefix="/api/templates", tags=["模板管理"])


@router.get("/", response_model=PaginatedResponse[TemplateListResponse])
async def list_templates(
    page: int = 1,
    size: int = 20,
    db: Session = Depends(get_db)
):
    """获取模板列表"""
    offset = (page - 1) * size
    
    templates = db.query(Template).filter(Template.is_active == True).offset(offset).limit(size).all()
    total = db.query(Template).filter(Template.is_active == True).count()
    
    items = [TemplateListResponse.model_validate(template) for template in templates]
    pages = (total + size - 1) // size
    
    return PaginatedResponse(
        items=items,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/{template_id}", response_model=TemplateResponse)
async def get_template(template_id: UUID, db: Session = Depends(get_db)):
    """获取模板详情"""
    template = db.query(Template).filter(
        Template.id == template_id,
        Template.is_active == True
    ).first()
    
    if not template:
        raise HTTPException(status_code=404, detail="模板不存在")
    
    return TemplateResponse.model_validate(template)


@router.post("/upload", response_model=TemplateResponse)
async def upload_template(
    name: str = Form(...),
    description: str = Form(None),
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """上传模板文件"""
    # 验证文件类型
    if not file.filename.endswith(('.doc', '.docx')):
        raise HTTPException(status_code=400, detail="只支持Word文档格式")
    
    # TODO: 实现文件保存和占位符解析逻辑
    file_path = f"uploads/templates/{file.filename}"
    
    # 创建模板记录
    template = Template(
        name=name,
        description=description,
        file_path=file_path,
        file_size=file.size,
        mime_type=file.content_type,
        created_by="system"  # TODO: 从认证信息获取
    )
    
    db.add(template)
    db.commit()
    db.refresh(template)
    
    return TemplateResponse.model_validate(template)


@router.put("/{template_id}", response_model=TemplateResponse)
async def update_template(
    template_id: UUID,
    template_update: TemplateUpdate,
    db: Session = Depends(get_db)
):
    """更新模板信息"""
    template = db.query(Template).filter(
        Template.id == template_id,
        Template.is_active == True
    ).first()
    
    if not template:
        raise HTTPException(status_code=404, detail="模板不存在")
    
    # 更新字段
    for field, value in template_update.model_dump(exclude_unset=True).items():
        setattr(template, field, value)
    
    db.commit()
    db.refresh(template)
    
    return TemplateResponse.model_validate(template)


@router.delete("/{template_id}", response_model=MessageResponse)
async def delete_template(template_id: UUID, db: Session = Depends(get_db)):
    """删除模板(软删除)"""
    template = db.query(Template).filter(
        Template.id == template_id,
        Template.is_active == True
    ).first()
    
    if not template:
        raise HTTPException(status_code=404, detail="模板不存在")
    
    template.is_active = False
    db.commit()
    
    return MessageResponse(message="模板删除成功")


@router.get("/{template_id}/fields", response_model=List[TemplateFieldResponse])
async def get_template_fields(template_id: UUID, db: Session = Depends(get_db)):
    """获取模板字段列表"""
    template = db.query(Template).filter(
        Template.id == template_id,
        Template.is_active == True
    ).first()
    
    if not template:
        raise HTTPException(status_code=404, detail="模板不存在")
    
    fields = db.query(TemplateField).filter(
        TemplateField.template_id == template_id,
        TemplateField.is_active == True
    ).order_by(TemplateField.field_order).all()
    
    return [TemplateFieldResponse.model_validate(field) for field in fields]


@router.post("/batch-delete", response_model=MessageResponse)
async def batch_delete_templates(
    template_ids: List[UUID],
    db: Session = Depends(get_db)
):
    """批量删除模板"""
    if not template_ids:
        raise HTTPException(status_code=400, detail="请提供要删除的模板ID列表")

    # 查找存在的模板
    templates = db.query(Template).filter(
        Template.id.in_(template_ids),
        Template.is_active == True
    ).all()

    if not templates:
        raise HTTPException(status_code=404, detail="未找到指定的模板")

    # 批量软删除
    deleted_count = 0
    for template in templates:
        template.is_active = False
        deleted_count += 1

    db.commit()

    return MessageResponse(message=f"成功删除 {deleted_count} 个模板")


@router.post("/export")
async def export_templates(
    template_ids: List[UUID] = None,
    export_format: str = "json",
    include_fields: bool = True,
    db: Session = Depends(get_db)
):
    """导出模板"""
    from fastapi.responses import JSONResponse
    from datetime import datetime

    # 构建查询
    query = db.query(Template).filter(Template.is_active == True)

    if template_ids:
        query = query.filter(Template.id.in_(template_ids))

    templates = query.all()

    if not templates:
        raise HTTPException(status_code=404, detail="未找到要导出的模板")

    # 准备导出数据
    export_data = []
    for template in templates:
        template_data = {
            "id": str(template.id),
            "name": template.name,
            "description": template.description,
            "category": template.category,
            "file_path": template.file_path,
            "version": template.version,
            "placeholder_count": template.placeholder_count,
            "key_definitions_path": template.key_definitions_path,
            "created_at": template.created_at.isoformat() if template.created_at else None,
            "updated_at": template.updated_at.isoformat() if template.updated_at else None
        }

        # 包含字段定义
        if include_fields:
            fields = db.query(TemplateField).filter(
                TemplateField.template_id == template.id,
                TemplateField.is_active == True
            ).order_by(TemplateField.field_order).all()

            template_data["fields"] = [
                {
                    "field_name": field.field_name,
                    "field_path": field.field_path,
                    "parent_field": field.parent_field,
                    "field_level": field.field_level,
                    "description": field.description,
                    "example": field.example,
                    "format": field.format,
                    "default_value": field.default_value,
                    "length": field.length,
                    "source": field.source,
                    "must": field.must,
                    "field_order": field.field_order
                }
                for field in fields
            ]

        export_data.append(template_data)

    if export_format.lower() == "json":
        return JSONResponse(
            content={
                "export_time": datetime.now().isoformat(),
                "total_count": len(export_data),
                "include_fields": include_fields,
                "templates": export_data
            },
            headers={"Content-Disposition": "attachment; filename=templates_export.json"}
        )
    else:
        raise HTTPException(status_code=400, detail="不支持的导出格式，目前仅支持 json")


@router.post("/import")
async def import_templates(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """导入模板"""
    if not file.filename.endswith('.json'):
        raise HTTPException(status_code=400, detail="仅支持JSON格式的导入文件")

    try:
        import json
        content = await file.read()
        import_data = json.loads(content.decode('utf-8'))

        if "templates" not in import_data:
            raise HTTPException(status_code=400, detail="导入文件格式错误，缺少 templates 字段")

        imported_count = 0
        skipped_count = 0

        for template_data in import_data["templates"]:
            # 检查是否已存在同名模板
            existing = db.query(Template).filter(
                Template.name == template_data["name"],
                Template.is_active == True
            ).first()

            if existing:
                skipped_count += 1
                continue

            # 创建新模板
            new_template = Template(
                name=template_data["name"],
                description=template_data.get("description"),
                category=template_data.get("category"),
                file_path=template_data.get("file_path"),
                version=template_data.get("version", "1.0"),
                placeholder_count=template_data.get("placeholder_count", 0),
                key_definitions_path=template_data.get("key_definitions_path"),
                created_by="import_system"
            )

            db.add(new_template)
            db.flush()  # 获取新模板的ID

            # 导入字段定义
            if "fields" in template_data:
                for field_data in template_data["fields"]:
                    new_field = TemplateField(
                        template_id=new_template.id,
                        field_name=field_data["field_name"],
                        field_path=field_data.get("field_path"),
                        parent_field=field_data.get("parent_field"),
                        field_level=field_data.get("field_level", 1),
                        description=field_data.get("description"),
                        example=field_data.get("example"),
                        format=field_data.get("format"),
                        default_value=field_data.get("default_value"),
                        length=field_data.get("length"),
                        source=field_data.get("source"),
                        must=field_data.get("must", False),
                        field_order=field_data.get("field_order", 0)
                    )
                    db.add(new_field)

            imported_count += 1

        db.commit()

        return {
            "message": "模板导入完成",
            "imported_count": imported_count,
            "skipped_count": skipped_count,
            "total_processed": imported_count + skipped_count
        }

    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="JSON文件格式错误")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导入失败: {str(e)}")
