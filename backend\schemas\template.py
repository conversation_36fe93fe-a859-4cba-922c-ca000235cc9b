"""
模板相关Pydantic schemas
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID
from pydantic import BaseModel, Field
from .common import BaseResponse


class TemplateFieldBase(BaseModel):
    """模板字段基础模型"""
    
    field_name: str = Field(..., description="字段名称")
    field_type: str = Field("text", description="字段类型")
    is_required: bool = Field(False, description="是否必填")
    is_repeatable: bool = Field(False, description="是否可重复")
    default_value: Optional[str] = Field(None, description="默认值")
    description: Optional[str] = Field(None, description="字段描述")
    field_order: int = Field(0, description="字段顺序")


class TemplateFieldResponse(TemplateFieldBase, BaseResponse):
    """模板字段响应模型"""
    
    template_id: UUID


class TemplateBase(BaseModel):
    """模板基础模型"""
    
    name: str = Field(..., description="模板名称")
    description: Optional[str] = Field(None, description="模板描述")


class TemplateCreate(TemplateBase):
    """创建模板请求模型"""
    
    pass


class TemplateUpdate(BaseModel):
    """更新模板请求模型"""
    
    name: Optional[str] = Field(None, description="模板名称")
    description: Optional[str] = Field(None, description="模板描述")


class TemplateResponse(TemplateBase, BaseResponse):
    """模板响应模型"""
    
    file_path: str
    file_size: Optional[int] = None
    mime_type: Optional[str] = None
    placeholder_count: int = 0
    fields: List[TemplateFieldResponse] = []


class TemplateListResponse(BaseModel):
    """模板列表响应模型"""

    id: UUID
    name: str
    description: Optional[str] = None
    placeholder_count: int = 0
    created_at: datetime
    is_active: bool = True

    class Config:
        from_attributes = True
