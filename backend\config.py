import os
from typing import List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类 - 使用Pydantic Settings进行配置管理"""

    # 应用基础配置
    app_name: str = Field(default="报告智能生成工具", description="应用名称")
    app_version: str = Field(default="1.0.0", description="应用版本")
    environment: str = Field(default="development", description="运行环境")
    debug: bool = Field(default=True, description="调试模式")
    secret_key: str = Field(default="dev-secret-key-change-in-production", description="应用密钥")

    # 数据库配置
    database_url: str = Field(
        default="postgresql://report_user:report_pass@localhost:5432/report_gen",
        description="数据库连接URL"
    )
    db_host: str = Field(default="localhost", description="数据库主机")
    db_port: int = Field(default=5432, description="数据库端口")
    db_name: str = Field(default="report_gen", description="数据库名称")
    db_user: str = Field(default="report_user", description="数据库用户")
    db_password: str = Field(default="report_pass", description="数据库密码")
    db_echo: bool = Field(default=False, description="是否打印SQL语句")

    # Redis 配置
    redis_url: str = Field(default="redis://localhost:6379/0", description="Redis连接URL")
    redis_host: str = Field(default="localhost", description="Redis主机")
    redis_port: int = Field(default=6379, description="Redis端口")
    redis_db: int = Field(default=0, description="Redis数据库")
    redis_password: Optional[str] = Field(default=None, description="Redis密码")

    # Celery 配置
    celery_broker_url: str = Field(default="redis://localhost:6379/0", description="Celery消息代理")
    celery_result_backend: str = Field(default="redis://localhost:6379/0", description="Celery结果后端")

    # LLM 配置
    llm_provider: str = Field(default="dashscope", description="LLM提供商")
    dashscope_api_key: str = Field(default="", description="DashScope API密钥")
    llm_model: str = Field(default="qwq-32b-preview", description="LLM模型名称")
    llm_temperature: float = Field(default=0.1, description="LLM温度参数")
    llm_max_tokens: int = Field(default=4000, description="LLM最大令牌数")
    llm_timeout: int = Field(default=60, description="LLM请求超时时间")
    llm_retry_times: int = Field(default=3, description="LLM重试次数")

    # 文件存储配置
    upload_dir: str = Field(default="./uploads", description="文件上传目录")
    max_file_size: str = Field(default="50MB", description="最大文件大小")
    allowed_extensions: str = Field(default="docx,txt", description="允许的文件扩展名")

    # 文档处理配置
    doc_max_size: int = Field(default=50, description="文档最大大小(MB)")
    doc_allowed_types: str = Field(default="docx,txt", description="允许的文档类型")

    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_file: str = Field(default="./logs/app.log", description="日志文件路径")

    # CORS 配置
    cors_origins: str = Field(
        default="http://localhost:3000,http://localhost:5173,http://127.0.0.1:3000,http://127.0.0.1:5173",
        description="CORS允许的源"
    )

    # JWT 配置
    jwt_secret_key: str = Field(default="dev-jwt-secret-change-in-production", description="JWT密钥")
    jwt_algorithm: str = Field(default="HS256", description="JWT算法")
    jwt_expire_minutes: int = Field(default=1440, description="JWT过期时间(分钟)")

    # 服务器配置
    host: str = Field(default="0.0.0.0", description="服务器主机")
    port: int = Field(default=8000, description="服务器端口")
    workers: int = Field(default=1, description="工作进程数")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

    @property
    def cors_origins_list(self) -> List[str]:
        """获取CORS允许的源列表"""
        return [origin.strip() for origin in self.cors_origins.split(",")]

    @property
    def allowed_extensions_list(self) -> List[str]:
        """获取允许的文件扩展名列表"""
        return [ext.strip().lower() for ext in self.allowed_extensions.split(",")]

    @property
    def doc_allowed_types_list(self) -> List[str]:
        """获取允许的文档类型列表"""
        return [doc_type.strip().lower() for doc_type in self.doc_allowed_types.split(",")]

    def get_database_url(self) -> str:
        """获取数据库连接URL"""
        if self.database_url:
            return self.database_url
        return f"postgresql://{self.db_user}:{self.db_password}@{self.db_host}:{self.db_port}/{self.db_name}"

    def get_redis_url(self) -> str:
        """获取Redis连接URL"""
        if self.redis_url:
            return self.redis_url
        auth_part = f":{self.redis_password}@" if self.redis_password else ""
        return f"redis://{auth_part}{self.redis_host}:{self.redis_port}/{self.redis_db}"

    def get_max_file_size_bytes(self) -> int:
        """获取最大文件大小(字节)"""
        size_str = self.max_file_size.upper()
        if size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)


# 创建全局配置实例
settings = Settings()


def create_directories():
    """创建必要的目录"""
    directories = [
        settings.upload_dir,
        os.path.dirname(settings.log_file) if settings.log_file else None,
    ]

    for directory in directories:
        if directory and not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)


# 初始化时创建目录
create_directories()