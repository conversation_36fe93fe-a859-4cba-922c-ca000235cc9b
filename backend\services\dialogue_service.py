"""
对话服务模块
提供对话持久化、检索和状态管理功能
"""

import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple, Union
import json

from sqlalchemy.orm import Session
from sqlalchemy import desc, asc

from database import get_db
from models.dialogue import (
    Conversation, Message, ConversationCheckpoint, 
    HumanFeedback, MessageRole, ConversationStatus,
    InteractionType
)
from services.llm_service import llm_service, LLMResponse

# 配置日志
logger = logging.getLogger(__name__)


class DialogueService:
    """对话服务类"""
    
    def __init__(self):
        """初始化对话服务"""
        self.db = next(get_db())
    
    def create_conversation(
        self, 
        user_id: Optional[str] = None,
        title: Optional[str] = None,
        interaction_type: str = InteractionType.CHAT.value,
        metadata: Optional[Dict[str, Any]] = None,
        context_data: Optional[Dict[str, Any]] = None
    ) -> Conversation:
        """
        创建新的对话会话
        
        Args:
            user_id: 用户ID
            title: 对话标题
            interaction_type: 交互类型
            metadata: 元数据
            context_data: 上下文数据
            
        Returns:
            Conversation: 新创建的对话会话
        """
        thread_id = str(uuid.uuid4())
        
        conversation = Conversation(
            thread_id=thread_id,
            user_id=user_id,
            title=title or f"对话 {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            status=ConversationStatus.ACTIVE.value,
            interaction_type=interaction_type,
            metadata=metadata or {},
            context_data=context_data or {},
        )
        
        self.db.add(conversation)
        self.db.commit()
        self.db.refresh(conversation)
        
        logger.info(f"创建新对话: {conversation.id} (thread_id: {thread_id})")
        return conversation
    
    def get_conversation(self, conversation_id: str) -> Optional[Conversation]:
        """
        获取对话会话
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            Optional[Conversation]: 对话会话或None
        """
        try:
            return self.db.query(Conversation).filter(Conversation.id == conversation_id).first()
        except Exception as e:
            logger.error(f"获取对话失败: {e}")
            return None
    
    def get_conversation_by_thread_id(self, thread_id: str) -> Optional[Conversation]:
        """
        通过thread_id获取对话会话
        
        Args:
            thread_id: LangGraph线程ID
            
        Returns:
            Optional[Conversation]: 对话会话或None
        """
        try:
            return self.db.query(Conversation).filter(Conversation.thread_id == thread_id).first()
        except Exception as e:
            logger.error(f"通过thread_id获取对话失败: {e}")
            return None
    
    def list_conversations(
        self, 
        user_id: Optional[str] = None,
        status: Optional[str] = None,
        interaction_type: Optional[str] = None,
        limit: int = 20,
        offset: int = 0
    ) -> List[Conversation]:
        """
        列出对话会话
        
        Args:
            user_id: 用户ID过滤
            status: 状态过滤
            interaction_type: 交互类型过滤
            limit: 返回数量限制
            offset: 分页偏移量
            
        Returns:
            List[Conversation]: 对话会话列表
        """
        query = self.db.query(Conversation)
        
        if user_id:
            query = query.filter(Conversation.user_id == user_id)
        
        if status:
            query = query.filter(Conversation.status == status)
            
        if interaction_type:
            query = query.filter(Conversation.interaction_type == interaction_type)
        
        return query.order_by(desc(Conversation.last_activity)).offset(offset).limit(limit).all()
    
    def update_conversation(
        self,
        conversation_id: str,
        title: Optional[str] = None,
        status: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        context_data: Optional[Dict[str, Any]] = None
    ) -> Optional[Conversation]:
        """
        更新对话会话
        
        Args:
            conversation_id: 对话ID
            title: 新标题
            status: 新状态
            metadata: 新元数据
            context_data: 新上下文数据
            
        Returns:
            Optional[Conversation]: 更新后的对话会话或None
        """
        conversation = self.get_conversation(conversation_id)
        if not conversation:
            return None
        
        if title:
            conversation.title = title
            
        if status:
            conversation.status = status
            
        if metadata:
            # 合并元数据而不是替换
            current_metadata = conversation.metadata or {}
            current_metadata.update(metadata)
            conversation.metadata = current_metadata
            
        if context_data:
            # 合并上下文数据而不是替换
            current_context = conversation.context_data or {}
            current_context.update(context_data)
            conversation.context_data = current_context
        
        conversation.updated_at = datetime.utcnow()
        
        self.db.commit()
        self.db.refresh(conversation)
        
        return conversation
    
    def add_message(
        self,
        conversation_id: str,
        role: str,
        content: str,
        content_type: str = "text",
        message_id: Optional[str] = None,
        parent_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        tool_calls: Optional[List[Dict[str, Any]]] = None,
        attachments: Optional[List[Dict[str, Any]]] = None
    ) -> Optional[Message]:
        """
        添加消息到对话
        
        Args:
            conversation_id: 对话ID
            role: 消息角色
            content: 消息内容
            content_type: 内容类型
            message_id: LangGraph消息ID
            parent_id: 父消息ID
            metadata: 元数据
            tool_calls: 工具调用信息
            attachments: 附件信息
            
        Returns:
            Optional[Message]: 新添加的消息或None
        """
        conversation = self.get_conversation(conversation_id)
        if not conversation:
            logger.error(f"添加消息失败: 对话 {conversation_id} 不存在")
            return None
        
        # 获取当前最大序号
        last_message = self.db.query(Message).filter(
            Message.conversation_id == conversation_id
        ).order_by(desc(Message.sequence_number)).first()
        
        sequence_number = 1
        if last_message:
            sequence_number = last_message.sequence_number + 1
        
        message = Message(
            conversation_id=conversation_id,
            role=role,
            content=content,
            content_type=content_type,
            message_id=message_id or str(uuid.uuid4()),
            parent_id=parent_id,
            sequence_number=sequence_number,
            metadata=metadata or {},
            tool_calls=tool_calls or [],
            attachments=attachments or []
        )
        
        self.db.add(message)
        
        # 更新对话统计信息
        conversation.message_count += 1
        conversation.last_activity = datetime.utcnow()
        
        self.db.commit()
        self.db.refresh(message)
        
        return message
    
    def get_messages(
        self,
        conversation_id: str,
        limit: Optional[int] = None,
        before_sequence: Optional[int] = None,
        after_sequence: Optional[int] = None
    ) -> List[Message]:
        """
        获取对话消息
        
        Args:
            conversation_id: 对话ID
            limit: 返回数量限制
            before_sequence: 获取序号小于此值的消息
            after_sequence: 获取序号大于此值的消息
            
        Returns:
            List[Message]: 消息列表
        """
        query = self.db.query(Message).filter(
            Message.conversation_id == conversation_id,
            Message.is_deleted == False
        )
        
        if before_sequence is not None:
            query = query.filter(Message.sequence_number < before_sequence)
            
        if after_sequence is not None:
            query = query.filter(Message.sequence_number > after_sequence)
        
        query = query.order_by(asc(Message.sequence_number))
        
        if limit:
            query = query.limit(limit)
            
        return query.all()
    
    def save_checkpoint(
        self,
        conversation_id: str,
        checkpoint_id: str,
        checkpoint_data: Dict[str, Any],
        parent_checkpoint_id: Optional[str] = None,
        step_number: int = 0,
        node_name: Optional[str] = None,
        status: str = "completed"
    ) -> Optional[ConversationCheckpoint]:
        """
        保存对话检查点
        
        Args:
            conversation_id: 对话ID
            checkpoint_id: 检查点ID
            checkpoint_data: 检查点数据
            parent_checkpoint_id: 父检查点ID
            step_number: 步骤编号
            node_name: 节点名称
            status: 状态
            
        Returns:
            Optional[ConversationCheckpoint]: 保存的检查点或None
        """
        conversation = self.get_conversation(conversation_id)
        if not conversation:
            logger.error(f"保存检查点失败: 对话 {conversation_id} 不存在")
            return None
        
        checkpoint = ConversationCheckpoint(
            conversation_id=conversation_id,
            checkpoint_id=checkpoint_id,
            parent_checkpoint_id=parent_checkpoint_id,
            checkpoint_data=checkpoint_data,
            step_number=step_number,
            node_name=node_name,
            status=status
        )
        
        self.db.add(checkpoint)
        self.db.commit()
        self.db.refresh(checkpoint)
        
        return checkpoint
    
    def get_latest_checkpoint(self, conversation_id: str) -> Optional[ConversationCheckpoint]:
        """
        获取最新检查点
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            Optional[ConversationCheckpoint]: 最新检查点或None
        """
        return self.db.query(ConversationCheckpoint).filter(
            ConversationCheckpoint.conversation_id == conversation_id
        ).order_by(desc(ConversationCheckpoint.step_number)).first()
    
    def add_human_feedback(
        self,
        conversation_id: str,
        feedback_type: str,
        feedback_content: str,
        message_id: Optional[str] = None,
        original_content: Optional[str] = None,
        feedback_metadata: Optional[Dict[str, Any]] = None,
        user_id: Optional[str] = None
    ) -> Optional[HumanFeedback]:
        """
        添加人工反馈
        
        Args:
            conversation_id: 对话ID
            feedback_type: 反馈类型
            feedback_content: 反馈内容
            message_id: 消息ID
            original_content: 原始内容
            feedback_metadata: 反馈元数据
            user_id: 用户ID
            
        Returns:
            Optional[HumanFeedback]: 添加的反馈或None
        """
        conversation = self.get_conversation(conversation_id)
        if not conversation:
            logger.error(f"添加反馈失败: 对话 {conversation_id} 不存在")
            return None
        
        feedback = HumanFeedback(
            conversation_id=conversation_id,
            message_id=message_id,
            feedback_type=feedback_type,
            feedback_content=feedback_content,
            original_content=original_content,
            feedback_metadata=feedback_metadata or {},
            user_id=user_id
        )
        
        self.db.add(feedback)
        self.db.commit()
        self.db.refresh(feedback)
        
        return feedback
    
    async def process_user_message(
        self, 
        conversation_id: str, 
        user_message: str,
        system_prompt: Optional[str] = None
    ) -> Tuple[Message, Message]:
        """
        处理用户消息并生成助手回复
        
        Args:
            conversation_id: 对话ID
            user_message: 用户消息内容
            system_prompt: 系统提示词
            
        Returns:
            Tuple[Message, Message]: 用户消息和助手回复
        """
        # 添加用户消息
        user_msg = self.add_message(
            conversation_id=conversation_id,
            role=MessageRole.HUMAN.value,
            content=user_message
        )
        
        if not user_msg:
            raise ValueError(f"添加用户消息失败: {conversation_id}")
        
        # 获取对话历史
        messages = self.get_messages(conversation_id)
        
        # 构建LLM请求
        llm_messages = []
        
        # 添加系统提示词
        if system_prompt:
            llm_messages.append({
                "role": "system",
                "content": system_prompt
            })
        
        # 添加对话历史
        for msg in messages:
            role = "user" if msg.role == MessageRole.HUMAN.value else "assistant"
            if msg.role == MessageRole.SYSTEM.value:
                role = "system"
                
            llm_messages.append({
                "role": role,
                "content": msg.content
            })
        
        # 调用LLM
        try:
            response: LLMResponse = await llm_service.call_llm(llm_messages)
            
            if not response.success:
                error_message = f"LLM调用失败: {response.error}"
                logger.error(error_message)
                
                # 添加错误消息
                assistant_msg = self.add_message(
                    conversation_id=conversation_id,
                    role=MessageRole.ASSISTANT.value,
                    content="抱歉，我现在无法回答您的问题。请稍后再试。",
                    metadata={"error": response.error}
                )
                
                return user_msg, assistant_msg
            
            # 添加助手回复
            assistant_msg = self.add_message(
                conversation_id=conversation_id,
                role=MessageRole.ASSISTANT.value,
                content=response.content
            )
            
            return user_msg, assistant_msg
            
        except Exception as e:
            logger.error(f"处理用户消息异常: {e}")
            
            # 添加错误消息
            assistant_msg = self.add_message(
                conversation_id=conversation_id,
                role=MessageRole.ASSISTANT.value,
                content="抱歉，处理您的消息时出现了错误。请稍后再试。",
                metadata={"error": str(e)}
            )
            
            return user_msg, assistant_msg

    def delete_conversation(self, conversation_id: str) -> bool:
        """
        删除对话及其所有相关数据

        Args:
            conversation_id: 对话ID

        Returns:
            bool: 删除是否成功
        """
        try:
            conversation = self.get_conversation(conversation_id)
            if not conversation:
                return False

            # 删除对话（级联删除会自动删除相关的消息、检查点等）
            self.db.delete(conversation)
            self.db.commit()

            logger.info(f"删除对话: {conversation_id}")
            return True

        except Exception as e:
            logger.error(f"删除对话失败: {e}")
            self.db.rollback()
            return False

    def clear_conversation_messages(self, conversation_id: str) -> bool:
        """
        清空对话的所有消息

        Args:
            conversation_id: 对话ID

        Returns:
            bool: 清空是否成功
        """
        try:
            # 删除所有消息
            deleted_count = self.db.query(Message).filter(
                Message.conversation_id == conversation_id
            ).delete()

            # 重置消息计数
            conversation = self.get_conversation(conversation_id)
            if conversation:
                conversation.message_count = 0

            self.db.commit()

            logger.info(f"清空对话消息: {conversation_id}, 删除 {deleted_count} 条消息")
            return True

        except Exception as e:
            logger.error(f"清空对话消息失败: {e}")
            self.db.rollback()
            return False

    def clear_conversation_checkpoints(self, conversation_id: str) -> bool:
        """
        清空对话的所有检查点

        Args:
            conversation_id: 对话ID

        Returns:
            bool: 清空是否成功
        """
        try:
            # 删除所有检查点
            deleted_count = self.db.query(ConversationCheckpoint).filter(
                ConversationCheckpoint.conversation_id == conversation_id
            ).delete()

            self.db.commit()

            logger.info(f"清空对话检查点: {conversation_id}, 删除 {deleted_count} 条检查点")
            return True

        except Exception as e:
            logger.error(f"清空对话检查点失败: {e}")
            self.db.rollback()
            return False

    def get_message(self, message_id: str) -> Optional[Message]:
        """
        获取特定消息

        Args:
            message_id: 消息ID

        Returns:
            Optional[Message]: 消息对象或None
        """
        try:
            return self.db.query(Message).filter(Message.id == message_id).first()
        except Exception as e:
            logger.error(f"获取消息失败: {e}")
            return None

    def update_message(
        self,
        message_id: str,
        content: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        更新消息内容

        Args:
            message_id: 消息ID
            content: 新的消息内容
            metadata: 新的元数据

        Returns:
            bool: 更新是否成功
        """
        try:
            message = self.get_message(message_id)
            if not message:
                return False

            if content is not None:
                message.content = content
                message.is_edited = True

            if metadata is not None:
                message.metadata = metadata

            message.updated_at = datetime.utcnow()

            self.db.commit()

            logger.info(f"更新消息: {message_id}")
            return True

        except Exception as e:
            logger.error(f"更新消息失败: {e}")
            self.db.rollback()
            return False

    def delete_message(self, message_id: str) -> bool:
        """
        删除消息（软删除）

        Args:
            message_id: 消息ID

        Returns:
            bool: 删除是否成功
        """
        try:
            message = self.get_message(message_id)
            if not message:
                return False

            # 软删除
            message.is_deleted = True
            message.updated_at = datetime.utcnow()

            self.db.commit()

            logger.info(f"删除消息: {message_id}")
            return True

        except Exception as e:
            logger.error(f"删除消息失败: {e}")
            self.db.rollback()
            return False


# 全局对话服务实例
dialogue_service = DialogueService()
