#!/usr/bin/env python3
"""
调试数据提取节点
"""

import requests
import tempfile
import os
import json

def debug_data_extraction():
    # 创建测试文件
    content = """
会议纪要

时间：2024年7月25日 14:00-15:30
地点：会议室A
主持人：张三
参会人员：李四、王五、赵六

会议议题：
1. 项目进度汇报
2. 下阶段工作安排
3. 资源配置讨论

会议内容：
1. 项目进度汇报
   - 当前完成度：80%
   - 主要成果：完成了核心功能开发
   - 遇到的问题：测试环境配置有待优化

2. 下阶段工作安排
   - 完成剩余功能开发
   - 进行全面测试
   - 准备上线部署

3. 资源配置讨论
   - 需要增加2名测试人员
   - 服务器资源需要扩容

决议事项：
1. 下周完成功能开发
2. 申请增加测试人员
3. 联系运维部门扩容服务器

下次会议时间：2024年8月1日 14:00
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(content)
        test_file_path = f.name

    try:
        # 启动工作流
        with open(test_file_path, 'rb') as f:
            files = {'files': ('meeting_notes.txt', f, 'text/plain')}
            data = {'text_input': '请根据上传的文档生成会议纪要'}
            response = requests.post('http://localhost:8000/api/workflow/langgraph/start', files=files, data=data, timeout=30)
        
        print(f'启动状态码: {response.status_code}')
        if response.status_code == 200:
            result = response.json()
            workflow_id = result.get('workflow_id')
            print(f'工作流ID: {workflow_id}')
            
            # 显示启动结果的详细信息
            final_state = result.get('data', {}).get('final_state', {})
            print(f'当前阶段: {final_state.get("current_stage", "unknown")}')
            print(f'下一步动作: {final_state.get("next_action", "unknown")}')
            print(f'需要用户操作: {result.get("data", {}).get("requires_user_action", False)}')
            
            # 检查是否有人机交互数据
            human_interaction = final_state.get('human_interaction')
            if human_interaction:
                print(f'人机交互类型: {human_interaction.get("type")}')
                print(f'人机交互消息: {human_interaction.get("message", "")}')
                
                # 检查提取结果
                interaction_data = human_interaction.get('data', {})
                extraction_results = interaction_data.get('extraction_results', [])
                print(f'提取结果数量: {len(extraction_results)}')
                
                if extraction_results:
                    print('前5个提取结果:')
                    for i, result in enumerate(extraction_results[:5]):
                        field_name = result.get('field_name', '')
                        field_value = result.get('field_value', '')
                        confidence = result.get('confidence', 0)
                        needs_input = result.get('needs_human_input', False)
                        method = result.get('extraction_method', 'unknown')
                        
                        print(f'  {i+1}. {field_name}: {field_value} (置信度: {confidence:.2f}, 需要输入: {needs_input}, 方法: {method})')
            
            # 如果需要用户操作，进行模板确认
            if result.get('data', {}).get('requires_user_action', False):
                print('\n进行模板确认...')
                resume_data = {'action': 'confirm'}
                resume_response = requests.post(f'http://localhost:8000/api/workflow/langgraph/resume/{workflow_id}', json=resume_data, timeout=30)
                print(f'模板确认状态码: {resume_response.status_code}')
                
                if resume_response.status_code == 200:
                    resume_result = resume_response.json()
                    print(f'模板确认状态: {resume_result.get("status")}')
                    
                    # 显示模板确认后的状态
                    final_state = resume_result.get('data', {}).get('final_state', {})
                    print(f'确认后当前阶段: {final_state.get("current_stage", "unknown")}')
                    print(f'确认后下一步动作: {final_state.get("next_action", "unknown")}')
                    print(f'确认后需要用户操作: {resume_result.get("data", {}).get("requires_user_action", False)}')
                    
                    # 检查是否有新的人机交互数据
                    human_interaction = final_state.get('human_interaction')
                    if human_interaction:
                        print(f'新的人机交互类型: {human_interaction.get("type")}')
                        print(f'新的人机交互消息: {human_interaction.get("message", "")}')
                        
                        # 检查提取结果
                        interaction_data = human_interaction.get('data', {})
                        extraction_results = interaction_data.get('extraction_results', [])
                        print(f'新的提取结果数量: {len(extraction_results)}')
                        
                        if extraction_results:
                            print('新的前5个提取结果:')
                            for i, result in enumerate(extraction_results[:5]):
                                field_name = result.get('field_name', '')
                                field_value = result.get('field_value', '')
                                confidence = result.get('confidence', 0)
                                needs_input = result.get('needs_human_input', False)
                                method = result.get('extraction_method', 'unknown')
                                
                                print(f'  {i+1}. {field_name}: {field_value} (置信度: {confidence:.2f}, 需要输入: {needs_input}, 方法: {method})')
                    else:
                        print('没有新的人机交互数据')
                else:
                    print(f'模板确认失败: {resume_response.text}')
        else:
            print(f'启动失败: {response.text}')
    finally:
        if os.path.exists(test_file_path):
            os.unlink(test_file_path)

if __name__ == "__main__":
    debug_data_extraction()
