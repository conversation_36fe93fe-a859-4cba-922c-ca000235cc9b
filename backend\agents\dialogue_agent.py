"""
基础智能体 - 支持LangGraph持久化对话和人工干预
所有其他智能体都应该继承这个基类来获得对话持久化能力
"""

import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple, Union, TypedDict, Annotated
from enum import Enum
import operator

from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver
from langgraph.store.memory import InMemoryStore
from langgraph.prebuilt import create_react_agent
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage

from services.llm_service import llm_service, LLMResponse
from services.document_service import document_service
from services.database_checkpointer import database_checkpointer

# 配置日志
logger = logging.getLogger(__name__)


class AgentState(TypedDict):
    """LangGraph智能体状态"""
    messages: Annotated[List[BaseMessage], operator.add]
    user_id: Optional[str]
    agent_name: str
    current_stage: str
    context_data: Dict[str, Any]
    require_human_approval: bool
    human_feedback: Optional[Dict[str, Any]]
    extracted_data: Dict[str, Any]
    processing_status: str


class BaseDialogueAgent:
    """
    基础对话智能体类 - 支持LangGraph持久化
    所有其他智能体都应该继承这个基类来获得对话持久化能力
    """

    def __init__(
        self,
        agent_name: str = "BaseDialogueAgent",
        system_prompt: Optional[str] = None,
        checkpointer = None,
        store = None
    ):
        """
        初始化基础对话智能体

        Args:
            agent_name: 智能体名称
            system_prompt: 系统提示词
            checkpointer: LangGraph检查点保存器
            store: LangGraph内存存储器
        """
        self.agent_name = agent_name
        self.system_prompt = system_prompt or self._get_default_system_prompt()

        # LangGraph组件
        self.checkpointer = checkpointer or database_checkpointer
        self.store = store or InMemoryStore()

        # 构建图
        self.graph = self._build_graph()

    def _get_default_system_prompt(self) -> str:
        """获取默认系统提示词"""
        return """你是一个专业的AI助手，能够帮助用户处理文档、生成报告和回答问题。

你的能力包括：
1. 文档分析和信息提取
2. 报告生成和格式化
3. 问题回答和对话交流
4. 人工协作和反馈处理

请始终保持专业、友好和有帮助的态度。如果遇到不确定的情况，请主动寻求人工帮助。"""

    def _build_graph(self) -> StateGraph:
        """构建LangGraph工作流图"""
        workflow = StateGraph(AgentState)

        # 添加节点
        workflow.add_node("process_message", self._process_message_node)
        workflow.add_node("check_human_approval", self._check_human_approval_node)
        workflow.add_node("wait_for_human", self._wait_for_human_node)
        workflow.add_node("generate_response", self._generate_response_node)
        workflow.add_node("save_memory", self._save_memory_node)

        # 设置入口点
        workflow.add_edge(START, "process_message")

        # 添加条件边
        workflow.add_conditional_edges(
            "process_message",
            self._should_wait_for_human,
            {
                "wait": "wait_for_human",
                "continue": "generate_response"
            }
        )

        workflow.add_edge("wait_for_human", "check_human_approval")
        workflow.add_edge("check_human_approval", "generate_response")
        workflow.add_edge("generate_response", "save_memory")
        workflow.add_edge("save_memory", END)

        # 编译图
        return workflow.compile(
            checkpointer=self.checkpointer,
            store=self.store
        )
    
    def _process_message_node(self, state: AgentState) -> AgentState:
        """
        处理用户消息节点

        Args:
            state: 当前状态

        Returns:
            AgentState: 更新后的状态
        """
        # 获取最新的用户消息
        last_message = state["messages"][-1]
        if not isinstance(last_message, HumanMessage):
            # 如果最后一条消息不是用户消息，则不处理
            return state

        # 更新处理状态
        state["processing_status"] = "processing"
        state["current_stage"] = "process_message"

        # 记录到长期记忆（可选）
        user_id = state.get("user_id")
        if user_id and isinstance(last_message.content, str):
            # 将用户消息保存到长期记忆
            self._save_to_long_term_memory(user_id, last_message.content)

        return state

    def _should_wait_for_human(self, state: AgentState) -> str:
        """
        决定是否需要等待人工干预

        Args:
            state: 当前状态

        Returns:
            str: 路由决策 - "wait" 或 "continue"
        """
        if state.get("require_human_approval", False):
            return "wait"
        return "continue"

    def _wait_for_human_node(self, state: AgentState) -> AgentState:
        """
        等待人工干预节点

        Args:
            state: 当前状态

        Returns:
            AgentState: 更新后的状态
        """
        state["processing_status"] = "waiting_for_human"
        state["current_stage"] = "wait_for_human"

        # 这里可以添加通知逻辑，例如发送邮件或推送通知
        logger.info(f"等待人工干预: {state.get('user_id')}")

        return state

    def _check_human_approval_node(self, state: AgentState) -> AgentState:
        """
        检查人工审批节点

        Args:
            state: 当前状态

        Returns:
            AgentState: 更新后的状态
        """
        # 检查是否有人工反馈
        human_feedback = state.get("human_feedback")
        if human_feedback and isinstance(human_feedback, dict):
            # 根据反馈更新状态
            if human_feedback.get("approved", False):
                state["processing_status"] = "approved"
            else:
                state["processing_status"] = "rejected"

            # 清除人工反馈标记，避免重复处理
            state["require_human_approval"] = False

        state["current_stage"] = "check_human_approval"
        return state

    async def _generate_response_node(self, state: AgentState) -> AgentState:
        """
        生成AI响应节点

        Args:
            state: 当前状态

        Returns:
            AgentState: 更新后的状态
        """
        # 构建消息历史
        messages = []

        # 添加系统提示词
        messages.append(SystemMessage(content=self.system_prompt))

        # 添加对话历史
        messages.extend(state["messages"])

        try:
            # 调用LLM生成响应
            llm_messages = [
                {"role": msg.type if hasattr(msg, 'type') else "user", "content": msg.content}
                for msg in messages
            ]

            response = await llm_service.call_llm(llm_messages)

            if response.success:
                # 添加AI响应到消息历史
                ai_message = AIMessage(content=response.content)
                state["messages"].append(ai_message)
                state["processing_status"] = "completed"
            else:
                # 处理错误
                error_message = AIMessage(content="抱歉，我现在无法回答您的问题。请稍后再试。")
                state["messages"].append(error_message)
                state["processing_status"] = "error"
                logger.error(f"LLM调用失败: {response.error}")

        except Exception as e:
            error_message = AIMessage(content="抱歉，处理您的消息时出现了错误。请稍后再试。")
            state["messages"].append(error_message)
            state["processing_status"] = "error"
            logger.error(f"生成响应异常: {e}")

        state["current_stage"] = "generate_response"
        return state

    def _save_memory_node(self, state: AgentState) -> AgentState:
        """
        保存记忆节点

        Args:
            state: 当前状态

        Returns:
            AgentState: 更新后的状态
        """
        # 保存短期记忆（通过LangGraph的checkpointer自动处理）

        # 保存长期记忆
        if state.get("user_id") and len(state["messages"]) >= 2:
            try:
                # 获取最后的用户消息和AI响应
                user_msg = state["messages"][-2]
                ai_msg = state["messages"][-1]

                user_id = state.get("user_id")
                if (isinstance(user_msg, HumanMessage) and isinstance(ai_msg, AIMessage)
                    and user_id and isinstance(user_msg.content, str) and isinstance(ai_msg.content, str)):
                    self._save_to_long_term_memory(
                        user_id,
                        user_msg.content,
                        ai_msg.content
                    )
            except Exception as e:
                logger.error(f"保存长期记忆失败: {e}")

        state["current_stage"] = "save_memory"
        return state

    def _save_to_long_term_memory(
        self,
        user_id: str,
        user_message: str,
        ai_response: Optional[str] = None
    ):
        """
        保存到长期记忆

        Args:
            user_id: 用户ID
            user_message: 用户消息
            ai_response: AI响应（可选）
        """
        try:
            # 使用LangGraph的store保存长期记忆
            namespace = ("user_memory", user_id)
            memory_key = f"interaction_{datetime.utcnow().isoformat()}"

            memory_data = {
                "user_message": user_message,
                "ai_response": ai_response,
                "timestamp": datetime.utcnow().isoformat(),
                "agent_name": self.agent_name
            }

            self.store.put(namespace, memory_key, memory_data)
            logger.info(f"保存长期记忆: {user_id} - {memory_key}")

        except Exception as e:
            logger.error(f"保存长期记忆失败: {e}")

    def get_long_term_memory(self, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取长期记忆

        Args:
            user_id: 用户ID
            limit: 返回数量限制

        Returns:
            List[Dict[str, Any]]: 记忆列表
        """
        try:
            namespace = ("user_memory", user_id)
            memories = self.store.search(namespace, limit=limit)
            return [memory.value for memory in memories]
        except Exception as e:
            logger.error(f"获取长期记忆失败: {e}")
            return []

    # 主要接口方法 - 其他智能体可以继承使用

    async def chat(
        self,
        user_message: str,
        user_id: Optional[str] = None,
        thread_id: Optional[str] = None,
        require_human_approval: bool = False,
        context_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        主要聊天接口 - 支持持久化对话

        Args:
            user_message: 用户消息
            user_id: 用户ID
            thread_id: 线程ID（用于恢复对话）
            require_human_approval: 是否需要人工审批
            context_data: 上下文数据

        Returns:
            Dict[str, Any]: 响应结果
        """
        # 构建初始状态
        initial_state = AgentState(
            messages=[HumanMessage(content=user_message)],
            user_id=user_id,
            agent_name=self.agent_name,
            current_stage="start",
            context_data=context_data or {},
            require_human_approval=require_human_approval,
            human_feedback=None,
            extracted_data={},
            processing_status="pending"
        )

        # 配置
        config = {
            "configurable": {
                "thread_id": thread_id or str(uuid.uuid4())
            }
        }

        try:
            # 运行图
            result = await self.graph.ainvoke(initial_state, config)

            # 提取响应
            if result["messages"]:
                last_message = result["messages"][-1]
                if isinstance(last_message, AIMessage):
                    return {
                        "success": True,
                        "response": last_message.content,
                        "thread_id": config["configurable"]["thread_id"],
                        "status": result["processing_status"],
                        "requires_human_approval": result.get("require_human_approval", False),
                        "context_data": result.get("context_data", {})
                    }

            return {
                "success": False,
                "error": "No response generated",
                "thread_id": config["configurable"]["thread_id"]
            }

        except Exception as e:
            logger.error(f"聊天处理失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "thread_id": config["configurable"]["thread_id"]
            }

    async def provide_human_feedback(
        self,
        thread_id: str,
        feedback_content: str,
        approved: bool = True,
        feedback_metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        提供人工反馈

        Args:
            thread_id: 线程ID
            feedback_content: 反馈内容
            approved: 是否批准
            feedback_metadata: 反馈元数据

        Returns:
            Dict[str, Any]: 处理结果
        """
        config = {"configurable": {"thread_id": thread_id}}

        try:
            # 获取当前状态
            current_state = await self.graph.aget_state(config)

            if not current_state:
                return {
                    "success": False,
                    "error": "Thread not found"
                }

            # 更新状态以包含人工反馈
            updated_state = current_state.values.copy()
            updated_state["human_feedback"] = {
                "content": feedback_content,
                "approved": approved,
                "metadata": feedback_metadata or {},
                "timestamp": datetime.now().isoformat()
            }
            updated_state["require_human_approval"] = False

            # 继续执行图
            result = await self.graph.ainvoke(updated_state, config)

            return {
                "success": True,
                "message": "Human feedback processed",
                "status": result["processing_status"],
                "thread_id": thread_id
            }

        except Exception as e:
            logger.error(f"处理人工反馈失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "thread_id": thread_id
            }
    
    def get_thread_history(self, thread_id: str) -> List[BaseMessage]:
        """
        获取线程历史消息

        Args:
            thread_id: 线程ID

        Returns:
            List[BaseMessage]: 消息历史
        """
        try:
            config = {"configurable": {"thread_id": thread_id}}
            state = self.graph.get_state(config)

            if state and state.values:
                return state.values.get("messages", [])
            return []

        except Exception as e:
            logger.error(f"获取线程历史失败: {e}")
            return []

    def get_agent_status(self, thread_id: Optional[str] = None) -> Dict[str, Any]:
        """
        获取智能体状态

        Args:
            thread_id: 线程ID（可选）

        Returns:
            Dict[str, Any]: 状态信息
        """
        status = {
            "agent_name": self.agent_name,
            "timestamp": datetime.now().isoformat()
        }

        if thread_id:
            try:
                config = {"configurable": {"thread_id": thread_id}}
                state = self.graph.get_state(config)

                if state and state.values:
                    status.update({
                        "thread_id": thread_id,
                        "current_stage": state.values.get("current_stage", "unknown"),
                        "processing_status": state.values.get("processing_status", "unknown"),
                        "message_count": len(state.values.get("messages", [])),
                        "requires_human_approval": state.values.get("require_human_approval", False)
                    })
            except Exception as e:
                logger.error(f"获取线程状态失败: {e}")
                status["error"] = str(e)

        return status


# 示例：创建一个继承自BaseDialogueAgent的具体智能体
class DocumentProcessingAgent(BaseDialogueAgent):
    """文档处理智能体示例"""

    def __init__(self):
        system_prompt = """你是一个专业的文档处理助手。你的主要任务是：
1. 分析和理解用户上传的文档
2. 根据用户需求提取关键信息
3. 生成结构化的报告
4. 在不确定时主动寻求人工帮助

请始终保持专业和准确。"""

        super().__init__(
            agent_name="DocumentProcessingAgent",
            system_prompt=system_prompt
        )


# 全局基础对话智能体实例
base_dialogue_agent = BaseDialogueAgent("BaseDialogueAgent")
