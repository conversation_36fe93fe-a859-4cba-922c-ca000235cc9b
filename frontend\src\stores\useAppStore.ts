/**
 * 应用状态管理
 * 使用Zustand管理全局状态
 */

import { create } from 'zustand';
import type { TemplateField, WorkflowResult, UploadResult } from '../services/api';

interface AppState {
  // 当前步骤
  currentStep: number;
  setCurrentStep: (step: number) => void;

  // 上传的文件
  uploadedFile: UploadResult | null;
  setUploadedFile: (file: UploadResult | null) => void;

  // 模板字段
  templateFields: TemplateField[];
  setTemplateFields: (fields: TemplateField[]) => void;
  addTemplateField: (field: TemplateField) => void;
  removeTemplateField: (index: number) => void;
  updateTemplateField: (index: number, field: TemplateField) => void;

  // 报告模板
  reportTemplate: string;
  setReportTemplate: (template: string) => void;

  // 工作流结果
  workflowResult: WorkflowResult | null;
  setWorkflowResult: (result: WorkflowResult | null) => void;

  // 处理状态
  isProcessing: boolean;
  setIsProcessing: (processing: boolean) => void;

  // 错误信息
  error: string | null;
  setError: (error: string | null) => void;

  // 重置所有状态
  reset: () => void;
}

export const useAppStore = create<AppState>((set) => ({
  // 初始状态
  currentStep: 0,
  uploadedFile: null,
  templateFields: [],
  reportTemplate: '',
  workflowResult: null,
  isProcessing: false,
  error: null,

  // 设置当前步骤
  setCurrentStep: (step) => set({ currentStep: step }),

  // 设置上传文件
  setUploadedFile: (file) => set({ uploadedFile: file }),

  // 设置模板字段
  setTemplateFields: (fields) => set({ templateFields: fields }),

  // 添加模板字段
  addTemplateField: (field) =>
    set((state) => ({
      templateFields: [...state.templateFields, field],
    })),

  // 删除模板字段
  removeTemplateField: (index) =>
    set((state) => ({
      templateFields: state.templateFields.filter((_, i) => i !== index),
    })),

  // 更新模板字段
  updateTemplateField: (index, field) =>
    set((state) => ({
      templateFields: state.templateFields.map((f, i) => (i === index ? field : f)),
    })),

  // 设置报告模板
  setReportTemplate: (template) => set({ reportTemplate: template }),

  // 设置工作流结果
  setWorkflowResult: (result) => set({ workflowResult: result }),

  // 设置处理状态
  setIsProcessing: (processing) => set({ isProcessing: processing }),

  // 设置错误信息
  setError: (error) => set({ error }),

  // 重置所有状态
  reset: () =>
    set({
      currentStep: 0,
      uploadedFile: null,
      templateFields: [],
      reportTemplate: '',
      workflowResult: null,
      isProcessing: false,
      error: null,
    }),
}));

// 默认报告模板
export const DEFAULT_REPORT_TEMPLATE = `# 信息抽取报告

## 基本信息
- **文档类型**: {document_type}
- **处理时间**: {processing_time}
- **抽取字段数**: {field_count}

## 抽取结果

{extracted_fields}

## 质量评估
- **整体置信度**: {overall_confidence}
- **验证通过率**: {validation_rate}

## 备注
本报告由AI自动生成，请人工核验关键信息的准确性。

---
*生成时间: {generation_time}*
*系统版本: 超限报告智能生成工具 v1.0*`;

// 预设模板字段
export const PRESET_TEMPLATE_FIELDS = {
  meeting: [
    {
      field_name: '会议主题',
      description: '会议讨论的主要议题',
      example: '数字化转型项目进展讨论',
      format: '普通文本',
    },
    {
      field_name: '会议时间',
      description: '会议举行的具体时间',
      example: '2024年7月4日 14:00-16:00',
      format: '日期时间',
    },
    {
      field_name: '主持人',
      description: '会议的主持人姓名',
      example: '张总经理',
      format: '人名',
    },
    {
      field_name: '参会人数',
      description: '参加会议的总人数',
      example: '5人',
      format: '数字',
    },
  ],
  project: [
    {
      field_name: '项目名称',
      description: '项目的正式名称',
      example: '智能报告生成系统',
      format: '普通文本',
    },
    {
      field_name: '项目负责人',
      description: '项目的主要负责人姓名',
      example: '李明',
      format: '人名',
    },
    {
      field_name: '完成进度',
      description: '项目当前的完成百分比',
      example: '75%',
      format: '百分比',
    },
    {
      field_name: '预计完成时间',
      description: '项目预计的完成日期',
      example: '2024年8月31日',
      format: '日期',
    },
  ],
} as const;
